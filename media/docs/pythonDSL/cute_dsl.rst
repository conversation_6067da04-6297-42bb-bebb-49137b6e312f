.. _cute_dsl:

CuTe DSL
========

.. toctree::
  :maxdepth: 1

  Introduction <cute_dsl_general/dsl_introduction.rst>
  Code Generation <cute_dsl_general/dsl_code_generation.rst>
  Control Flow <cute_dsl_general/dsl_control_flow.rst>
  JIT Argument Generation <cute_dsl_general/dsl_jit_arg_generation.rst>
  JIT Argument: Layouts <cute_dsl_general/dsl_dynamic_layout.rst>
  JIT Caching <cute_dsl_general/dsl_jit_caching.rst>
  JIT Compilation Options <cute_dsl_general/dsl_jit_compilation_options.rst>
  Integration with Frameworks <cute_dsl_general/framework_integration.rst>
  Debugging with the DSL <cute_dsl_general/debugging.rst>
  Autotuning with the DSL <cute_dsl_general/autotuning_gemm.rst>
  Educational Notebooks <cute_dsl_general/notebooks.rst>
