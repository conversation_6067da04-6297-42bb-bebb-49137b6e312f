name: Feature Request
description: Suggest an idea for CUTLASS
title: "[FEA] "
labels: ["? - Needs Triage", "feature request"]
assignees: []

body:
  - type: dropdown
    id: component
    attributes:
      label: Which component requires the feature?
      options:
        - CuTe DSL
        - CUTLASS C++
    validations:
      required: true
  - type: textarea
    id: feature-request
    attributes:
      label: Feature Request
      description: Please fill out all sections below
      value: |
        **Is your feature request related to a problem? Please describe.**
        A clear and concise description of what the problem is. Ex. I wish I could use CUTLASS to do [...]

        **Describe the solution you'd like**
        A clear and concise description of what you want to happen.

        **Describe alternatives you've considered**
        A clear and concise description of any alternative solutions or features you've considered.

        **Additional context**
        Add any other context, code examples, or references to existing implementations about the feature request here.
    validations:
      required: true 