#################################################################################################
#
# Copyright (c) 2023 - 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: BSD-3-Clause
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#
# 1. Redistributions of source code must retain the above copyright notice, this
# list of conditions and the following disclaimer.
#
# 2. Redistributions in binary form must reproduce the above copyright notice,
# this list of conditions and the following disclaimer in the documentation
# and/or other materials provided with the distribution.
#
# 3. Neither the name of the copyright holder nor the names of its
# contributors may be used to endorse or promote products derived from
# this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
# AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
# IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
# FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
# DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
# SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
# CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
# OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
# OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
#
#################################################################################################

# A workflow to trigger ci on hybrid infra (github + self hosted runner)
name: Blossom-CI
on:
  issue_comment:
    types: [created]
  workflow_dispatch:
      inputs:
          platform:
            description: 'runs-on argument'
            required: false
          args:
            description: 'argument'
            required: false

jobs:
  Authorization:
    name: Authorization
    runs-on: blossom
    outputs:
      args: ${{ env.args }}

    # This job only runs for pull request comments
    if: |
        (startsWith(github.event.comment.body, '/bot run') ||
        startsWith(github.event.comment.body, '/bot kill')) && contains(
        fromJson('["zekunf-nv"]'),
        github.actor)
    steps:
      - name: Check if comment is issued by authorized person
        run: blossom-ci
        env:
          OPERATION: 'AUTH'
          REPO_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          REPO_KEY_DATA: ${{ secrets.BLOSSOM_KEY }}

  Vulnerability-scan:
    name: Vulnerability scan
    needs: [Authorization]
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v2
        with:
          repository: ${{ fromJson(needs.Authorization.outputs.args).repo }}
          ref: ${{ fromJson(needs.Authorization.outputs.args).ref }}
          lfs: 'true'

      - name: Run blossom action
        uses: NVIDIA/blossom-action@main
        env:
          REPO_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          REPO_KEY_DATA: ${{ secrets.BLOSSOM_KEY }}
        with:
          args1: ${{ fromJson(needs.Authorization.outputs.args).args1 }}
          args2: ${{ fromJson(needs.Authorization.outputs.args).args2 }}
          args3: ${{ fromJson(needs.Authorization.outputs.args).args3 }}

  Job-trigger:
    name: Start ci job
    needs: [Vulnerability-scan]
    runs-on: blossom
    steps:
      - name: Start ci job
        run: blossom-ci
        env:
          OPERATION: 'START-CI-JOB'
          CI_SERVER: ${{ secrets.CI_SERVER }}
          REPO_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  Upload-Log:
    name: Upload log
    runs-on: blossom
    if : github.event_name == 'workflow_dispatch'
    steps:
      - name: Jenkins log for pull request ${{ fromJson(github.event.inputs.args).pr }} (click here)
        run: blossom-ci
        env:
          OPERATION: 'POST-PROCESSING'
          CI_SERVER: ${{ secrets.CI_SERVER }}
          REPO_TOKEN: ${{ secrets.GITHUB_TOKEN }}
