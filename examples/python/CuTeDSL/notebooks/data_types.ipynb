{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from typing import List\n", "\n", "import cutlass\n", "import cutlass.cute as cute"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Understanding data structure in CuTe DSL\n", "\n", "In most cases, data structures in CuTe DSL work the same as Python data structures with the notable difference that Python data structures in most cases are considered as static data which are interpreted by the DSL compiler embedded inside Python interpreter.\n", "\n", "To differentiate between compile-time and runtime values, CuTe DSL introduces primitive types that \n", "represent dynamic values in JIT-compiled code.\n", "\n", "CuTe DSL provides a comprehensive set of primitive numeric types for representing dynamic values at \n", "runtime. These types are formally defined within the CuTe DSL typing system:\n", "\n", "### Integer Types\n", "- `Int8` - 8-bit signed integer\n", "- `Int16` - 16-bit signed integer  \n", "- `Int32` - 32-bit signed integer\n", "- `Int64` - 64-bit signed integer\n", "- `Int128` - 128-bit signed integer\n", "- `Uint8` - 8-bit unsigned integer\n", "- `Uint16` - 16-bit unsigned integer\n", "- `Uint32` - 32-bit unsigned integer\n", "- `Uint64` - 64-bit unsigned integer\n", "- `Uint128` - 128-bit unsigned integer\n", "\n", "### Floating Point Types\n", "- `Float16` - 16-bit floating point\n", "- `Float32` - 32-bit floating point \n", "- `Float64` - 64-bit floating point\n", "- `BFloat16` - Brain Floating Point format (16-bit)\n", "- `TFloat32` - Tensor Float32 format (reduced precision format used in tensor operations)\n", "- `Float8E4M3` - 8-bit floating point with 4-bit exponent and 3-bit mantissa\n", "- `Float8E5M2` - 8-bit floating point with 5-bit exponent and 2-bit mantissa\n", "\n", "These specialized types are designed to represent dynamic values in CuTe DSL code that will be \n", "evaluated at runtime, in contrast to Python's built-in numeric types which are evaluated during \n", "compilation.\n", "\n", "### Example usage:\n", "\n", "```python\n", "x = cutlass.Int32(5)        # Creates a 32-bit integer\n", "y = cutlass.Float32(3.14)   # Creates a 32-bit float\n", "\n", "@cute.jit\n", "def foo(a: cutlass.Int32):  # annotate `a` as 32-bit integer passed to jit function via ABI\n", "    ...\n", "```\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["a(static) = ?\n", "b(static) = ?\n", "a(dynamic) = 3.140000\n", "b(dynamic) = 5\n"]}], "source": ["@cute.jit\n", "def bar():\n", "    a = cutlass.Float32(3.14)\n", "    print(\"a(static) =\", a)             # prints `a(static) = ?`\n", "    cute.printf(\"a(dynamic) = {}\", a)   # prints `a(dynamic) = 3.140000`\n", "\n", "    b = cutlass.Int32(5)\n", "    print(\"b(static) =\", b)             # prints `b(static) = 5`\n", "    cute.printf(\"b(dynamic) = {}\", b)   # prints `b(dynamic) = 5`\n", "\n", "bar()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Type Conversion API\n", "\n", "CUTLASS numeric types provide type conversion through the `to()` method available on all Numeric types. This allows you to convert between different numeric data types at runtime.\n", "\n", "Syntax:\n", "\n", "```python\n", "new_value = value.to(target_type)\n", "```\n", "\n", "The `to()` method supports conversion between:\n", "- Integer types (Int8, Int16, Int32, Int64, UInt8, UInt16, UInt32, UInt64)\n", "- Floating point types (Float16, Float32, Float64, BFloat16)\n", "- Mixed integer/floating point conversions\n", "\n", "Note that when converting from floating point to integer types, the decimal portion is truncated. When converting between types with different ranges, values may be clamped or lose precision if they exceed the target type's representable range."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Int32(42) => Float32(42.000000)\n", "Float32(3.140000) => Int32(3)\n", "Int32(127) => Int8(127)\n", "Int32(300) => Int8(44) (truncated due to range limitation)\n"]}], "source": ["@cute.jit\n", "def type_conversion():\n", "    # Convert from Int32 to Float32\n", "    x = cutlass.Int32(42)\n", "    y = x.to(cutlass.Float32)\n", "    cute.printf(\"Int32({}) => Float32({})\", x, y)\n", "\n", "    # Convert from Float32 to Int32\n", "    a = cutlass.Float32(3.14)\n", "    b = a.to(cutlass.Int32)\n", "    cute.printf(\"Float32({}) => Int32({})\", a, b)\n", "\n", "    # Convert from Int32 to Int8\n", "    c = cutlass.Int32(127)\n", "    d = c.to(cutlass.Int8)\n", "    cute.printf(\"Int32({}) => Int8({})\", c, d)\n", "\n", "    # Convert from Int32 to Int8 with value exceeding Int8 range\n", "    e = cutlass.Int32(300)\n", "    f = e.to(cutlass.Int8)\n", "    cute.printf(\"Int32({}) => Int8({}) (truncated due to range limitation)\", e, f)\n", "\n", "type_conversion()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Operator Overloading\n", "\n", "CUTLASS numeric types support Python's built-in operators, allowing you to write natural mathematical expressions. The operators work with both CUTLASS numeric types and Python native numeric types.\n", "\n", "Supported operators include:\n", "- Arithmetic: `+`, `-`, `*`, `/`, `//`, `%`, `**`\n", "- Comparison: `<`, `<=`, `==`, `!=`, `>=`, `>`\n", "- Bitwise: `&`, `|`, `^`, `<<`, `>>`\n", "- Unary: `-` (negation), `~` (bitwise NOT)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["a: Int32(10), b: Int32(3)\n", "x: Float32(5.500000)\n", "\n", "a + b = 13\n", "x * 2 = 11.000000\n", "a + x = 15.500000 (Int32 + Float32 promotes to Float32)\n", "a / b = 3.333333\n", "x / 2.0 = 2.750000\n", "a > b = 1\n", "a & b = 2\n", "-a = -10\n", "~a = -11\n"]}], "source": ["@cute.jit\n", "def operator_demo():\n", "    # Arithmetic operators\n", "    a = cutlass.Int32(10)\n", "    b = cutlass.Int32(3)\n", "    cute.printf(\"a: Int32({}), b: Int32({})\", a, b)\n", "\n", "    x = cutlass.Float32(5.5)\n", "    cute.printf(\"x: Float32({})\", x)\n", "\n", "    cute.printf(\"\")\n", "\n", "    sum_result = a + b\n", "    cute.printf(\"a + b = {}\", sum_result)\n", "\n", "    y = x * 2  # Multiplying with Python native type\n", "    cute.printf(\"x * 2 = {}\", y)\n", "\n", "    # Mixed type arithmetic (Int32 + Float32) that integer is converted into float32\n", "    mixed_result = a + x\n", "    cute.printf(\"a + x = {} (Int32 + Float32 promotes to Float32)\", mixed_result)\n", "\n", "    # Division with Int32 (note: integer division)\n", "    div_result = a / b\n", "    cute.printf(\"a / b = {}\", div_result)\n", "\n", "    # Float division\n", "    float_div = x / cutlass.Float32(2.0)\n", "    cute.printf(\"x / 2.0 = {}\", float_div)\n", "\n", "    # Comparison operators\n", "    is_greater = a > b\n", "    cute.printf(\"a > b = {}\", is_greater)\n", "\n", "    # Bitwise operators\n", "    bit_and = a & b\n", "    cute.printf(\"a & b = {}\", bit_and)\n", "\n", "    neg_a = -a\n", "    cute.printf(\"-a = {}\", neg_a)\n", "\n", "    not_a = ~a\n", "    cute.printf(\"~a = {}\", not_a)\n", "\n", "operator_demo()\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 4}