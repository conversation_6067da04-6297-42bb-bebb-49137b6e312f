<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: matrix.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_ed1948a6da781e7f72c597b5619a522d.html">thread</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">thread/matrix.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="thread_2matrix_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2017-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> **************************************************************************************************/</span></div><div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;</div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="cutlass_8h.html">cutlass/cutlass.h</a>&quot;</span></div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="array_8h.html">cutlass/array.h</a>&quot;</span></div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="matrix__coord_8h.html">cutlass/matrix_coord.h</a>&quot;</span></div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;</div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00036"></a><span class="lineno"><a class="line" href="namespacecutlass_1_1thread.html">   36</a></span>&#160;<span class="keyword">namespace </span>thread {</div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;</div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;</div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;  <span class="keyword">typename</span> Element,</div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;  <span class="keywordtype">int</span> Rows,</div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;  <span class="keywordtype">int</span> Columns,</div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;  <span class="keyword">typename</span> Layout = <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a></div><div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;&gt;</div><div class="line"><a name="l00047"></a><span class="lineno"><a class="line" href="classcutlass_1_1thread_1_1Matrix.html">   47</a></span>&#160;<span class="keyword">class </span><a class="code" href="classcutlass_1_1thread_1_1Matrix.html">Matrix</a> : <span class="keyword">public</span> Array&lt;Element, Rows * Columns&gt; {</div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;  </div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;  <span class="comment">// Verify layout refers to a rank=2 matrix.</span></div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;  <a class="code" href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a>(</div><div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;    Layout::kRank == 2,</div><div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;    <span class="stringliteral">&quot;Layout type must refer to a rank=2 matrix&quot;</span>);</div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;</div><div class="line"><a name="l00056"></a><span class="lineno"><a class="line" href="classcutlass_1_1thread_1_1Matrix.html#a2abcd8b9b18c88f3e60941b4ca315c25">   56</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a2abcd8b9b18c88f3e60941b4ca315c25">Base</a> = Array&lt;Element, Rows * Columns&gt;;</div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;</div><div class="line"><a name="l00059"></a><span class="lineno"><a class="line" href="classcutlass_1_1thread_1_1Matrix.html#a396f92d03a869d50680357c866a1a6f9">   59</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a396f92d03a869d50680357c866a1a6f9">Element</a> = Element_;</div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;</div><div class="line"><a name="l00062"></a><span class="lineno"><a class="line" href="classcutlass_1_1thread_1_1Matrix.html#a808e73d767cc4e248cfba54a42b0a41d">   62</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a808e73d767cc4e248cfba54a42b0a41d">kRows</a> = Rows;</div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;</div><div class="line"><a name="l00065"></a><span class="lineno"><a class="line" href="classcutlass_1_1thread_1_1Matrix.html#ad13ab25b0e147387c7868fbde3dd0436">   65</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#ad13ab25b0e147387c7868fbde3dd0436">kColumns</a> = Columns;</div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;</div><div class="line"><a name="l00068"></a><span class="lineno"><a class="line" href="classcutlass_1_1thread_1_1Matrix.html#ac4212d7ab194569009ef64699101d3a9">   68</a></span>&#160;  <span class="keyword">using</span> Layout = Layout_;</div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;</div><div class="line"><a name="l00071"></a><span class="lineno"><a class="line" href="classcutlass_1_1thread_1_1Matrix.html#a3c9535ffa08c91d0040b09667c81b201">   71</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a3c9535ffa08c91d0040b09667c81b201">Reference</a> = <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a396f92d03a869d50680357c866a1a6f9">Element</a> &amp;;</div><div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;</div><div class="line"><a name="l00074"></a><span class="lineno"><a class="line" href="classcutlass_1_1thread_1_1Matrix.html#a20cc1aac0aa311bb952cf0b491d3b185">   74</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a20cc1aac0aa311bb952cf0b491d3b185">kRank</a> = 2;</div><div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;</div><div class="line"><a name="l00077"></a><span class="lineno"><a class="line" href="classcutlass_1_1thread_1_1Matrix.html#ae131725fd99cdd6e028ed3cde45d97b8">   77</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#ae131725fd99cdd6e028ed3cde45d97b8">Index</a> = <span class="keyword">typename</span> Layout::Index;</div><div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;</div><div class="line"><a name="l00080"></a><span class="lineno"><a class="line" href="classcutlass_1_1thread_1_1Matrix.html#aa1ba37a162d1844a125d5659b3d72fc8">   80</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#aa1ba37a162d1844a125d5659b3d72fc8">LongIndex</a> = <span class="keyword">typename</span> Layout::LongIndex;</div><div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;</div><div class="line"><a name="l00083"></a><span class="lineno"><a class="line" href="classcutlass_1_1thread_1_1Matrix.html#a3f88201079800ed3e3e38a62a018638e">   83</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a3f88201079800ed3e3e38a62a018638e">TensorCoord</a> = <span class="keyword">typename</span> Layout::TensorCoord;</div><div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;</div><div class="line"><a name="l00086"></a><span class="lineno"><a class="line" href="classcutlass_1_1thread_1_1Matrix.html#a2d69a5beadf3c092a8a9e09b53a1167d">   86</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a2d69a5beadf3c092a8a9e09b53a1167d">Stride</a> = <span class="keyword">typename</span> Layout::Stride;</div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;</div><div class="line"><a name="l00089"></a><span class="lineno"><a class="line" href="classcutlass_1_1thread_1_1Matrix.html#a51c2c4446bb887fdc18639642a4a1190">   89</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a51c2c4446bb887fdc18639642a4a1190">TensorRef</a> = <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a51c2c4446bb887fdc18639642a4a1190">TensorRef&lt;Element, kRank, Layout&gt;</a>;</div><div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;</div><div class="line"><a name="l00092"></a><span class="lineno"><a class="line" href="classcutlass_1_1thread_1_1Matrix.html#a5cae9543ffd1f2e722943b53fa5486b9">   92</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a5cae9543ffd1f2e722943b53fa5486b9">ConstTensorRef</a> = <span class="keyword">typename</span> <a class="code" href="classcutlass_1_1TensorRef.html#ad3c5c9466713f62a5191e720827f34da">TensorRef::ConstTensorRef</a>;</div><div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;</div><div class="line"><a name="l00095"></a><span class="lineno"><a class="line" href="classcutlass_1_1thread_1_1Matrix.html#a99ef3b56c9beaa9f1939f7de29b2e753">   95</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a99ef3b56c9beaa9f1939f7de29b2e753">TensorView</a> = <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a99ef3b56c9beaa9f1939f7de29b2e753">TensorView&lt;Element, kRank, Layout&gt;</a>;</div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;</div><div class="line"><a name="l00098"></a><span class="lineno"><a class="line" href="classcutlass_1_1thread_1_1Matrix.html#a24979d95b579648b9871db63ba9f7c6b">   98</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a24979d95b579648b9871db63ba9f7c6b">ConstTensorView</a> = <span class="keyword">typename</span> <a class="code" href="classcutlass_1_1TensorView.html#a71def6d54ed28dfe3b17fde3e6461578">TensorView::ConstTensorView</a>;</div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;</div><div class="line"><a name="l00101"></a><span class="lineno"><a class="line" href="classcutlass_1_1thread_1_1Matrix.html#a7f68cf22835050f9c77df48750e278f2">  101</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a7f68cf22835050f9c77df48750e278f2">Diagonal</a> = Vector&lt;Element, __NV_STD_MIN(kRows, kColumns)&gt;;</div><div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;</div><div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;</div><div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;</div><div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;</div><div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;  <span class="comment">// Methods</span></div><div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;</div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00114"></a><span class="lineno"><a class="line" href="classcutlass_1_1thread_1_1Matrix.html#a3b19b3b7ceea1bcefe22155eb28643e8">  114</a></span>&#160;  <span class="keyword">static</span> <a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a> <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a3b19b3b7ceea1bcefe22155eb28643e8">extent</a>() {</div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="namespacecutlass.html#a7419519fa453a121dfa5f26bf87318d9">make_Coord</a>(kRows, kColumns);</div><div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;  }</div><div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;</div><div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00120"></a><span class="lineno"><a class="line" href="classcutlass_1_1thread_1_1Matrix.html#a23134a82ceae2601f3d6694a02271877">  120</a></span>&#160;  <span class="keyword">static</span> Layout <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a23134a82ceae2601f3d6694a02271877">layout</a>() {</div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;    <span class="keywordflow">return</span> Layout::packed(<a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a3b19b3b7ceea1bcefe22155eb28643e8">extent</a>());</div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;  }</div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;</div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00126"></a><span class="lineno"><a class="line" href="classcutlass_1_1thread_1_1Matrix.html#a96d351d1686a79eececdc4ee2e6b886e">  126</a></span>&#160;  <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a96d351d1686a79eececdc4ee2e6b886e">Matrix</a>() { }</div><div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;</div><div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00130"></a><span class="lineno"><a class="line" href="classcutlass_1_1thread_1_1Matrix.html#a8a5afcd352fd76b6c3062372084437ca">  130</a></span>&#160;  <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a8a5afcd352fd76b6c3062372084437ca">Matrix</a>(<a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a7f68cf22835050f9c77df48750e278f2">Diagonal</a> <span class="keyword">const</span> &amp;diag) {</div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;    <span class="comment">// Todo - construct from diagonal</span></div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;  }</div><div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;</div><div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00136"></a><span class="lineno"><a class="line" href="classcutlass_1_1thread_1_1Matrix.html#a21e0504879de0558718e05977a7b8a73">  136</a></span>&#160;  <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a51c2c4446bb887fdc18639642a4a1190">TensorRef</a> <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a21e0504879de0558718e05977a7b8a73">ref</a>() {</div><div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a51c2c4446bb887fdc18639642a4a1190">TensorRef</a>(this-&gt;data(), <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a23134a82ceae2601f3d6694a02271877">layout</a>());</div><div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;  }</div><div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;</div><div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00142"></a><span class="lineno"><a class="line" href="classcutlass_1_1thread_1_1Matrix.html#a850e9e43797c386ffbdec398d1c1b559">  142</a></span>&#160;  <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a5cae9543ffd1f2e722943b53fa5486b9">ConstTensorRef</a> <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a850e9e43797c386ffbdec398d1c1b559">const_ref</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a5cae9543ffd1f2e722943b53fa5486b9">ConstTensorRef</a>(this-&gt;data(), <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a23134a82ceae2601f3d6694a02271877">layout</a>());</div><div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;  }</div><div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;</div><div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00148"></a><span class="lineno"><a class="line" href="classcutlass_1_1thread_1_1Matrix.html#a7c727fa4d536a9e82957b5a3f4ae1c92">  148</a></span>&#160;  <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a99ef3b56c9beaa9f1939f7de29b2e753">TensorView</a> <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a7c727fa4d536a9e82957b5a3f4ae1c92">view</a>() {</div><div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a99ef3b56c9beaa9f1939f7de29b2e753">TensorView</a>(<a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a21e0504879de0558718e05977a7b8a73">ref</a>(), <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a3b19b3b7ceea1bcefe22155eb28643e8">extent</a>());</div><div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;  }</div><div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;</div><div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00154"></a><span class="lineno"><a class="line" href="classcutlass_1_1thread_1_1Matrix.html#a65a4c5ff99fd0a7dbee722c9e04fac35">  154</a></span>&#160;  <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a24979d95b579648b9871db63ba9f7c6b">ConstTensorView</a> <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a65a4c5ff99fd0a7dbee722c9e04fac35">const_view</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a24979d95b579648b9871db63ba9f7c6b">ConstTensorView</a>(<a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a850e9e43797c386ffbdec398d1c1b559">const_ref</a>(), <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a3b19b3b7ceea1bcefe22155eb28643e8">extent</a>());</div><div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;  }</div><div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;</div><div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00160"></a><span class="lineno"><a class="line" href="classcutlass_1_1thread_1_1Matrix.html#a2756a0c0eb2efcce5033fb175855755e">  160</a></span>&#160;  <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a3c9535ffa08c91d0040b09667c81b201">Reference</a> <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a2756a0c0eb2efcce5033fb175855755e">at</a>(<a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a> <span class="keyword">const</span>&amp; coord)<span class="keyword"> const </span>{</div><div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;    <span class="keyword">typename</span> Base::size_type offset_(<a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a23134a82ceae2601f3d6694a02271877">layout</a>().offset(coord));</div><div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;    <span class="keywordflow">return</span> Base::at(offset_);</div><div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;  }</div><div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;</div><div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00167"></a><span class="lineno"><a class="line" href="classcutlass_1_1thread_1_1Matrix.html#a6c737c01701bb00f0d47ffdf847fcb6d">  167</a></span>&#160;  <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#aa1ba37a162d1844a125d5659b3d72fc8">LongIndex</a> <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a6c737c01701bb00f0d47ffdf847fcb6d">capacity</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#aa1ba37a162d1844a125d5659b3d72fc8">LongIndex</a>(Base::size());</div><div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;  }</div><div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;};</div><div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;</div><div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;</div><div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;  <span class="keyword">typename</span> <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a396f92d03a869d50680357c866a1a6f9">Element</a>,</div><div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;  <span class="keywordtype">int</span> Rows,</div><div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;  <span class="keyword">typename</span> Layout = <a class="code" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a></div><div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;&gt;</div><div class="line"><a name="l00180"></a><span class="lineno"><a class="line" href="namespacecutlass_1_1thread.html#a1a7bcc895cfbd560c476b74bd6eb60bc">  180</a></span>&#160;<span class="keyword">using</span> <a class="code" href="classcutlass_1_1thread_1_1Matrix.html">ColumnVector</a> = <a class="code" href="classcutlass_1_1thread_1_1Matrix.html">Matrix&lt;Element, Rows, 1, Layout&gt;</a>;</div><div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;</div><div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;  <span class="keyword">typename</span> <a class="code" href="classcutlass_1_1thread_1_1Matrix.html#a396f92d03a869d50680357c866a1a6f9">Element</a>,</div><div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;  <span class="keywordtype">int</span> Columns,</div><div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;  <span class="keyword">typename</span> Layout = <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a></div><div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;&gt;</div><div class="line"><a name="l00188"></a><span class="lineno"><a class="line" href="namespacecutlass_1_1thread.html#a4a8bdc0c4e09b113284e07228704f98a">  188</a></span>&#160;<span class="keyword">using</span> <a class="code" href="classcutlass_1_1thread_1_1Matrix.html">RowVector</a> = <a class="code" href="classcutlass_1_1thread_1_1Matrix.html">Matrix&lt;Element, 1, Columns, Layout&gt;</a>;</div><div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;</div><div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;</div><div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;} <span class="comment">// namespace thread</span></div><div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;} <span class="comment">// namespace cutlass</span></div><div class="ttc" id="classcutlass_1_1thread_1_1Matrix_html_a3f88201079800ed3e3e38a62a018638e"><div class="ttname"><a href="classcutlass_1_1thread_1_1Matrix.html#a3f88201079800ed3e3e38a62a018638e">cutlass::thread::Matrix::TensorCoord</a></div><div class="ttdeci">typename Layout::TensorCoord TensorCoord</div><div class="ttdoc">Coordinate in logical tensor space. </div><div class="ttdef"><b>Definition:</b> thread/matrix.h:83</div></div>
<div class="ttc" id="classcutlass_1_1thread_1_1Matrix_html"><div class="ttname"><a href="classcutlass_1_1thread_1_1Matrix.html">cutlass::thread::Matrix</a></div><div class="ttdoc">Per-thread matrix object storing a packed matrix. </div><div class="ttdef"><b>Definition:</b> thread/matrix.h:47</div></div>
<div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="classcutlass_1_1thread_1_1Matrix_html_a2d69a5beadf3c092a8a9e09b53a1167d"><div class="ttname"><a href="classcutlass_1_1thread_1_1Matrix.html#a2d69a5beadf3c092a8a9e09b53a1167d">cutlass::thread::Matrix::Stride</a></div><div class="ttdeci">typename Layout::Stride Stride</div><div class="ttdoc">Stride type. </div><div class="ttdef"><b>Definition:</b> thread/matrix.h:86</div></div>
<div class="ttc" id="namespacecutlass_html_a7419519fa453a121dfa5f26bf87318d9"><div class="ttname"><a href="namespacecutlass.html#a7419519fa453a121dfa5f26bf87318d9">cutlass::make_Coord</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Coord&lt; 1 &gt; make_Coord(int _0)</div><div class="ttdoc">Helper to make a 2-element coordinate. </div><div class="ttdef"><b>Definition:</b> coord.h:387</div></div>
<div class="ttc" id="classcutlass_1_1thread_1_1Matrix_html_a2abcd8b9b18c88f3e60941b4ca315c25"><div class="ttname"><a href="classcutlass_1_1thread_1_1Matrix.html#a2abcd8b9b18c88f3e60941b4ca315c25">cutlass::thread::Matrix::Base</a></div><div class="ttdeci">Array&lt; Element, Rows *Columns &gt; Base</div><div class="ttdoc">Base type. </div><div class="ttdef"><b>Definition:</b> thread/matrix.h:56</div></div>
<div class="ttc" id="classcutlass_1_1thread_1_1Matrix_html_a21e0504879de0558718e05977a7b8a73"><div class="ttname"><a href="classcutlass_1_1thread_1_1Matrix.html#a21e0504879de0558718e05977a7b8a73">cutlass::thread::Matrix::ref</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorRef ref()</div><div class="ttdoc">Returns a TensorRef pointing to the first element of the tensor. </div><div class="ttdef"><b>Definition:</b> thread/matrix.h:136</div></div>
<div class="ttc" id="classcutlass_1_1thread_1_1Matrix_html_a7f68cf22835050f9c77df48750e278f2"><div class="ttname"><a href="classcutlass_1_1thread_1_1Matrix.html#a7f68cf22835050f9c77df48750e278f2">cutlass::thread::Matrix::Diagonal</a></div><div class="ttdeci">Vector&lt; Element, __NV_STD_MIN(kRows, kColumns)&gt; Diagonal</div><div class="ttdoc">Diagonal vector. </div><div class="ttdef"><b>Definition:</b> thread/matrix.h:101</div></div>
<div class="ttc" id="classcutlass_1_1thread_1_1Matrix_html_a3b19b3b7ceea1bcefe22155eb28643e8"><div class="ttname"><a href="classcutlass_1_1thread_1_1Matrix.html#a3b19b3b7ceea1bcefe22155eb28643e8">cutlass::thread::Matrix::extent</a></div><div class="ttdeci">static CUTLASS_HOST_DEVICE MatrixCoord extent()</div><div class="ttdoc">Returns the size of the object. </div><div class="ttdef"><b>Definition:</b> thread/matrix.h:114</div></div>
<div class="ttc" id="classcutlass_1_1thread_1_1Matrix_html_a808e73d767cc4e248cfba54a42b0a41d"><div class="ttname"><a href="classcutlass_1_1thread_1_1Matrix.html#a808e73d767cc4e248cfba54a42b0a41d">cutlass::thread::Matrix::kRows</a></div><div class="ttdeci">static int const kRows</div><div class="ttdoc">Number of rows. </div><div class="ttdef"><b>Definition:</b> thread/matrix.h:62</div></div>
<div class="ttc" id="classcutlass_1_1thread_1_1Matrix_html_a6c737c01701bb00f0d47ffdf847fcb6d"><div class="ttname"><a href="classcutlass_1_1thread_1_1Matrix.html#a6c737c01701bb00f0d47ffdf847fcb6d">cutlass::thread::Matrix::capacity</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex capacity() const </div><div class="ttdoc">Returns the number of scalar elements needed to store tensor. </div><div class="ttdef"><b>Definition:</b> thread/matrix.h:167</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_ad3c5c9466713f62a5191e720827f34da"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#ad3c5c9466713f62a5191e720827f34da">cutlass::TensorRef::ConstTensorRef</a></div><div class="ttdeci">TensorRef&lt; typename platform::remove_const&lt; Element &gt;::type const, Layout &gt; ConstTensorRef</div><div class="ttdoc">TensorRef to constant data. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:179</div></div>
<div class="ttc" id="classcutlass_1_1thread_1_1Matrix_html_a5cae9543ffd1f2e722943b53fa5486b9"><div class="ttname"><a href="classcutlass_1_1thread_1_1Matrix.html#a5cae9543ffd1f2e722943b53fa5486b9">cutlass::thread::Matrix::ConstTensorRef</a></div><div class="ttdeci">typename TensorRef::ConstTensorRef ConstTensorRef</div><div class="ttdoc">TensorRef to constant matrix object. </div><div class="ttdef"><b>Definition:</b> thread/matrix.h:92</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1ColumnMajor_html"><div class="ttname"><a href="classcutlass_1_1layout_1_1ColumnMajor.html">cutlass::layout::ColumnMajor</a></div><div class="ttdoc">Mapping function for column-major matrices. </div><div class="ttdef"><b>Definition:</b> layout/matrix.h:142</div></div>
<div class="ttc" id="classcutlass_1_1thread_1_1Matrix_html_a99ef3b56c9beaa9f1939f7de29b2e753"><div class="ttname"><a href="classcutlass_1_1thread_1_1Matrix.html#a99ef3b56c9beaa9f1939f7de29b2e753">cutlass::thread::Matrix::TensorView</a></div><div class="ttdeci">TensorView&lt; Element, kRank, Layout &gt; TensorView</div><div class="ttdoc">TensorRef to matrix object. </div><div class="ttdef"><b>Definition:</b> thread/matrix.h:95</div></div>
<div class="ttc" id="classcutlass_1_1thread_1_1Matrix_html_a7c727fa4d536a9e82957b5a3f4ae1c92"><div class="ttname"><a href="classcutlass_1_1thread_1_1Matrix.html#a7c727fa4d536a9e82957b5a3f4ae1c92">cutlass::thread::Matrix::view</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorView view()</div><div class="ttdoc">Returns a TensorRef pointing to the first element of the tensor. </div><div class="ttdef"><b>Definition:</b> thread/matrix.h:148</div></div>
<div class="ttc" id="array_8h_html"><div class="ttname"><a href="array_8h.html">array.h</a></div><div class="ttdoc">Statically sized array of elements that accommodates all CUTLASS-supported numeric types and is safe ...</div></div>
<div class="ttc" id="classcutlass_1_1TensorView_html_a71def6d54ed28dfe3b17fde3e6461578"><div class="ttname"><a href="classcutlass_1_1TensorView.html#a71def6d54ed28dfe3b17fde3e6461578">cutlass::TensorView::ConstTensorView</a></div><div class="ttdeci">TensorView&lt; typename platform::remove_const&lt; Element &gt;::type const, Layout &gt; ConstTensorView</div><div class="ttdoc">TensorView pointing to constant memory. </div><div class="ttdef"><b>Definition:</b> tensor_view.h:95</div></div>
<div class="ttc" id="classcutlass_1_1thread_1_1Matrix_html_a2756a0c0eb2efcce5033fb175855755e"><div class="ttname"><a href="classcutlass_1_1thread_1_1Matrix.html#a2756a0c0eb2efcce5033fb175855755e">cutlass::thread::Matrix::at</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Reference at(MatrixCoord const &amp;coord) const </div><div class="ttdoc">Returns a reference to the element at a given Coord. </div><div class="ttdef"><b>Definition:</b> thread/matrix.h:160</div></div>
<div class="ttc" id="classcutlass_1_1thread_1_1Matrix_html_a51c2c4446bb887fdc18639642a4a1190"><div class="ttname"><a href="classcutlass_1_1thread_1_1Matrix.html#a51c2c4446bb887fdc18639642a4a1190">cutlass::thread::Matrix::TensorRef</a></div><div class="ttdeci">TensorRef&lt; Element, kRank, Layout &gt; TensorRef</div><div class="ttdoc">TensorRef to matrix object. </div><div class="ttdef"><b>Definition:</b> thread/matrix.h:89</div></div>
<div class="ttc" id="classcutlass_1_1thread_1_1Matrix_html_ae131725fd99cdd6e028ed3cde45d97b8"><div class="ttname"><a href="classcutlass_1_1thread_1_1Matrix.html#ae131725fd99cdd6e028ed3cde45d97b8">cutlass::thread::Matrix::Index</a></div><div class="ttdeci">typename Layout::Index Index</div><div class="ttdoc">Index type. </div><div class="ttdef"><b>Definition:</b> thread/matrix.h:77</div></div>
<div class="ttc" id="classcutlass_1_1thread_1_1Matrix_html_a24979d95b579648b9871db63ba9f7c6b"><div class="ttname"><a href="classcutlass_1_1thread_1_1Matrix.html#a24979d95b579648b9871db63ba9f7c6b">cutlass::thread::Matrix::ConstTensorView</a></div><div class="ttdeci">typename TensorView::ConstTensorView ConstTensorView</div><div class="ttdoc">TensorRef to constant matrix object. </div><div class="ttdef"><b>Definition:</b> thread/matrix.h:98</div></div>
<div class="ttc" id="classcutlass_1_1thread_1_1Matrix_html_aa1ba37a162d1844a125d5659b3d72fc8"><div class="ttname"><a href="classcutlass_1_1thread_1_1Matrix.html#aa1ba37a162d1844a125d5659b3d72fc8">cutlass::thread::Matrix::LongIndex</a></div><div class="ttdeci">typename Layout::LongIndex LongIndex</div><div class="ttdoc">Long index used for pointer offsets. </div><div class="ttdef"><b>Definition:</b> thread/matrix.h:80</div></div>
<div class="ttc" id="classcutlass_1_1thread_1_1Matrix_html_a396f92d03a869d50680357c866a1a6f9"><div class="ttname"><a href="classcutlass_1_1thread_1_1Matrix.html#a396f92d03a869d50680357c866a1a6f9">cutlass::thread::Matrix::Element</a></div><div class="ttdeci">Element_ Element</div><div class="ttdoc">Element type. </div><div class="ttdef"><b>Definition:</b> thread/matrix.h:59</div></div>
<div class="ttc" id="cutlass_8h_html_a28c2443a142676d3d71effdae1a986b1"><div class="ttname"><a href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="ttdeci">#define CUTLASS_HOST_DEVICE</div><div class="ttdef"><b>Definition:</b> cutlass.h:89</div></div>
<div class="ttc" id="platform_8h_html_adde4c9ea91b753491851361a4198c009"><div class="ttname"><a href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a></div><div class="ttdeci">#define static_assert(__e, __m)</div><div class="ttdef"><b>Definition:</b> platform.h:153</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1RowMajor_html"><div class="ttname"><a href="classcutlass_1_1layout_1_1RowMajor.html">cutlass::layout::RowMajor</a></div><div class="ttdoc">Mapping function for row-major matrices. </div><div class="ttdef"><b>Definition:</b> layout/matrix.h:50</div></div>
<div class="ttc" id="classcutlass_1_1thread_1_1Matrix_html_a96d351d1686a79eececdc4ee2e6b886e"><div class="ttname"><a href="classcutlass_1_1thread_1_1Matrix.html#a96d351d1686a79eececdc4ee2e6b886e">cutlass::thread::Matrix::Matrix</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Matrix()</div><div class="ttdoc">Ctor. </div><div class="ttdef"><b>Definition:</b> thread/matrix.h:126</div></div>
<div class="ttc" id="matrix__coord_8h_html"><div class="ttname"><a href="matrix__coord_8h.html">matrix_coord.h</a></div><div class="ttdoc">Defines a canonical coordinate for rank=2 matrices offering named indices. </div></div>
<div class="ttc" id="classcutlass_1_1thread_1_1Matrix_html_a8a5afcd352fd76b6c3062372084437ca"><div class="ttname"><a href="classcutlass_1_1thread_1_1Matrix.html#a8a5afcd352fd76b6c3062372084437ca">cutlass::thread::Matrix::Matrix</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Matrix(Diagonal const &amp;diag)</div><div class="ttdoc">Ctor. </div><div class="ttdef"><b>Definition:</b> thread/matrix.h:130</div></div>
<div class="ttc" id="classcutlass_1_1thread_1_1Matrix_html_a3c9535ffa08c91d0040b09667c81b201"><div class="ttname"><a href="classcutlass_1_1thread_1_1Matrix.html#a3c9535ffa08c91d0040b09667c81b201">cutlass::thread::Matrix::Reference</a></div><div class="ttdeci">Element &amp; Reference</div><div class="ttdoc">Reference type to an element. </div><div class="ttdef"><b>Definition:</b> thread/matrix.h:71</div></div>
<div class="ttc" id="classcutlass_1_1thread_1_1Matrix_html_a850e9e43797c386ffbdec398d1c1b559"><div class="ttname"><a href="classcutlass_1_1thread_1_1Matrix.html#a850e9e43797c386ffbdec398d1c1b559">cutlass::thread::Matrix::const_ref</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE ConstTensorRef const_ref() const </div><div class="ttdoc">Returns a TensorRef pointing to the first element of the tensor. </div><div class="ttdef"><b>Definition:</b> thread/matrix.h:142</div></div>
<div class="ttc" id="classcutlass_1_1thread_1_1Matrix_html_a23134a82ceae2601f3d6694a02271877"><div class="ttname"><a href="classcutlass_1_1thread_1_1Matrix.html#a23134a82ceae2601f3d6694a02271877">cutlass::thread::Matrix::layout</a></div><div class="ttdeci">static CUTLASS_HOST_DEVICE Layout layout()</div><div class="ttdoc">Returns the layout object. </div><div class="ttdef"><b>Definition:</b> thread/matrix.h:120</div></div>
<div class="ttc" id="cutlass_8h_html"><div class="ttname"><a href="cutlass_8h.html">cutlass.h</a></div><div class="ttdoc">Basic include for CUTLASS. </div></div>
<div class="ttc" id="structcutlass_1_1MatrixCoord_html"><div class="ttname"><a href="structcutlass_1_1MatrixCoord.html">cutlass::MatrixCoord</a></div><div class="ttdef"><b>Definition:</b> matrix_coord.h:39</div></div>
<div class="ttc" id="classcutlass_1_1thread_1_1Matrix_html_a65a4c5ff99fd0a7dbee722c9e04fac35"><div class="ttname"><a href="classcutlass_1_1thread_1_1Matrix.html#a65a4c5ff99fd0a7dbee722c9e04fac35">cutlass::thread::Matrix::const_view</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE ConstTensorView const_view() const </div><div class="ttdoc">Returns a TensorView to const data. </div><div class="ttdef"><b>Definition:</b> thread/matrix.h:154</div></div>
<div class="ttc" id="classcutlass_1_1thread_1_1Matrix_html_a20cc1aac0aa311bb952cf0b491d3b185"><div class="ttname"><a href="classcutlass_1_1thread_1_1Matrix.html#a20cc1aac0aa311bb952cf0b491d3b185">cutlass::thread::Matrix::kRank</a></div><div class="ttdeci">static int const kRank</div><div class="ttdoc">Logical rank of tensor index space. </div><div class="ttdef"><b>Definition:</b> thread/matrix.h:74</div></div>
<div class="ttc" id="classcutlass_1_1thread_1_1Matrix_html_ad13ab25b0e147387c7868fbde3dd0436"><div class="ttname"><a href="classcutlass_1_1thread_1_1Matrix.html#ad13ab25b0e147387c7868fbde3dd0436">cutlass::thread::Matrix::kColumns</a></div><div class="ttdeci">static int const kColumns</div><div class="ttdoc">Number of columns. </div><div class="ttdef"><b>Definition:</b> thread/matrix.h:65</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
