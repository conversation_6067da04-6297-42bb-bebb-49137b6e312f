<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: tensor_op_multiplicand_sm75.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_2296cf082f2778f9a3503c8ea1010763.html">layout</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">tensor_op_multiplicand_sm75.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="tensor__op__multiplicand__sm75_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2017-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> **************************************************************************************************/</span></div><div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;</div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="cutlass_8h.html">cutlass/cutlass.h</a>&quot;</span></div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="coord_8h.html">cutlass/coord.h</a>&quot;</span></div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="matrix__coord_8h.html">cutlass/matrix_coord.h</a>&quot;</span></div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="pitch__linear_8h.html">cutlass/layout/pitch_linear.h</a>&quot;</span></div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;</div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;</div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="keyword">namespace </span>layout {</div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;</div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;</div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> ElementSize, <span class="keywordtype">int</span> Crosswise&gt;</div><div class="line"><a name="l00046"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html">   46</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html">TensorOpMultiplicand</a> {</div><div class="line"><a name="l00048"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a92637a459d6d39f55aada64522e77dd6">   48</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a92637a459d6d39f55aada64522e77dd6">kRank</a> = 2;</div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;</div><div class="line"><a name="l00051"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a8c6e0632e7a38d03612a73a93f5f6a1f">   51</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a8c6e0632e7a38d03612a73a93f5f6a1f">kStrideRank</a> = 1;</div><div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;</div><div class="line"><a name="l00054"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a68fab741fd24d425c260d274b550bd2f">   54</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a68fab741fd24d425c260d274b550bd2f">Index</a> = int32_t;</div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;</div><div class="line"><a name="l00057"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a28b111ff2701662606d3c69d53c49a84">   57</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a28b111ff2701662606d3c69d53c49a84">LongIndex</a> = int64_t;</div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;</div><div class="line"><a name="l00060"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#abf27d3930c7287a173a45596dd823903">   60</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">TensorCoord</a> = <a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">PitchLinearCoord</a>;</div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;</div><div class="line"><a name="l00063"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a86f319dcc8cc4913ef676bcf0daf3a1a">   63</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1Coord.html">Stride</a> = <a class="code" href="structcutlass_1_1Coord.html">Coord&lt;kStrideRank, Index, LongIndex&gt;</a>;</div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;</div><div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;  <span class="comment">// Static constants</span></div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;</div><div class="line"><a name="l00070"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a627189c6253996de45f23c2dc04df72e">   70</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a627189c6253996de45f23c2dc04df72e">kAccessSize</a> = 128;</div><div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;</div><div class="line"><a name="l00072"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a1f35f08a131d76521a98c391acedb4e6">   72</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a1f35f08a131d76521a98c391acedb4e6">kElementSize</a> = ElementSize;</div><div class="line"><a name="l00073"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a653fa6fd827643bcb835f6a715ac8d25">   73</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a653fa6fd827643bcb835f6a715ac8d25">kElementsPerAccess</a> = kAccessSize / <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a1f35f08a131d76521a98c391acedb4e6">kElementSize</a>;</div><div class="line"><a name="l00074"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a58b9d5809fed8924636d1caa32f661ef">   74</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a58b9d5809fed8924636d1caa32f661ef">kCrosswise</a> = Crosswise;</div><div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;</div><div class="line"><a name="l00078"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a1a8ae93363b37a31b2b0ec0df2ea00d2">   78</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a1a8ae93363b37a31b2b0ec0df2ea00d2">kTileShapeContiguous</a> = 128 / (kAccessSize / 8);</div><div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;</div><div class="line"><a name="l00081"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#abb0b4f936c0e251e9b3560fc9b7820f6">   81</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#abb0b4f936c0e251e9b3560fc9b7820f6">kFactor</a> =</div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;      kTileShapeContiguous * kElementsPerAccess / <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a58b9d5809fed8924636d1caa32f661ef">kCrosswise</a>;</div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;</div><div class="line"><a name="l00087"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a56de60deb0ebd18347ad4fa33e10ffce">   87</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a56de60deb0ebd18347ad4fa33e10ffce">kTileShapeStride</a> =</div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;      ((kTileShapeContiguous / <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#abb0b4f936c0e251e9b3560fc9b7820f6">kFactor</a>) &gt; (32 / kTileShapeContiguous))</div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;          ? (kTileShapeContiguous / kFactor)</div><div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;          : (32 / <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a1a8ae93363b37a31b2b0ec0df2ea00d2">kTileShapeContiguous</a>);</div><div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;</div><div class="line"><a name="l00095"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a1fe038f4ae8da6c432397cf20d5894a1">   95</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">TileShape</a> = <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">PitchLinearShape&lt;kTileShapeContiguous, kTileShapeStride&gt;</a>;</div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;</div><div class="line"><a name="l00098"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a46f64537deddaaedfaca7b5ae3cc3e6e">   98</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">PartitionShape</a> = <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">PitchLinearShape&lt;4, 4&gt;</a>;</div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;</div><div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">PartitionCount</a> =</div><div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;      <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">PitchLinearShape</a>&lt;<a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">TileShape::kContiguous</a> / <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">PartitionShape::kContiguous</a>,</div><div class="line"><a name="l00102"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a439376ce6d4333d65d971c0012674931">  102</a></span>&#160;                       <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#aaec0afa0c26627d951d2d2b98a3e5601">TileShape::kStrided</a> / <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#aaec0afa0c26627d951d2d2b98a3e5601">PartitionShape::kStrided</a>&gt;;</div><div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;</div><div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">AccessCount</a> =</div><div class="line"><a name="l00105"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#aca2a06b16d1d4b1b9c0e8b8485e1b02f">  105</a></span>&#160;      <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">PitchLinearShape&lt;PartitionShape::kContiguous, PartitionShape::kStrided&gt;</a>;</div><div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;</div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160; <span class="keyword">private</span>:</div><div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;</div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> stride_;</div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;</div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160; <span class="keyword">public</span>:</div><div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;  <span class="comment">// Methods</span></div><div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;</div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00122"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a8a437591f9e9aa95ce0616dd931f48bd">  122</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a8a437591f9e9aa95ce0616dd931f48bd">TensorOpMultiplicand</a>(<a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a68fab741fd24d425c260d274b550bd2f">Index</a> ldm = 0) : stride_(ldm) {}</div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;</div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00126"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#aeee51a96d48446dd30a0024f6a88a95d">  126</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#aeee51a96d48446dd30a0024f6a88a95d">TensorOpMultiplicand</a>(<a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a57baa00f66f3b3ad24dbc803186e40d4">stride</a>) : stride_(stride) {}</div><div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;</div><div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00130"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#aae09584d5e60d401d73484d579722007">  130</a></span>&#160;  <span class="keyword">static</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html">TensorOpMultiplicand</a> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#aae09584d5e60d401d73484d579722007">packed</a>(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent) {</div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a8a437591f9e9aa95ce0616dd931f48bd">TensorOpMultiplicand</a>(extent[0]);</div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;  }</div><div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;</div><div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00137"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a13c4bd4700ca704d527a4f83f0e58365">  137</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a28b111ff2701662606d3c69d53c49a84">LongIndex</a> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a13c4bd4700ca704d527a4f83f0e58365">operator()</a>(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;coord)<span class="keyword"> const </span>{</div><div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;    <span class="comment">// First, compute c and s of vector within source (in units of vector</span></div><div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;    <span class="comment">// accesses)</span></div><div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;</div><div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;    <span class="keywordtype">int</span> vec_contiguous_idx = coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#adb31bc9b8cf49dfff64245b70a850834">contiguous</a>() / <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a653fa6fd827643bcb835f6a715ac8d25">kElementsPerAccess</a>;</div><div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;    <span class="keywordtype">int</span> vec_strided_idx = coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#aa828f8dbee3903754b56759c1e6a6043">strided</a>() / <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#abb0b4f936c0e251e9b3560fc9b7820f6">kFactor</a>;</div><div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;</div><div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;    <span class="comment">// Compute the fundamental tile being accessed</span></div><div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;    <span class="keywordtype">int</span> tile_contiguous_idx =</div><div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;        vec_contiguous_idx / (<a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">TileShape::kContiguous</a> / <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#abb0b4f936c0e251e9b3560fc9b7820f6">kFactor</a>);</div><div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;</div><div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;    <span class="keywordtype">int</span> tile_contiguous_residual =</div><div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;        vec_contiguous_idx % (<a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">TileShape::kContiguous</a> / <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#abb0b4f936c0e251e9b3560fc9b7820f6">kFactor</a>) +</div><div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;        ((coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#aa828f8dbee3903754b56759c1e6a6043">strided</a>() % <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#abb0b4f936c0e251e9b3560fc9b7820f6">kFactor</a>) * (<a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">TileShape::kContiguous</a> / kFactor));</div><div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;    <span class="keywordtype">int</span> tile_strided_residual = vec_strided_idx % <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#aaec0afa0c26627d951d2d2b98a3e5601">TileShape::kStrided</a>;</div><div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;</div><div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;    <span class="comment">// Compute the &#39;partition&#39; within the fundamental tile</span></div><div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;    <span class="keywordtype">int</span> partition_contiguous_idx =</div><div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;        tile_contiguous_residual / <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">PartitionShape::kContiguous</a>;</div><div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;    <span class="keywordtype">int</span> partition_strided_idx =</div><div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;        tile_strided_residual / <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#aaec0afa0c26627d951d2d2b98a3e5601">PartitionShape::kStrided</a>;</div><div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;</div><div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;    <span class="keywordtype">int</span> partition_contiguous_residual =</div><div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;        tile_contiguous_residual % <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">PartitionShape::kContiguous</a>;</div><div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;    <span class="keywordtype">int</span> partition_strided_residual =</div><div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;        tile_strided_residual % <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#aaec0afa0c26627d951d2d2b98a3e5601">PartitionShape::kStrided</a>;</div><div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;</div><div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;    <span class="comment">// Then swizzle</span></div><div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;</div><div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;    <span class="keywordtype">int</span> permuted_vec_contiguous_within_partition =</div><div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;        partition_contiguous_residual ^ (partition_strided_residual % 4);</div><div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;</div><div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;    <span class="keywordtype">int</span> permuted_partition_contiguous_within_tile =</div><div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;        partition_contiguous_idx ^ (partition_strided_idx % 2);</div><div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;</div><div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;    <span class="comment">// Compute final element location</span></div><div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;</div><div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;    <span class="keywordtype">int</span> element_contiguous = (tile_contiguous_idx * <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">TileShape::kContiguous</a> +</div><div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;                              permuted_partition_contiguous_within_tile *</div><div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;                                  <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">PartitionShape::kContiguous</a> +</div><div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;                              permuted_vec_contiguous_within_partition) *</div><div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;                                 kElementsPerAccess +</div><div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;                             (coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#adb31bc9b8cf49dfff64245b70a850834">contiguous</a>() % <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a653fa6fd827643bcb835f6a715ac8d25">kElementsPerAccess</a>);</div><div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;</div><div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;    <span class="keywordtype">int</span> element_strided = vec_strided_idx;</div><div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;</div><div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;    <span class="keywordflow">return</span> element_contiguous + element_strided * stride_[0] * <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#abb0b4f936c0e251e9b3560fc9b7820f6">kFactor</a>;</div><div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;  }</div><div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;</div><div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00194"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a57baa00f66f3b3ad24dbc803186e40d4">  194</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a57baa00f66f3b3ad24dbc803186e40d4">stride</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> stride_; }</div><div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;</div><div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00198"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#acf086249495fdc5f55f7d4a03ce3c4dc">  198</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> &amp;<a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#acf086249495fdc5f55f7d4a03ce3c4dc">stride</a>() { <span class="keywordflow">return</span> stride_; }</div><div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;</div><div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00203"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a9ef7a95265d1602eb5d050eb89ad8b6b">  203</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a28b111ff2701662606d3c69d53c49a84">LongIndex</a> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a9ef7a95265d1602eb5d050eb89ad8b6b">capacity</a>(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent)<span class="keyword"> const </span>{</div><div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;    <span class="keywordflow">return</span> extent[1] * stride_[0];</div><div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;  }</div><div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;};</div><div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;</div><div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160;</div><div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> ElementSize, <span class="keywordtype">int</span> Crosswise&gt;</div><div class="line"><a name="l00213"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html">  213</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html">TensorOpMultiplicandCongruous</a> {</div><div class="line"><a name="l00215"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a11d2f9f8444139c934ea1af67fb358ed">  215</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kRank = 2;</div><div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;</div><div class="line"><a name="l00218"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a616795a0ef2b4b1d38d666972ec91a65">  218</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kStrideRank = 1;</div><div class="line"><a name="l00219"></a><span class="lineno">  219</span>&#160;</div><div class="line"><a name="l00221"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#ab498476001a090017735113863e28898">  221</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#ab498476001a090017735113863e28898">Index</a> = int32_t;</div><div class="line"><a name="l00222"></a><span class="lineno">  222</span>&#160;</div><div class="line"><a name="l00224"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#aa04ef0da8d8a859c2b7bb08cb3752d3d">  224</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#aa04ef0da8d8a859c2b7bb08cb3752d3d">LongIndex</a> = int64_t;</div><div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;</div><div class="line"><a name="l00227"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a316d20a2b5c904590ab5c84abea2a11c">  227</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">TensorCoord</a> = <a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">PitchLinearCoord</a>;</div><div class="line"><a name="l00228"></a><span class="lineno">  228</span>&#160;</div><div class="line"><a name="l00230"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#acb15140b4e313db8564cc5a580e57c7d">  230</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1Coord.html">Stride</a> = <a class="code" href="structcutlass_1_1Coord.html">Coord&lt;kStrideRank, Index, LongIndex&gt;</a>;</div><div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160;</div><div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160;  <span class="comment">// Invariants</span></div><div class="line"><a name="l00234"></a><span class="lineno">  234</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00235"></a><span class="lineno">  235</span>&#160;</div><div class="line"><a name="l00236"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#ae830ff3cb6bf7a23f9b07097cfb92a59">  236</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html">Base</a> = <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html">TensorOpMultiplicand&lt;ElementSize, Crosswise&gt;</a>;</div><div class="line"><a name="l00237"></a><span class="lineno">  237</span>&#160;</div><div class="line"><a name="l00239"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a580ac36bfc4fbd587fcbe3f36e25fb43">  239</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kAccessSize = Base::kAccessSize;</div><div class="line"><a name="l00240"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a4fc7a49e6a7b8fea8224991d5ab1e008">  240</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a4fc7a49e6a7b8fea8224991d5ab1e008">TileShape</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">Base::TileShape</a>;</div><div class="line"><a name="l00241"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a33b3e232203ea774f1bfd568f710f36d">  241</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a33b3e232203ea774f1bfd568f710f36d">PartitionShape</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">Base::PartitionShape</a>;</div><div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160;</div><div class="line"><a name="l00243"></a><span class="lineno">  243</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00244"></a><span class="lineno">  244</span>&#160;  <span class="comment">// Static constants</span></div><div class="line"><a name="l00245"></a><span class="lineno">  245</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00246"></a><span class="lineno">  246</span>&#160;</div><div class="line"><a name="l00247"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a0486b60fe43ff7f8b10f0ab6cde0ead4">  247</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kElementSize = Base::kElementSize;</div><div class="line"><a name="l00248"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a1937f2fe9816df95d76ef9036345a5c9">  248</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kElementsPerAccess = Base::kElementsPerAccess;</div><div class="line"><a name="l00249"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a9c818862df1951d9e8ebb31165b61fb5">  249</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a9c818862df1951d9e8ebb31165b61fb5">PartitionCount</a> =  <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">Base::PartitionCount</a>;</div><div class="line"><a name="l00250"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a45607ffb63b8f3e307a2537777c88491">  250</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a45607ffb63b8f3e307a2537777c88491">AccessCount</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">Base::AccessCount</a>;</div><div class="line"><a name="l00251"></a><span class="lineno">  251</span>&#160;</div><div class="line"><a name="l00252"></a><span class="lineno">  252</span>&#160; <span class="keyword">private</span>:</div><div class="line"><a name="l00253"></a><span class="lineno">  253</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00254"></a><span class="lineno">  254</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00255"></a><span class="lineno">  255</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00256"></a><span class="lineno">  256</span>&#160;</div><div class="line"><a name="l00257"></a><span class="lineno">  257</span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html">Base</a> layout_;</div><div class="line"><a name="l00258"></a><span class="lineno">  258</span>&#160;</div><div class="line"><a name="l00259"></a><span class="lineno">  259</span>&#160; <span class="keyword">public</span>:</div><div class="line"><a name="l00260"></a><span class="lineno">  260</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00261"></a><span class="lineno">  261</span>&#160;  <span class="comment">// Methods</span></div><div class="line"><a name="l00262"></a><span class="lineno">  262</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00263"></a><span class="lineno">  263</span>&#160;</div><div class="line"><a name="l00265"></a><span class="lineno">  265</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00266"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a3fbf6b274197d5f90513aa10360e667e">  266</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a3fbf6b274197d5f90513aa10360e667e">TensorOpMultiplicandCongruous</a>(<a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#ab498476001a090017735113863e28898">Index</a> ldm = 0) : layout_(ldm) {}</div><div class="line"><a name="l00267"></a><span class="lineno">  267</span>&#160;</div><div class="line"><a name="l00269"></a><span class="lineno">  269</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00270"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#afa388d90ae2f571c85c8988a1a15f934">  270</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#afa388d90ae2f571c85c8988a1a15f934">TensorOpMultiplicandCongruous</a>(<a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a57baa00f66f3b3ad24dbc803186e40d4">stride</a>) : layout_(stride) {}</div><div class="line"><a name="l00271"></a><span class="lineno">  271</span>&#160;</div><div class="line"><a name="l00273"></a><span class="lineno">  273</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00274"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a9307437b4b93a3dd450244bb54aa50d4">  274</a></span>&#160;  <span class="keyword">static</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html">TensorOpMultiplicandCongruous</a> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a9307437b4b93a3dd450244bb54aa50d4">packed</a>(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent) {</div><div class="line"><a name="l00275"></a><span class="lineno">  275</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html">TensorOpMultiplicandCongruous</a>(extent[0]);</div><div class="line"><a name="l00276"></a><span class="lineno">  276</span>&#160;  }</div><div class="line"><a name="l00277"></a><span class="lineno">  277</span>&#160;</div><div class="line"><a name="l00280"></a><span class="lineno">  280</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00281"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a3b9ea3abdcd5cc5e4c5b14f7f329bb4f">  281</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#aa04ef0da8d8a859c2b7bb08cb3752d3d">LongIndex</a> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a3b9ea3abdcd5cc5e4c5b14f7f329bb4f">operator()</a>(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;coord)<span class="keyword"> const </span>{</div><div class="line"><a name="l00282"></a><span class="lineno">  282</span>&#160;    <span class="keywordflow">return</span> layout_(coord);</div><div class="line"><a name="l00283"></a><span class="lineno">  283</span>&#160;  }</div><div class="line"><a name="l00284"></a><span class="lineno">  284</span>&#160;</div><div class="line"><a name="l00286"></a><span class="lineno">  286</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00287"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a6ef0968585fcb6bb564e1ea6c4bdc9a3">  287</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">TensorCoord</a> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a6ef0968585fcb6bb564e1ea6c4bdc9a3">inverse</a>(<a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#aa04ef0da8d8a859c2b7bb08cb3752d3d">LongIndex</a> offset)<span class="keyword"> const </span>{</div><div class="line"><a name="l00288"></a><span class="lineno">  288</span>&#160;    <a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">PitchLinearCoord</a> coord = layout_.inverse(offset);</div><div class="line"><a name="l00289"></a><span class="lineno">  289</span>&#160;    <span class="keywordflow">return</span> coord;</div><div class="line"><a name="l00290"></a><span class="lineno">  290</span>&#160;  }</div><div class="line"><a name="l00291"></a><span class="lineno">  291</span>&#160;</div><div class="line"><a name="l00293"></a><span class="lineno">  293</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00294"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a6b9e003e72d27841f42ee2e74689c632">  294</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a6b9e003e72d27841f42ee2e74689c632">stride</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> layout_.<a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a57baa00f66f3b3ad24dbc803186e40d4">stride</a>(); }</div><div class="line"><a name="l00295"></a><span class="lineno">  295</span>&#160;</div><div class="line"><a name="l00297"></a><span class="lineno">  297</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00298"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a8d2117bfc734389ab04e40288e783a4d">  298</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> &amp;<a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a8d2117bfc734389ab04e40288e783a4d">stride</a>() { <span class="keywordflow">return</span> layout_.<a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a57baa00f66f3b3ad24dbc803186e40d4">stride</a>(); }</div><div class="line"><a name="l00299"></a><span class="lineno">  299</span>&#160;</div><div class="line"><a name="l00302"></a><span class="lineno">  302</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00303"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a016ea88480ec49f60b311b00e06dba54">  303</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#aa04ef0da8d8a859c2b7bb08cb3752d3d">LongIndex</a> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a016ea88480ec49f60b311b00e06dba54">capacity</a>(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent)<span class="keyword"> const </span>{</div><div class="line"><a name="l00304"></a><span class="lineno">  304</span>&#160;    <span class="keywordflow">return</span> layout_.<a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a9ef7a95265d1602eb5d050eb89ad8b6b">capacity</a>(extent);</div><div class="line"><a name="l00305"></a><span class="lineno">  305</span>&#160;  }</div><div class="line"><a name="l00306"></a><span class="lineno">  306</span>&#160;};</div><div class="line"><a name="l00307"></a><span class="lineno">  307</span>&#160;</div><div class="line"><a name="l00309"></a><span class="lineno">  309</span>&#160;</div><div class="line"><a name="l00312"></a><span class="lineno">  312</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> Crosswise&gt;</div><div class="line"><a name="l00313"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html">  313</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html">TensorOpMultiplicandCongruous</a>&lt;32, Crosswise&gt; {</div><div class="line"><a name="l00315"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#a037912b3f87ab25000802e091470c348">  315</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kRank = 2;</div><div class="line"><a name="l00316"></a><span class="lineno">  316</span>&#160;</div><div class="line"><a name="l00318"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#a942daade1d67cf023fa8e7a98055d7e6">  318</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kStrideRank = 1;</div><div class="line"><a name="l00319"></a><span class="lineno">  319</span>&#160;</div><div class="line"><a name="l00321"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#adfba7bb7c28bfb734201bac9ca0d2bd0">  321</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#adfba7bb7c28bfb734201bac9ca0d2bd0">Index</a> = int32_t;</div><div class="line"><a name="l00322"></a><span class="lineno">  322</span>&#160;</div><div class="line"><a name="l00324"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#a8873becf0049da7289f853021e3beec3">  324</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#a8873becf0049da7289f853021e3beec3">LongIndex</a> = int64_t;</div><div class="line"><a name="l00325"></a><span class="lineno">  325</span>&#160;</div><div class="line"><a name="l00327"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#a2149a77af698eb1bbf7042054a06be4e">  327</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">TensorCoord</a> = <a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">PitchLinearCoord</a>;</div><div class="line"><a name="l00328"></a><span class="lineno">  328</span>&#160;</div><div class="line"><a name="l00330"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#a59fad8f0cbc0f3edb84604c54f843755">  330</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1Coord.html">Stride</a> = <a class="code" href="structcutlass_1_1Coord.html">Coord&lt;kStrideRank, Index, LongIndex&gt;</a>;</div><div class="line"><a name="l00331"></a><span class="lineno">  331</span>&#160;</div><div class="line"><a name="l00332"></a><span class="lineno">  332</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00333"></a><span class="lineno">  333</span>&#160;  <span class="comment">// Invariants</span></div><div class="line"><a name="l00334"></a><span class="lineno">  334</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00335"></a><span class="lineno">  335</span>&#160;</div><div class="line"><a name="l00337"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#a2b38596d9f461583bbb6c8129acc24e3">  337</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kAccessSize = 128;</div><div class="line"><a name="l00338"></a><span class="lineno">  338</span>&#160;</div><div class="line"><a name="l00340"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#a1ed59cbdce1bac0bc6ea8ea3dd1c1e8c">  340</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">TileShape</a> = <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">PitchLinearShape&lt;8, 4&gt;</a>;</div><div class="line"><a name="l00341"></a><span class="lineno">  341</span>&#160;</div><div class="line"><a name="l00343"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#ae1dfd7c1955567ade7e136b779fda2e7">  343</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">PartitionShape</a> = <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">PitchLinearShape&lt;8, 4&gt;</a>;</div><div class="line"><a name="l00344"></a><span class="lineno">  344</span>&#160;</div><div class="line"><a name="l00345"></a><span class="lineno">  345</span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">PartitionCount</a> =</div><div class="line"><a name="l00346"></a><span class="lineno">  346</span>&#160;      <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">PitchLinearShape</a>&lt;<a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">TileShape::kContiguous</a> / <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">PartitionShape::kContiguous</a>,</div><div class="line"><a name="l00347"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#a10feb79f61f6dec862da9541baa37425">  347</a></span>&#160;                       <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#aaec0afa0c26627d951d2d2b98a3e5601">TileShape::kStrided</a> / <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#aaec0afa0c26627d951d2d2b98a3e5601">PartitionShape::kStrided</a>&gt;;</div><div class="line"><a name="l00348"></a><span class="lineno">  348</span>&#160;</div><div class="line"><a name="l00349"></a><span class="lineno">  349</span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">AccessCount</a> =</div><div class="line"><a name="l00350"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#ac6876a070bf8f7805a70a4b9f41493a7">  350</a></span>&#160;      <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">PitchLinearShape&lt;PartitionShape::kContiguous, PartitionShape::kStrided&gt;</a>;</div><div class="line"><a name="l00351"></a><span class="lineno">  351</span>&#160;</div><div class="line"><a name="l00352"></a><span class="lineno">  352</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00353"></a><span class="lineno">  353</span>&#160;  <span class="comment">// Static constants</span></div><div class="line"><a name="l00354"></a><span class="lineno">  354</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00355"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#a075ffc6df8fc96d549ef16cd61ab11e4">  355</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kElementSize = 32;</div><div class="line"><a name="l00356"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#a4349d6c8b646e4537f7bd43982d07ffd">  356</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kElementsPerAccess = kAccessSize / <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a1f35f08a131d76521a98c391acedb4e6">kElementSize</a>;</div><div class="line"><a name="l00357"></a><span class="lineno">  357</span>&#160;</div><div class="line"><a name="l00358"></a><span class="lineno">  358</span>&#160; <span class="keyword">private</span>:</div><div class="line"><a name="l00359"></a><span class="lineno">  359</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00360"></a><span class="lineno">  360</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00361"></a><span class="lineno">  361</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00362"></a><span class="lineno">  362</span>&#160;</div><div class="line"><a name="l00364"></a><span class="lineno">  364</span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> stride_;</div><div class="line"><a name="l00365"></a><span class="lineno">  365</span>&#160;</div><div class="line"><a name="l00366"></a><span class="lineno">  366</span>&#160; <span class="keyword">public</span>:</div><div class="line"><a name="l00367"></a><span class="lineno">  367</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00368"></a><span class="lineno">  368</span>&#160;  <span class="comment">// Methods</span></div><div class="line"><a name="l00369"></a><span class="lineno">  369</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00370"></a><span class="lineno">  370</span>&#160;</div><div class="line"><a name="l00372"></a><span class="lineno">  372</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00373"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#acb38ed15663b5dfc87d071e18ca29682">  373</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#acb38ed15663b5dfc87d071e18ca29682">TensorOpMultiplicandCongruous</a>(<a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#adfba7bb7c28bfb734201bac9ca0d2bd0">Index</a> ldm = 0) : stride_(ldm) {}</div><div class="line"><a name="l00374"></a><span class="lineno">  374</span>&#160;</div><div class="line"><a name="l00376"></a><span class="lineno">  376</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00377"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#a91245dbf28b2768acf9aa3d77ba20ee1">  377</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#a91245dbf28b2768acf9aa3d77ba20ee1">TensorOpMultiplicandCongruous</a>(<a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a57baa00f66f3b3ad24dbc803186e40d4">stride</a>) : stride_(stride) {}</div><div class="line"><a name="l00378"></a><span class="lineno">  378</span>&#160;</div><div class="line"><a name="l00380"></a><span class="lineno">  380</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00381"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#aafa9812d3c82b30b2d416738c586bfe0">  381</a></span>&#160;  <span class="keyword">static</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html">TensorOpMultiplicandCongruous</a> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#aafa9812d3c82b30b2d416738c586bfe0">packed</a>(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent) {</div><div class="line"><a name="l00382"></a><span class="lineno">  382</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html">TensorOpMultiplicandCongruous</a>(extent[0]);</div><div class="line"><a name="l00383"></a><span class="lineno">  383</span>&#160;  }</div><div class="line"><a name="l00384"></a><span class="lineno">  384</span>&#160;</div><div class="line"><a name="l00387"></a><span class="lineno">  387</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00388"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#a3881a944fbeeda82884adfd5853f7577">  388</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#a8873becf0049da7289f853021e3beec3">LongIndex</a> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#a3881a944fbeeda82884adfd5853f7577">operator()</a>(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;coord)<span class="keyword"> const </span>{</div><div class="line"><a name="l00389"></a><span class="lineno">  389</span>&#160;    <span class="keywordtype">int</span> tc = coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#adb31bc9b8cf49dfff64245b70a850834">contiguous</a>() / 32;</div><div class="line"><a name="l00390"></a><span class="lineno">  390</span>&#160;    <span class="keywordtype">int</span> ts = coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#aa828f8dbee3903754b56759c1e6a6043">strided</a>() / 4;</div><div class="line"><a name="l00391"></a><span class="lineno">  391</span>&#160;</div><div class="line"><a name="l00392"></a><span class="lineno">  392</span>&#160;    <span class="keywordtype">int</span> c = (coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#adb31bc9b8cf49dfff64245b70a850834">contiguous</a>() % 32) / kElementsPerAccess;</div><div class="line"><a name="l00393"></a><span class="lineno">  393</span>&#160;    <span class="keywordtype">int</span> s = coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#aa828f8dbee3903754b56759c1e6a6043">strided</a>() % 4;</div><div class="line"><a name="l00394"></a><span class="lineno">  394</span>&#160;</div><div class="line"><a name="l00395"></a><span class="lineno">  395</span>&#160;    <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#a8873becf0049da7289f853021e3beec3">LongIndex</a> offset = (c ^ (2 * s)) * kElementsPerAccess + s * stride_[0] +</div><div class="line"><a name="l00396"></a><span class="lineno">  396</span>&#160;                       tc * 32 + ts * stride_[0] * 4 + coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#adb31bc9b8cf49dfff64245b70a850834">contiguous</a>() % 4;</div><div class="line"><a name="l00397"></a><span class="lineno">  397</span>&#160;</div><div class="line"><a name="l00398"></a><span class="lineno">  398</span>&#160;    <span class="keywordflow">return</span> offset;</div><div class="line"><a name="l00399"></a><span class="lineno">  399</span>&#160;  }</div><div class="line"><a name="l00400"></a><span class="lineno">  400</span>&#160;</div><div class="line"><a name="l00402"></a><span class="lineno">  402</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00403"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#a842b22d0cb0974b95aa9d9b05d90524e">  403</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#a842b22d0cb0974b95aa9d9b05d90524e">stride</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> stride_; }</div><div class="line"><a name="l00404"></a><span class="lineno">  404</span>&#160;</div><div class="line"><a name="l00406"></a><span class="lineno">  406</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00407"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#a659ba11b4c1dc1d06005e160feb13d4b">  407</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> &amp;<a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#a659ba11b4c1dc1d06005e160feb13d4b">stride</a>() { <span class="keywordflow">return</span> stride_; }</div><div class="line"><a name="l00408"></a><span class="lineno">  408</span>&#160;</div><div class="line"><a name="l00411"></a><span class="lineno">  411</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00412"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#a8580ab6b5c31b815c9ff4d146cbec442">  412</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#a8873becf0049da7289f853021e3beec3">LongIndex</a> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#a8580ab6b5c31b815c9ff4d146cbec442">capacity</a>(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent)<span class="keyword"> const </span>{</div><div class="line"><a name="l00413"></a><span class="lineno">  413</span>&#160;    <span class="keywordflow">return</span> extent[1] * stride_[0];</div><div class="line"><a name="l00414"></a><span class="lineno">  414</span>&#160;  }</div><div class="line"><a name="l00415"></a><span class="lineno">  415</span>&#160;};</div><div class="line"><a name="l00416"></a><span class="lineno">  416</span>&#160;</div><div class="line"><a name="l00418"></a><span class="lineno">  418</span>&#160;</div><div class="line"><a name="l00421"></a><span class="lineno">  421</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> ElementSize, <span class="keywordtype">int</span> Crosswise&gt;</div><div class="line"><a name="l00422"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html">  422</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html">ColumnMajorTensorOpMultiplicandCongruous</a> {</div><div class="line"><a name="l00423"></a><span class="lineno">  423</span>&#160;</div><div class="line"><a name="l00425"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#a9b8184bd2ad7692e14945ab0673331e5">  425</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kRank = 2;</div><div class="line"><a name="l00426"></a><span class="lineno">  426</span>&#160;</div><div class="line"><a name="l00428"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#a45984a21ce8ab362d62f390328072bed">  428</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kStrideRank = 1;</div><div class="line"><a name="l00429"></a><span class="lineno">  429</span>&#160;</div><div class="line"><a name="l00431"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#aa455da7f56592fdb70977b1cad108003">  431</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#aa455da7f56592fdb70977b1cad108003">Index</a> = int32_t;</div><div class="line"><a name="l00432"></a><span class="lineno">  432</span>&#160;</div><div class="line"><a name="l00434"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#a37fd29e6b74010e9a3ae089d6a777724">  434</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#a37fd29e6b74010e9a3ae089d6a777724">LongIndex</a> = int64_t;</div><div class="line"><a name="l00435"></a><span class="lineno">  435</span>&#160;</div><div class="line"><a name="l00437"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#a9b6fcec14e142fe9af66797ca0b77d7d">  437</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> = <a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>;</div><div class="line"><a name="l00438"></a><span class="lineno">  438</span>&#160;</div><div class="line"><a name="l00440"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#acae8bbf90758c038fed9501896861c13">  440</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1Coord.html">Stride</a> = <a class="code" href="structcutlass_1_1Coord.html">Coord&lt;kStrideRank, Index, LongIndex&gt;</a>;</div><div class="line"><a name="l00441"></a><span class="lineno">  441</span>&#160;</div><div class="line"><a name="l00442"></a><span class="lineno">  442</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00443"></a><span class="lineno">  443</span>&#160;  <span class="comment">// Invariants</span></div><div class="line"><a name="l00444"></a><span class="lineno">  444</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00445"></a><span class="lineno">  445</span>&#160;</div><div class="line"><a name="l00446"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#a3e44a55d0be474138fce394480c8267e">  446</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html">Base</a> = <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html">TensorOpMultiplicandCongruous&lt;ElementSize, Crosswise&gt;</a>;</div><div class="line"><a name="l00447"></a><span class="lineno">  447</span>&#160;</div><div class="line"><a name="l00449"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#a4824540ce26044047aabec0be63ed0dc">  449</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kAccessSize = Base::kAccessSize;</div><div class="line"><a name="l00450"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#ad00403d7a4792d15fddcf3d229556351">  450</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#ad00403d7a4792d15fddcf3d229556351">TileShape</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a4fc7a49e6a7b8fea8224991d5ab1e008">Base::TileShape</a>;</div><div class="line"><a name="l00451"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#ade26abdd740cb37ed4ef0383a0096c01">  451</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#ade26abdd740cb37ed4ef0383a0096c01">PartitionShape</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a33b3e232203ea774f1bfd568f710f36d">Base::PartitionShape</a>;</div><div class="line"><a name="l00452"></a><span class="lineno">  452</span>&#160;</div><div class="line"><a name="l00453"></a><span class="lineno">  453</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00454"></a><span class="lineno">  454</span>&#160;  <span class="comment">// Static constants</span></div><div class="line"><a name="l00455"></a><span class="lineno">  455</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00456"></a><span class="lineno">  456</span>&#160;</div><div class="line"><a name="l00457"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#aa683ea614d333101f9fb6c221337b4a2">  457</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kElementSize = Base::kElementSize;</div><div class="line"><a name="l00458"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#a1e1b169e7b1108d6c6e975d259938431">  458</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kElementsPerAccess = Base::kElementsPerAccess;</div><div class="line"><a name="l00459"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#abd26edad15a0ce1c3a24d6a9c96f66a3">  459</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#abd26edad15a0ce1c3a24d6a9c96f66a3">PartitionCount</a> =  <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a9c818862df1951d9e8ebb31165b61fb5">Base::PartitionCount</a>;</div><div class="line"><a name="l00460"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#a41a554dc29e1852fe4cfc21fa250a9ac">  460</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#a41a554dc29e1852fe4cfc21fa250a9ac">AccessCount</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a45607ffb63b8f3e307a2537777c88491">Base::AccessCount</a>;</div><div class="line"><a name="l00461"></a><span class="lineno">  461</span>&#160;</div><div class="line"><a name="l00462"></a><span class="lineno">  462</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00463"></a><span class="lineno">  463</span>&#160;</div><div class="line"><a name="l00464"></a><span class="lineno">  464</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00465"></a><span class="lineno">  465</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00466"></a><span class="lineno">  466</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00467"></a><span class="lineno">  467</span>&#160;</div><div class="line"><a name="l00468"></a><span class="lineno">  468</span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html">Base</a> layout_;</div><div class="line"><a name="l00469"></a><span class="lineno">  469</span>&#160;</div><div class="line"><a name="l00470"></a><span class="lineno">  470</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00471"></a><span class="lineno">  471</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00472"></a><span class="lineno">  472</span>&#160;  <span class="comment">// Methods</span></div><div class="line"><a name="l00473"></a><span class="lineno">  473</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00474"></a><span class="lineno">  474</span>&#160;</div><div class="line"><a name="l00476"></a><span class="lineno">  476</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00477"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#a910ff464fc55a153bf3e54cb7b816ccb">  477</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#a910ff464fc55a153bf3e54cb7b816ccb">ColumnMajorTensorOpMultiplicandCongruous</a>(<a class="code" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#aa455da7f56592fdb70977b1cad108003">Index</a> ldm = 0): layout_(ldm) { }</div><div class="line"><a name="l00478"></a><span class="lineno">  478</span>&#160;</div><div class="line"><a name="l00480"></a><span class="lineno">  480</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00481"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#a588582ed7496bb9f6cfbb7f79873affa">  481</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#a588582ed7496bb9f6cfbb7f79873affa">ColumnMajorTensorOpMultiplicandCongruous</a>(<a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a57baa00f66f3b3ad24dbc803186e40d4">stride</a>): layout_(stride) { }</div><div class="line"><a name="l00482"></a><span class="lineno">  482</span>&#160;</div><div class="line"><a name="l00484"></a><span class="lineno">  484</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00485"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#ae068c7ff740e38ea4c06a35a57416595">  485</a></span>&#160;  <span class="keyword">static</span> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html">ColumnMajorTensorOpMultiplicandCongruous</a> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#ae068c7ff740e38ea4c06a35a57416595">packed</a>(<a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent) {</div><div class="line"><a name="l00486"></a><span class="lineno">  486</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html">ColumnMajorTensorOpMultiplicandCongruous</a>(extent.<a class="code" href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">row</a>());</div><div class="line"><a name="l00487"></a><span class="lineno">  487</span>&#160;  }</div><div class="line"><a name="l00488"></a><span class="lineno">  488</span>&#160;</div><div class="line"><a name="l00491"></a><span class="lineno">  491</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00492"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#a4707586d7a899943c62fa9aaee2c612e">  492</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#a37fd29e6b74010e9a3ae089d6a777724">LongIndex</a> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#a4707586d7a899943c62fa9aaee2c612e">operator()</a>(<a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;coord)<span class="keyword"> const </span>{</div><div class="line"><a name="l00493"></a><span class="lineno">  493</span>&#160;    <span class="keywordflow">return</span> layout_(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">PitchLinearCoord</a>(coord.<a class="code" href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">row</a>(), coord.<a class="code" href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">column</a>()));</div><div class="line"><a name="l00494"></a><span class="lineno">  494</span>&#160;  }</div><div class="line"><a name="l00495"></a><span class="lineno">  495</span>&#160;</div><div class="line"><a name="l00497"></a><span class="lineno">  497</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00498"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#ae7834e63ec3b61d181f847011be413ab">  498</a></span>&#160;  <a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#ae7834e63ec3b61d181f847011be413ab">inverse</a>(<a class="code" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#a37fd29e6b74010e9a3ae089d6a777724">LongIndex</a> offset)<span class="keyword"> const </span>{</div><div class="line"><a name="l00499"></a><span class="lineno">  499</span>&#160;    <a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">PitchLinearCoord</a> coord = layout_.<a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a6ef0968585fcb6bb564e1ea6c4bdc9a3">inverse</a>(offset);</div><div class="line"><a name="l00500"></a><span class="lineno">  500</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>(coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#adb31bc9b8cf49dfff64245b70a850834">contiguous</a>(), coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#aa828f8dbee3903754b56759c1e6a6043">strided</a>());    </div><div class="line"><a name="l00501"></a><span class="lineno">  501</span>&#160;  }</div><div class="line"><a name="l00502"></a><span class="lineno">  502</span>&#160;</div><div class="line"><a name="l00504"></a><span class="lineno">  504</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00505"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#aef0305eeba98299968d5b42b262425d0">  505</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#aef0305eeba98299968d5b42b262425d0">stride</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00506"></a><span class="lineno">  506</span>&#160;    <span class="keywordflow">return</span> layout_.<a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a6b9e003e72d27841f42ee2e74689c632">stride</a>();</div><div class="line"><a name="l00507"></a><span class="lineno">  507</span>&#160;  }</div><div class="line"><a name="l00508"></a><span class="lineno">  508</span>&#160;</div><div class="line"><a name="l00510"></a><span class="lineno">  510</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00511"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#a83a61ac278a06817c63cdc71fe823675">  511</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> &amp; <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#a83a61ac278a06817c63cdc71fe823675">stride</a>() {</div><div class="line"><a name="l00512"></a><span class="lineno">  512</span>&#160;    <span class="keywordflow">return</span> layout_.<a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a6b9e003e72d27841f42ee2e74689c632">stride</a>();</div><div class="line"><a name="l00513"></a><span class="lineno">  513</span>&#160;  }</div><div class="line"><a name="l00514"></a><span class="lineno">  514</span>&#160;</div><div class="line"><a name="l00516"></a><span class="lineno">  516</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00517"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#a789b0744a97926b5447be3861f184122">  517</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#a37fd29e6b74010e9a3ae089d6a777724">LongIndex</a> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#a789b0744a97926b5447be3861f184122">capacity</a>(<a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent)<span class="keyword"> const </span>{</div><div class="line"><a name="l00518"></a><span class="lineno">  518</span>&#160;    <span class="keywordflow">return</span> layout_.<a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a016ea88480ec49f60b311b00e06dba54">capacity</a>(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">PitchLinearCoord</a>(extent.<a class="code" href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">row</a>(), extent.<a class="code" href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">column</a>()));</div><div class="line"><a name="l00519"></a><span class="lineno">  519</span>&#160;  }</div><div class="line"><a name="l00520"></a><span class="lineno">  520</span>&#160;};</div><div class="line"><a name="l00521"></a><span class="lineno">  521</span>&#160;</div><div class="line"><a name="l00523"></a><span class="lineno">  523</span>&#160;</div><div class="line"><a name="l00526"></a><span class="lineno">  526</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> ElementSize, <span class="keywordtype">int</span> Crosswise&gt;</div><div class="line"><a name="l00527"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html">  527</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html">RowMajorTensorOpMultiplicandCongruous</a> {</div><div class="line"><a name="l00528"></a><span class="lineno">  528</span>&#160;</div><div class="line"><a name="l00530"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#a27065a5c3ac2ad4e798e149c421a33bd">  530</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kRank = 2;</div><div class="line"><a name="l00531"></a><span class="lineno">  531</span>&#160;</div><div class="line"><a name="l00533"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#ac44e7b11d94d2ee3fa0cc7ec2bc1423a">  533</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kStrideRank = 1;</div><div class="line"><a name="l00534"></a><span class="lineno">  534</span>&#160;</div><div class="line"><a name="l00536"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#a32c8623b9c3c8490263ea0af24a1410f">  536</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#a32c8623b9c3c8490263ea0af24a1410f">Index</a> = int32_t;</div><div class="line"><a name="l00537"></a><span class="lineno">  537</span>&#160;</div><div class="line"><a name="l00539"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#ad49467d9028288b0bb93dbf376ef573a">  539</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#ad49467d9028288b0bb93dbf376ef573a">LongIndex</a> = int64_t;</div><div class="line"><a name="l00540"></a><span class="lineno">  540</span>&#160;</div><div class="line"><a name="l00542"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#ab49e1afbe8abd3b1a28ce09f6cdb06e7">  542</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> = <a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>;</div><div class="line"><a name="l00543"></a><span class="lineno">  543</span>&#160;</div><div class="line"><a name="l00545"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#afdf7a943f661b714f164abfc4b2c80e3">  545</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1Coord.html">Stride</a> = <a class="code" href="structcutlass_1_1Coord.html">Coord&lt;kStrideRank, Index, LongIndex&gt;</a>;</div><div class="line"><a name="l00546"></a><span class="lineno">  546</span>&#160;</div><div class="line"><a name="l00547"></a><span class="lineno">  547</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00548"></a><span class="lineno">  548</span>&#160;  <span class="comment">// Invariants</span></div><div class="line"><a name="l00549"></a><span class="lineno">  549</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00550"></a><span class="lineno">  550</span>&#160;</div><div class="line"><a name="l00551"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#a726c11e5c883a32a6948d5d8092c00a9">  551</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html">Base</a> = <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html">TensorOpMultiplicandCongruous&lt;ElementSize, Crosswise&gt;</a>;</div><div class="line"><a name="l00552"></a><span class="lineno">  552</span>&#160;</div><div class="line"><a name="l00554"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#aed5835ab4bafa391321f721b2a39f285">  554</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kAccessSize = Base::kAccessSize;</div><div class="line"><a name="l00555"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#a08f78c18f903ffd868934ba443d0533f">  555</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#a08f78c18f903ffd868934ba443d0533f">TileShape</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a4fc7a49e6a7b8fea8224991d5ab1e008">Base::TileShape</a>;</div><div class="line"><a name="l00556"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#aef568e91d5233b70fffe5315633bcc0d">  556</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#aef568e91d5233b70fffe5315633bcc0d">PartitionShape</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a33b3e232203ea774f1bfd568f710f36d">Base::PartitionShape</a>;</div><div class="line"><a name="l00557"></a><span class="lineno">  557</span>&#160;</div><div class="line"><a name="l00558"></a><span class="lineno">  558</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00559"></a><span class="lineno">  559</span>&#160;  <span class="comment">// Static constants</span></div><div class="line"><a name="l00560"></a><span class="lineno">  560</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00561"></a><span class="lineno">  561</span>&#160;</div><div class="line"><a name="l00562"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#a852a5e6f718ab0e91b0cb6a0e6148648">  562</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kElementSize = Base::kElementSize;</div><div class="line"><a name="l00563"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#a5322b178b2699ec72ac38ed4e6ba3bb7">  563</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kElementsPerAccess = Base::kElementsPerAccess;</div><div class="line"><a name="l00564"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#a6db9483dd8c793e687445514fd00124f">  564</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#a6db9483dd8c793e687445514fd00124f">PartitionCount</a> =  <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a9c818862df1951d9e8ebb31165b61fb5">Base::PartitionCount</a>;</div><div class="line"><a name="l00565"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#a274f0531c54b7e15243b0460d59e0c3b">  565</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#a274f0531c54b7e15243b0460d59e0c3b">AccessCount</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a45607ffb63b8f3e307a2537777c88491">Base::AccessCount</a>;</div><div class="line"><a name="l00566"></a><span class="lineno">  566</span>&#160;</div><div class="line"><a name="l00567"></a><span class="lineno">  567</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00568"></a><span class="lineno">  568</span>&#160;</div><div class="line"><a name="l00569"></a><span class="lineno">  569</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00570"></a><span class="lineno">  570</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00571"></a><span class="lineno">  571</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00572"></a><span class="lineno">  572</span>&#160;</div><div class="line"><a name="l00573"></a><span class="lineno">  573</span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html">Base</a> layout_;</div><div class="line"><a name="l00574"></a><span class="lineno">  574</span>&#160;</div><div class="line"><a name="l00575"></a><span class="lineno">  575</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00576"></a><span class="lineno">  576</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00577"></a><span class="lineno">  577</span>&#160;  <span class="comment">// Methods</span></div><div class="line"><a name="l00578"></a><span class="lineno">  578</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00579"></a><span class="lineno">  579</span>&#160;</div><div class="line"><a name="l00581"></a><span class="lineno">  581</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00582"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#acd7ea2b943914f72f0040b208915aa89">  582</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#acd7ea2b943914f72f0040b208915aa89">RowMajorTensorOpMultiplicandCongruous</a>(<a class="code" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#a32c8623b9c3c8490263ea0af24a1410f">Index</a> ldm = 0): layout_(ldm) { }</div><div class="line"><a name="l00583"></a><span class="lineno">  583</span>&#160;</div><div class="line"><a name="l00585"></a><span class="lineno">  585</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00586"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#a863ff7fbc770524406c5f3a72ec4e919">  586</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#a863ff7fbc770524406c5f3a72ec4e919">RowMajorTensorOpMultiplicandCongruous</a>(<a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a57baa00f66f3b3ad24dbc803186e40d4">stride</a>): layout_(stride) { }</div><div class="line"><a name="l00587"></a><span class="lineno">  587</span>&#160;</div><div class="line"><a name="l00589"></a><span class="lineno">  589</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00590"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#a443b469af1a3602e3173b132ae3dd40b">  590</a></span>&#160;  <span class="keyword">static</span> <a class="code" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html">RowMajorTensorOpMultiplicandCongruous</a> <a class="code" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#a443b469af1a3602e3173b132ae3dd40b">packed</a>(<a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent) {</div><div class="line"><a name="l00591"></a><span class="lineno">  591</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html">RowMajorTensorOpMultiplicandCongruous</a>(extent.<a class="code" href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">column</a>());</div><div class="line"><a name="l00592"></a><span class="lineno">  592</span>&#160;  }</div><div class="line"><a name="l00593"></a><span class="lineno">  593</span>&#160;</div><div class="line"><a name="l00596"></a><span class="lineno">  596</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00597"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#a7e55b41a76b968ade75d77330471e10e">  597</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#ad49467d9028288b0bb93dbf376ef573a">LongIndex</a> <a class="code" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#a7e55b41a76b968ade75d77330471e10e">operator()</a>(<a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;coord)<span class="keyword"> const </span>{</div><div class="line"><a name="l00598"></a><span class="lineno">  598</span>&#160;    <span class="keywordflow">return</span> layout_(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">PitchLinearCoord</a>(coord.<a class="code" href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">column</a>(), coord.<a class="code" href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">row</a>()));</div><div class="line"><a name="l00599"></a><span class="lineno">  599</span>&#160;  }</div><div class="line"><a name="l00600"></a><span class="lineno">  600</span>&#160;</div><div class="line"><a name="l00602"></a><span class="lineno">  602</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00603"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#a67e0749e995824bc35a8cde478f7c631">  603</a></span>&#160;  <a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <a class="code" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#a67e0749e995824bc35a8cde478f7c631">inverse</a>(<a class="code" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#ad49467d9028288b0bb93dbf376ef573a">LongIndex</a> offset)<span class="keyword"> const </span>{</div><div class="line"><a name="l00604"></a><span class="lineno">  604</span>&#160;    <a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">PitchLinearCoord</a> coord = layout_.<a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a6ef0968585fcb6bb564e1ea6c4bdc9a3">inverse</a>(offset);</div><div class="line"><a name="l00605"></a><span class="lineno">  605</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>(coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#aa828f8dbee3903754b56759c1e6a6043">strided</a>(), coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#adb31bc9b8cf49dfff64245b70a850834">contiguous</a>());</div><div class="line"><a name="l00606"></a><span class="lineno">  606</span>&#160;  }</div><div class="line"><a name="l00607"></a><span class="lineno">  607</span>&#160;</div><div class="line"><a name="l00609"></a><span class="lineno">  609</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00610"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#a080e343a32d097ab57d86362986cfb96">  610</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#a080e343a32d097ab57d86362986cfb96">stride</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00611"></a><span class="lineno">  611</span>&#160;    <span class="keywordflow">return</span> layout_.<a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a6b9e003e72d27841f42ee2e74689c632">stride</a>();</div><div class="line"><a name="l00612"></a><span class="lineno">  612</span>&#160;  }</div><div class="line"><a name="l00613"></a><span class="lineno">  613</span>&#160;</div><div class="line"><a name="l00615"></a><span class="lineno">  615</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00616"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#afcbbd0705f41dcc61aec0ba0ecaacc6a">  616</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> &amp; <a class="code" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#afcbbd0705f41dcc61aec0ba0ecaacc6a">stride</a>() {</div><div class="line"><a name="l00617"></a><span class="lineno">  617</span>&#160;    <span class="keywordflow">return</span> layout_.<a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a6b9e003e72d27841f42ee2e74689c632">stride</a>();</div><div class="line"><a name="l00618"></a><span class="lineno">  618</span>&#160;  }</div><div class="line"><a name="l00619"></a><span class="lineno">  619</span>&#160;</div><div class="line"><a name="l00621"></a><span class="lineno">  621</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00622"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#aa5e728fbc807f398363a43dafd0118f5">  622</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#ad49467d9028288b0bb93dbf376ef573a">LongIndex</a> <a class="code" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#aa5e728fbc807f398363a43dafd0118f5">capacity</a>(<a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent)<span class="keyword"> const </span>{</div><div class="line"><a name="l00623"></a><span class="lineno">  623</span>&#160;    <span class="keywordflow">return</span> layout_.<a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a016ea88480ec49f60b311b00e06dba54">capacity</a>(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">PitchLinearCoord</a>(extent.<a class="code" href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">column</a>(), extent.<a class="code" href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">row</a>()));</div><div class="line"><a name="l00624"></a><span class="lineno">  624</span>&#160;  }</div><div class="line"><a name="l00625"></a><span class="lineno">  625</span>&#160;};</div><div class="line"><a name="l00626"></a><span class="lineno">  626</span>&#160;</div><div class="line"><a name="l00628"></a><span class="lineno">  628</span>&#160;</div><div class="line"><a name="l00631"></a><span class="lineno">  631</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> ElementSize, <span class="keywordtype">int</span> Crosswise&gt;</div><div class="line"><a name="l00632"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html">  632</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html">TensorOpMultiplicandCrosswise</a> {</div><div class="line"><a name="l00634"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a6a0c039823054c3ce6b083634e38eb85">  634</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kRank = 2;</div><div class="line"><a name="l00635"></a><span class="lineno">  635</span>&#160;</div><div class="line"><a name="l00637"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#ad165f5ccdcd9a82efec86e830c03ba00">  637</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kStrideRank = 1;</div><div class="line"><a name="l00638"></a><span class="lineno">  638</span>&#160;</div><div class="line"><a name="l00640"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a4e8aa7fa89ae4ebd8afe284a9786df27">  640</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a4e8aa7fa89ae4ebd8afe284a9786df27">Index</a> = int32_t;</div><div class="line"><a name="l00641"></a><span class="lineno">  641</span>&#160;</div><div class="line"><a name="l00643"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a07de92ffadfe255537479c37b04cde6f">  643</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a07de92ffadfe255537479c37b04cde6f">LongIndex</a> = int64_t;</div><div class="line"><a name="l00644"></a><span class="lineno">  644</span>&#160;</div><div class="line"><a name="l00646"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#ab16e1b8b92486a3afbf6952e2b109835">  646</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">TensorCoord</a> = <a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">PitchLinearCoord</a>;</div><div class="line"><a name="l00647"></a><span class="lineno">  647</span>&#160;</div><div class="line"><a name="l00649"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a7e47b9ac97ed2df72445eff6c598ad0d">  649</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1Coord.html">Stride</a> = <a class="code" href="structcutlass_1_1Coord.html">Coord&lt;kStrideRank, Index, LongIndex&gt;</a>;</div><div class="line"><a name="l00650"></a><span class="lineno">  650</span>&#160;</div><div class="line"><a name="l00651"></a><span class="lineno">  651</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00652"></a><span class="lineno">  652</span>&#160;  <span class="comment">// Invariants</span></div><div class="line"><a name="l00653"></a><span class="lineno">  653</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00654"></a><span class="lineno">  654</span>&#160;</div><div class="line"><a name="l00655"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a1634bc35ab63daec869b61382543c764">  655</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html">Base</a> = <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html">TensorOpMultiplicand&lt;ElementSize, Crosswise&gt;</a>;</div><div class="line"><a name="l00656"></a><span class="lineno">  656</span>&#160;</div><div class="line"><a name="l00658"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a44e047e914c52edcfeb07bc3747c0b18">  658</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kAccessSize = Base::kAccessSize;</div><div class="line"><a name="l00659"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#aba317cad410b80b71b703eebe9367a06">  659</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#aba317cad410b80b71b703eebe9367a06">TileShape</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">Base::TileShape</a>;</div><div class="line"><a name="l00660"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a8ef78b952d245778ef8df144df21de2e">  660</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a8ef78b952d245778ef8df144df21de2e">PartitionShape</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">Base::PartitionShape</a>;</div><div class="line"><a name="l00661"></a><span class="lineno">  661</span>&#160;</div><div class="line"><a name="l00662"></a><span class="lineno">  662</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00663"></a><span class="lineno">  663</span>&#160;  <span class="comment">// Static constants</span></div><div class="line"><a name="l00664"></a><span class="lineno">  664</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00665"></a><span class="lineno">  665</span>&#160;</div><div class="line"><a name="l00666"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#af07f2e40a9ba698ed0e98ac449b3f61f">  666</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kElementSize = Base::kElementSize;</div><div class="line"><a name="l00667"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a11572d8b23d45062f1844286c9c63db6">  667</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kElementsPerAccess = Base::kElementsPerAccess;</div><div class="line"><a name="l00668"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a7b0e3438e68b0c121f40ff9c79ae8d51">  668</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kCrosswise = Base::kCrosswise;</div><div class="line"><a name="l00669"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a6f1f52234a9fb636ff84f2139ffa432b">  669</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kFactor = Base::kFactor;</div><div class="line"><a name="l00670"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a3ff5e9dcf1e98e074b40a2e06fa56df0">  670</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a3ff5e9dcf1e98e074b40a2e06fa56df0">PartitionCount</a> =  <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">Base::PartitionCount</a>;</div><div class="line"><a name="l00671"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#ae9c97a64123f074907e805d3642d924b">  671</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#ae9c97a64123f074907e805d3642d924b">AccessCount</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">Base::AccessCount</a>;</div><div class="line"><a name="l00672"></a><span class="lineno">  672</span>&#160;</div><div class="line"><a name="l00673"></a><span class="lineno">  673</span>&#160; <span class="keyword">private</span>:</div><div class="line"><a name="l00674"></a><span class="lineno">  674</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00675"></a><span class="lineno">  675</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00676"></a><span class="lineno">  676</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00677"></a><span class="lineno">  677</span>&#160;</div><div class="line"><a name="l00678"></a><span class="lineno">  678</span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html">Base</a> layout_;</div><div class="line"><a name="l00679"></a><span class="lineno">  679</span>&#160;</div><div class="line"><a name="l00680"></a><span class="lineno">  680</span>&#160; <span class="keyword">public</span>:</div><div class="line"><a name="l00681"></a><span class="lineno">  681</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00682"></a><span class="lineno">  682</span>&#160;  <span class="comment">// Methods</span></div><div class="line"><a name="l00683"></a><span class="lineno">  683</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00684"></a><span class="lineno">  684</span>&#160;</div><div class="line"><a name="l00686"></a><span class="lineno">  686</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00687"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a1ec5d2e030810801e5e5816916a1cf7f">  687</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a1ec5d2e030810801e5e5816916a1cf7f">TensorOpMultiplicandCrosswise</a>(<a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a4e8aa7fa89ae4ebd8afe284a9786df27">Index</a> ldm = 0) : layout_(ldm) {}</div><div class="line"><a name="l00688"></a><span class="lineno">  688</span>&#160;</div><div class="line"><a name="l00690"></a><span class="lineno">  690</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00691"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a34106f682f912c7d73ce03964e220859">  691</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a34106f682f912c7d73ce03964e220859">TensorOpMultiplicandCrosswise</a>(<a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a57baa00f66f3b3ad24dbc803186e40d4">stride</a>) : layout_(stride) {}</div><div class="line"><a name="l00692"></a><span class="lineno">  692</span>&#160;</div><div class="line"><a name="l00694"></a><span class="lineno">  694</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00695"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a1a69654a56446c0271037c5df7fbfc86">  695</a></span>&#160;  <span class="keyword">static</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html">TensorOpMultiplicandCrosswise</a> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a1a69654a56446c0271037c5df7fbfc86">packed</a>(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent) {</div><div class="line"><a name="l00696"></a><span class="lineno">  696</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html">TensorOpMultiplicandCrosswise</a>(extent[0]);</div><div class="line"><a name="l00697"></a><span class="lineno">  697</span>&#160;  }</div><div class="line"><a name="l00698"></a><span class="lineno">  698</span>&#160;</div><div class="line"><a name="l00701"></a><span class="lineno">  701</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00702"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#ab78912683077ccfdd5c664069de713f1">  702</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a07de92ffadfe255537479c37b04cde6f">LongIndex</a> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#ab78912683077ccfdd5c664069de713f1">operator()</a>(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;coord)<span class="keyword"> const </span>{</div><div class="line"><a name="l00703"></a><span class="lineno">  703</span>&#160;    <span class="keywordflow">return</span> layout_(coord);</div><div class="line"><a name="l00704"></a><span class="lineno">  704</span>&#160;  }</div><div class="line"><a name="l00705"></a><span class="lineno">  705</span>&#160;</div><div class="line"><a name="l00707"></a><span class="lineno">  707</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00708"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#ac4ff3d7bfc5303cb2dee432a7cc430da">  708</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">TensorCoord</a> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#ac4ff3d7bfc5303cb2dee432a7cc430da">inverse</a>(<a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a07de92ffadfe255537479c37b04cde6f">LongIndex</a> offset)<span class="keyword"> const </span>{</div><div class="line"><a name="l00709"></a><span class="lineno">  709</span>&#160;    <a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">PitchLinearCoord</a> coord = layout_.inverse(offset);</div><div class="line"><a name="l00710"></a><span class="lineno">  710</span>&#160;    <span class="keywordflow">return</span> coord;</div><div class="line"><a name="l00711"></a><span class="lineno">  711</span>&#160;  }</div><div class="line"><a name="l00712"></a><span class="lineno">  712</span>&#160;</div><div class="line"><a name="l00714"></a><span class="lineno">  714</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00715"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a51b6769545027ba9304cf9d1f1dd05f6">  715</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a51b6769545027ba9304cf9d1f1dd05f6">stride</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> layout_.<a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a57baa00f66f3b3ad24dbc803186e40d4">stride</a>(); }</div><div class="line"><a name="l00716"></a><span class="lineno">  716</span>&#160;</div><div class="line"><a name="l00718"></a><span class="lineno">  718</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00719"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a06294c449ba29798c1fbfc3baebb0b0c">  719</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> &amp;<a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a06294c449ba29798c1fbfc3baebb0b0c">stride</a>() { <span class="keywordflow">return</span> layout_.<a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a57baa00f66f3b3ad24dbc803186e40d4">stride</a>(); }</div><div class="line"><a name="l00720"></a><span class="lineno">  720</span>&#160;</div><div class="line"><a name="l00723"></a><span class="lineno">  723</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00724"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#abf4bd0dac231dfa83fc109420371fe8e">  724</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a07de92ffadfe255537479c37b04cde6f">LongIndex</a> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#abf4bd0dac231dfa83fc109420371fe8e">capacity</a>(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent)<span class="keyword"> const </span>{</div><div class="line"><a name="l00725"></a><span class="lineno">  725</span>&#160;    <span class="keywordflow">return</span> layout_.<a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a9ef7a95265d1602eb5d050eb89ad8b6b">capacity</a>(extent);</div><div class="line"><a name="l00726"></a><span class="lineno">  726</span>&#160;  }</div><div class="line"><a name="l00727"></a><span class="lineno">  727</span>&#160;};</div><div class="line"><a name="l00728"></a><span class="lineno">  728</span>&#160;</div><div class="line"><a name="l00730"></a><span class="lineno">  730</span>&#160;</div><div class="line"><a name="l00733"></a><span class="lineno">  733</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> ElementSize, <span class="keywordtype">int</span> Crosswise&gt;</div><div class="line"><a name="l00734"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html">  734</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html">ColumnMajorTensorOpMultiplicandCrosswise</a> {</div><div class="line"><a name="l00736"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#a4bc83e4719dfb686928aa85d603ff621">  736</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kRank = 2;</div><div class="line"><a name="l00737"></a><span class="lineno">  737</span>&#160;</div><div class="line"><a name="l00739"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#ad618ebb0d23785d18e56bc3b7663fe4f">  739</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kStrideRank = 1;</div><div class="line"><a name="l00740"></a><span class="lineno">  740</span>&#160;</div><div class="line"><a name="l00742"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#af9367fe9d6c5d95367d84dbab02dd0fe">  742</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#af9367fe9d6c5d95367d84dbab02dd0fe">Index</a> = int32_t;</div><div class="line"><a name="l00743"></a><span class="lineno">  743</span>&#160;</div><div class="line"><a name="l00745"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#a8e1f0b54a8c1b3f37df9ec0dd3548985">  745</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#a8e1f0b54a8c1b3f37df9ec0dd3548985">LongIndex</a> = int64_t;</div><div class="line"><a name="l00746"></a><span class="lineno">  746</span>&#160;</div><div class="line"><a name="l00748"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#a856809b4faa7252eccaa767fa2e0551b">  748</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> = <a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>;</div><div class="line"><a name="l00749"></a><span class="lineno">  749</span>&#160;</div><div class="line"><a name="l00751"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#a9655b96e34806a56586bd8dfa542064f">  751</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1Coord.html">Stride</a> = <a class="code" href="structcutlass_1_1Coord.html">Coord&lt;kStrideRank, Index, LongIndex&gt;</a>;</div><div class="line"><a name="l00752"></a><span class="lineno">  752</span>&#160;</div><div class="line"><a name="l00753"></a><span class="lineno">  753</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00754"></a><span class="lineno">  754</span>&#160;  <span class="comment">// Invariants</span></div><div class="line"><a name="l00755"></a><span class="lineno">  755</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00756"></a><span class="lineno">  756</span>&#160;</div><div class="line"><a name="l00757"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#a9c459438e8660304b6f75bde269cf958">  757</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html">Base</a> = <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html">TensorOpMultiplicandCrosswise&lt;ElementSize, Crosswise&gt;</a>;</div><div class="line"><a name="l00758"></a><span class="lineno">  758</span>&#160;</div><div class="line"><a name="l00760"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#a31869d096511d5559f3ae1ff5671207f">  760</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kAccessSize = Base::kAccessSize;</div><div class="line"><a name="l00761"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#a60953e9a77d09691d2320df1a9ad3f3e">  761</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#a60953e9a77d09691d2320df1a9ad3f3e">TileShape</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#aba317cad410b80b71b703eebe9367a06">Base::TileShape</a>;</div><div class="line"><a name="l00762"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#a8de62d289c4d9e3123736e42b4c3b33d">  762</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#a8de62d289c4d9e3123736e42b4c3b33d">PartitionShape</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a8ef78b952d245778ef8df144df21de2e">Base::PartitionShape</a>;</div><div class="line"><a name="l00763"></a><span class="lineno">  763</span>&#160;</div><div class="line"><a name="l00764"></a><span class="lineno">  764</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00765"></a><span class="lineno">  765</span>&#160;  <span class="comment">// Static constants</span></div><div class="line"><a name="l00766"></a><span class="lineno">  766</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00767"></a><span class="lineno">  767</span>&#160;</div><div class="line"><a name="l00768"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#a6bd031a9b730ba2a609eef0457994524">  768</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kElementSize = Base::kElementSize;</div><div class="line"><a name="l00769"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#a31427122376ca36d9313e06664b0ee8a">  769</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kElementsPerAccess = Base::kElementsPerAccess;</div><div class="line"><a name="l00770"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#afe8f9e93641b51a0172a0a724e6cfb9c">  770</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#afe8f9e93641b51a0172a0a724e6cfb9c">PartitionCount</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a3ff5e9dcf1e98e074b40a2e06fa56df0">Base::PartitionCount</a>;</div><div class="line"><a name="l00771"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#ab447aac95eab1ad7d24767d4657990a4">  771</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#ab447aac95eab1ad7d24767d4657990a4">AccessCount</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#ae9c97a64123f074907e805d3642d924b">Base::AccessCount</a>;</div><div class="line"><a name="l00772"></a><span class="lineno">  772</span>&#160;</div><div class="line"><a name="l00773"></a><span class="lineno">  773</span>&#160; <span class="keyword">private</span>:</div><div class="line"><a name="l00774"></a><span class="lineno">  774</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00775"></a><span class="lineno">  775</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00776"></a><span class="lineno">  776</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00777"></a><span class="lineno">  777</span>&#160;</div><div class="line"><a name="l00778"></a><span class="lineno">  778</span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html">Base</a> layout_;</div><div class="line"><a name="l00779"></a><span class="lineno">  779</span>&#160;</div><div class="line"><a name="l00780"></a><span class="lineno">  780</span>&#160; <span class="keyword">public</span>:</div><div class="line"><a name="l00781"></a><span class="lineno">  781</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00782"></a><span class="lineno">  782</span>&#160;  <span class="comment">// Methods</span></div><div class="line"><a name="l00783"></a><span class="lineno">  783</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00784"></a><span class="lineno">  784</span>&#160;</div><div class="line"><a name="l00786"></a><span class="lineno">  786</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00787"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#ae9010e219ceb098de21d2673a9112b50">  787</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#ae9010e219ceb098de21d2673a9112b50">ColumnMajorTensorOpMultiplicandCrosswise</a>(<a class="code" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#af9367fe9d6c5d95367d84dbab02dd0fe">Index</a> ldm = 0) : layout_(ldm) {}</div><div class="line"><a name="l00788"></a><span class="lineno">  788</span>&#160;</div><div class="line"><a name="l00790"></a><span class="lineno">  790</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00791"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#a77befaca4f7609243842a4e54d50d010">  791</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#a77befaca4f7609243842a4e54d50d010">ColumnMajorTensorOpMultiplicandCrosswise</a>(<a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a57baa00f66f3b3ad24dbc803186e40d4">stride</a>) : layout_(stride) {}</div><div class="line"><a name="l00792"></a><span class="lineno">  792</span>&#160;</div><div class="line"><a name="l00794"></a><span class="lineno">  794</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00795"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#afd79156b28c636812e72d0fcccd455e7">  795</a></span>&#160;  <span class="keyword">static</span> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html">ColumnMajorTensorOpMultiplicandCrosswise</a> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#afd79156b28c636812e72d0fcccd455e7">packed</a>(</div><div class="line"><a name="l00796"></a><span class="lineno">  796</span>&#160;      <a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent) {</div><div class="line"><a name="l00797"></a><span class="lineno">  797</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html">ColumnMajorTensorOpMultiplicandCrosswise</a>(extent.<a class="code" href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">row</a>());</div><div class="line"><a name="l00798"></a><span class="lineno">  798</span>&#160;  }</div><div class="line"><a name="l00799"></a><span class="lineno">  799</span>&#160;</div><div class="line"><a name="l00802"></a><span class="lineno">  802</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00803"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#a8adbc25064e0edb48c429a59008f6d6a">  803</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#a8e1f0b54a8c1b3f37df9ec0dd3548985">LongIndex</a> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#a8adbc25064e0edb48c429a59008f6d6a">operator()</a>(<a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;coord)<span class="keyword"> const </span>{</div><div class="line"><a name="l00804"></a><span class="lineno">  804</span>&#160;    <span class="keywordflow">return</span> layout_(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">PitchLinearCoord</a>(coord.<a class="code" href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">row</a>(), coord.<a class="code" href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">column</a>()));</div><div class="line"><a name="l00805"></a><span class="lineno">  805</span>&#160;  }</div><div class="line"><a name="l00806"></a><span class="lineno">  806</span>&#160;</div><div class="line"><a name="l00808"></a><span class="lineno">  808</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00809"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#a09eaa824aea4b98af2e71c14f572fd56">  809</a></span>&#160;  <a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#a09eaa824aea4b98af2e71c14f572fd56">inverse</a>(<a class="code" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#a8e1f0b54a8c1b3f37df9ec0dd3548985">LongIndex</a> offset)<span class="keyword"> const </span>{</div><div class="line"><a name="l00810"></a><span class="lineno">  810</span>&#160;    <a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">PitchLinearCoord</a> coord = layout_.<a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#ac4ff3d7bfc5303cb2dee432a7cc430da">inverse</a>(offset);</div><div class="line"><a name="l00811"></a><span class="lineno">  811</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>(coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#adb31bc9b8cf49dfff64245b70a850834">contiguous</a>(), coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#aa828f8dbee3903754b56759c1e6a6043">strided</a>());</div><div class="line"><a name="l00812"></a><span class="lineno">  812</span>&#160;  }</div><div class="line"><a name="l00813"></a><span class="lineno">  813</span>&#160;</div><div class="line"><a name="l00815"></a><span class="lineno">  815</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00816"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#aa7c6cfa96263de67b132a54010f36189">  816</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#aa7c6cfa96263de67b132a54010f36189">stride</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> layout_.<a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a51b6769545027ba9304cf9d1f1dd05f6">stride</a>(); }</div><div class="line"><a name="l00817"></a><span class="lineno">  817</span>&#160;</div><div class="line"><a name="l00819"></a><span class="lineno">  819</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00820"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#ad1b74e4f166e09476444c56fec627504">  820</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> &amp;<a class="code" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#ad1b74e4f166e09476444c56fec627504">stride</a>() { <span class="keywordflow">return</span> layout_.<a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a51b6769545027ba9304cf9d1f1dd05f6">stride</a>(); }</div><div class="line"><a name="l00821"></a><span class="lineno">  821</span>&#160;</div><div class="line"><a name="l00824"></a><span class="lineno">  824</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00825"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#afeba15c02217a7e06c3cb96c7bef2bd0">  825</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#a8e1f0b54a8c1b3f37df9ec0dd3548985">LongIndex</a> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#afeba15c02217a7e06c3cb96c7bef2bd0">capacity</a>(<a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent)<span class="keyword"> const </span>{</div><div class="line"><a name="l00826"></a><span class="lineno">  826</span>&#160;    <span class="keywordflow">return</span> layout_.<a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#abf4bd0dac231dfa83fc109420371fe8e">capacity</a>(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">PitchLinearCoord</a>(extent.<a class="code" href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">row</a>(), extent.<a class="code" href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">column</a>()));</div><div class="line"><a name="l00827"></a><span class="lineno">  827</span>&#160;  }</div><div class="line"><a name="l00828"></a><span class="lineno">  828</span>&#160;};</div><div class="line"><a name="l00829"></a><span class="lineno">  829</span>&#160;</div><div class="line"><a name="l00831"></a><span class="lineno">  831</span>&#160;</div><div class="line"><a name="l00834"></a><span class="lineno">  834</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> ElementSize, <span class="keywordtype">int</span> Crosswise&gt;</div><div class="line"><a name="l00835"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html">  835</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html">RowMajorTensorOpMultiplicandCrosswise</a> {</div><div class="line"><a name="l00837"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#a5d70f2d7475c0f34dc0d7f2b7ccc3a5c">  837</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kRank = 2;</div><div class="line"><a name="l00838"></a><span class="lineno">  838</span>&#160;</div><div class="line"><a name="l00840"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#ad38dac4a905cd2074f8c33da3b46954e">  840</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kStrideRank = 1;</div><div class="line"><a name="l00841"></a><span class="lineno">  841</span>&#160;</div><div class="line"><a name="l00843"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#afb16d769e699fe88e09420cce6096d86">  843</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#afb16d769e699fe88e09420cce6096d86">Index</a> = int32_t;</div><div class="line"><a name="l00844"></a><span class="lineno">  844</span>&#160;</div><div class="line"><a name="l00846"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#a85b214a913b534fc9cc5366664a414e3">  846</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#a85b214a913b534fc9cc5366664a414e3">LongIndex</a> = int64_t;</div><div class="line"><a name="l00847"></a><span class="lineno">  847</span>&#160;</div><div class="line"><a name="l00849"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#af4216b9559628b14f0bb6c50ffb905f1">  849</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> = <a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>;</div><div class="line"><a name="l00850"></a><span class="lineno">  850</span>&#160;</div><div class="line"><a name="l00852"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#ab24dff4dcddf3ef77b9b5a9acd369dd8">  852</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1Coord.html">Stride</a> = <a class="code" href="structcutlass_1_1Coord.html">Coord&lt;kStrideRank, Index, LongIndex&gt;</a>;</div><div class="line"><a name="l00853"></a><span class="lineno">  853</span>&#160;</div><div class="line"><a name="l00854"></a><span class="lineno">  854</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00855"></a><span class="lineno">  855</span>&#160;  <span class="comment">// Invariants</span></div><div class="line"><a name="l00856"></a><span class="lineno">  856</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00857"></a><span class="lineno">  857</span>&#160;</div><div class="line"><a name="l00858"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#aaf70fbe057aede83fa9b66ea84d1f687">  858</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html">Base</a> = <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html">TensorOpMultiplicandCrosswise&lt;ElementSize, Crosswise&gt;</a>;</div><div class="line"><a name="l00859"></a><span class="lineno">  859</span>&#160;</div><div class="line"><a name="l00861"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#a9a93000ebe759669c1e54564b1266961">  861</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kAccessSize = Base::kAccessSize;</div><div class="line"><a name="l00862"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#a860acbb4cce4be26a6a72351b832414e">  862</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#a860acbb4cce4be26a6a72351b832414e">TileShape</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#aba317cad410b80b71b703eebe9367a06">Base::TileShape</a>;</div><div class="line"><a name="l00863"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#abdbf2109359d6c902676eb3519484ab2">  863</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#abdbf2109359d6c902676eb3519484ab2">PartitionShape</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a8ef78b952d245778ef8df144df21de2e">Base::PartitionShape</a>;</div><div class="line"><a name="l00864"></a><span class="lineno">  864</span>&#160;</div><div class="line"><a name="l00865"></a><span class="lineno">  865</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00866"></a><span class="lineno">  866</span>&#160;  <span class="comment">// Static constants</span></div><div class="line"><a name="l00867"></a><span class="lineno">  867</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00868"></a><span class="lineno">  868</span>&#160;</div><div class="line"><a name="l00869"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#a07b5e619df35284decc0941c6f860e63">  869</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kElementSize = Base::kElementSize;</div><div class="line"><a name="l00870"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#ab58ab635041661f1a77c5373ca9ee48d">  870</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kElementsPerAccess = Base::kElementsPerAccess;</div><div class="line"><a name="l00871"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#ac0f9fe7e728edb0eff201fd1c356db4a">  871</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#ac0f9fe7e728edb0eff201fd1c356db4a">PartitionCount</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a3ff5e9dcf1e98e074b40a2e06fa56df0">Base::PartitionCount</a>;</div><div class="line"><a name="l00872"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#ab7adede333905f5e87178fee4c0d530c">  872</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#ab7adede333905f5e87178fee4c0d530c">AccessCount</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#ae9c97a64123f074907e805d3642d924b">Base::AccessCount</a>;</div><div class="line"><a name="l00873"></a><span class="lineno">  873</span>&#160;</div><div class="line"><a name="l00874"></a><span class="lineno">  874</span>&#160; <span class="keyword">private</span>:</div><div class="line"><a name="l00875"></a><span class="lineno">  875</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00876"></a><span class="lineno">  876</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00877"></a><span class="lineno">  877</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00878"></a><span class="lineno">  878</span>&#160;</div><div class="line"><a name="l00879"></a><span class="lineno">  879</span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html">Base</a> layout_;</div><div class="line"><a name="l00880"></a><span class="lineno">  880</span>&#160;</div><div class="line"><a name="l00881"></a><span class="lineno">  881</span>&#160; <span class="keyword">public</span>:</div><div class="line"><a name="l00882"></a><span class="lineno">  882</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00883"></a><span class="lineno">  883</span>&#160;  <span class="comment">// Methods</span></div><div class="line"><a name="l00884"></a><span class="lineno">  884</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00885"></a><span class="lineno">  885</span>&#160;</div><div class="line"><a name="l00887"></a><span class="lineno">  887</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00888"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#a90513da18bdcebc20f673d83031551f9">  888</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#a90513da18bdcebc20f673d83031551f9">RowMajorTensorOpMultiplicandCrosswise</a>(<a class="code" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#afb16d769e699fe88e09420cce6096d86">Index</a> ldm = 0) : layout_(ldm) {}</div><div class="line"><a name="l00889"></a><span class="lineno">  889</span>&#160;</div><div class="line"><a name="l00891"></a><span class="lineno">  891</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00892"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#afe4b1ec877e6f1266e4e57a862d43767">  892</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#afe4b1ec877e6f1266e4e57a862d43767">RowMajorTensorOpMultiplicandCrosswise</a>(<a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a57baa00f66f3b3ad24dbc803186e40d4">stride</a>) : layout_(stride) {}</div><div class="line"><a name="l00893"></a><span class="lineno">  893</span>&#160;</div><div class="line"><a name="l00895"></a><span class="lineno">  895</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00896"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#a5b3ff99ea22d5d6c5bce9aa9ab228b46">  896</a></span>&#160;  <span class="keyword">static</span> <a class="code" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html">RowMajorTensorOpMultiplicandCrosswise</a> <a class="code" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#a5b3ff99ea22d5d6c5bce9aa9ab228b46">packed</a>(</div><div class="line"><a name="l00897"></a><span class="lineno">  897</span>&#160;      <a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent) {</div><div class="line"><a name="l00898"></a><span class="lineno">  898</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html">RowMajorTensorOpMultiplicandCrosswise</a>(extent.<a class="code" href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">column</a>());</div><div class="line"><a name="l00899"></a><span class="lineno">  899</span>&#160;  }</div><div class="line"><a name="l00900"></a><span class="lineno">  900</span>&#160;</div><div class="line"><a name="l00903"></a><span class="lineno">  903</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00904"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#a7f702111a44362cf73b81c11a1907b3a">  904</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#a85b214a913b534fc9cc5366664a414e3">LongIndex</a> <a class="code" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#a7f702111a44362cf73b81c11a1907b3a">operator()</a>(<a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;coord)<span class="keyword"> const </span>{</div><div class="line"><a name="l00905"></a><span class="lineno">  905</span>&#160;    <span class="keywordflow">return</span> layout_(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">PitchLinearCoord</a>(coord.<a class="code" href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">column</a>(), coord.<a class="code" href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">row</a>()));</div><div class="line"><a name="l00906"></a><span class="lineno">  906</span>&#160;  }</div><div class="line"><a name="l00907"></a><span class="lineno">  907</span>&#160;</div><div class="line"><a name="l00909"></a><span class="lineno">  909</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00910"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#a1fa92e43f3d6b1874f9fe5c1f87b8ee0">  910</a></span>&#160;  <a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <a class="code" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#a1fa92e43f3d6b1874f9fe5c1f87b8ee0">inverse</a>(<a class="code" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#a85b214a913b534fc9cc5366664a414e3">LongIndex</a> offset)<span class="keyword"> const </span>{</div><div class="line"><a name="l00911"></a><span class="lineno">  911</span>&#160;    <a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">PitchLinearCoord</a> coord = layout_.<a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#ac4ff3d7bfc5303cb2dee432a7cc430da">inverse</a>(offset);</div><div class="line"><a name="l00912"></a><span class="lineno">  912</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>(coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#aa828f8dbee3903754b56759c1e6a6043">strided</a>(), coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#adb31bc9b8cf49dfff64245b70a850834">contiguous</a>());</div><div class="line"><a name="l00913"></a><span class="lineno">  913</span>&#160;  }</div><div class="line"><a name="l00914"></a><span class="lineno">  914</span>&#160;</div><div class="line"><a name="l00916"></a><span class="lineno">  916</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00917"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#add5bc9679d4ad1bf2ee2c5b81c8ff0ca">  917</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#add5bc9679d4ad1bf2ee2c5b81c8ff0ca">stride</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> layout_.<a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a51b6769545027ba9304cf9d1f1dd05f6">stride</a>(); }</div><div class="line"><a name="l00918"></a><span class="lineno">  918</span>&#160;</div><div class="line"><a name="l00920"></a><span class="lineno">  920</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00921"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#a04d8e6cbf2723e8afaa7fd0e5f18c66f">  921</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> &amp;<a class="code" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#a04d8e6cbf2723e8afaa7fd0e5f18c66f">stride</a>() { <span class="keywordflow">return</span> layout_.<a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a51b6769545027ba9304cf9d1f1dd05f6">stride</a>(); }</div><div class="line"><a name="l00922"></a><span class="lineno">  922</span>&#160;</div><div class="line"><a name="l00925"></a><span class="lineno">  925</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00926"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#a04e4211bb2e725d434371b2cf1de696e">  926</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#a85b214a913b534fc9cc5366664a414e3">LongIndex</a> <a class="code" href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#a04e4211bb2e725d434371b2cf1de696e">capacity</a>(<a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent)<span class="keyword"> const </span>{</div><div class="line"><a name="l00927"></a><span class="lineno">  927</span>&#160;    <span class="keywordflow">return</span> layout_.<a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#abf4bd0dac231dfa83fc109420371fe8e">capacity</a>(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">PitchLinearCoord</a>(extent.<a class="code" href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">column</a>(), extent.<a class="code" href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">row</a>()));</div><div class="line"><a name="l00928"></a><span class="lineno">  928</span>&#160;  }</div><div class="line"><a name="l00929"></a><span class="lineno">  929</span>&#160;};</div><div class="line"><a name="l00930"></a><span class="lineno">  930</span>&#160;</div><div class="line"><a name="l00932"></a><span class="lineno">  932</span>&#160;</div><div class="line"><a name="l00934"></a><span class="lineno">  934</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> ElementSize, <span class="keywordtype">int</span> InterleavedK&gt;</div><div class="line"><a name="l00935"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html">  935</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html">TensorOpMultiplicandColumnMajorInterleaved</a> {</div><div class="line"><a name="l00936"></a><span class="lineno">  936</span>&#160;</div><div class="line"><a name="l00938"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#a6006c012512fa5b46e37fef97d9f23cb">  938</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kRank = 2;</div><div class="line"><a name="l00939"></a><span class="lineno">  939</span>&#160;</div><div class="line"><a name="l00941"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#a202a4b11a76201686a1f33b458ceb715">  941</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kStrideRank = 1;</div><div class="line"><a name="l00942"></a><span class="lineno">  942</span>&#160;</div><div class="line"><a name="l00944"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#a20c2a57c8a1c6224de7253cfb7239134">  944</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#a20c2a57c8a1c6224de7253cfb7239134">Index</a> = int32_t;</div><div class="line"><a name="l00945"></a><span class="lineno">  945</span>&#160;</div><div class="line"><a name="l00947"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#a6c2a122f2d16f1ca7957d7b0749248fc">  947</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#a6c2a122f2d16f1ca7957d7b0749248fc">LongIndex</a> = int64_t;</div><div class="line"><a name="l00948"></a><span class="lineno">  948</span>&#160;</div><div class="line"><a name="l00950"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#a6690936279f3c6d6f29ea8ea00421870">  950</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">TensorCoord</a> = <a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">PitchLinearCoord</a>;</div><div class="line"><a name="l00951"></a><span class="lineno">  951</span>&#160;</div><div class="line"><a name="l00953"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#a13709234cb6cbad624fe40d51a792883">  953</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1Coord.html">Stride</a> = <a class="code" href="structcutlass_1_1Coord.html">Coord&lt;kStrideRank, Index, LongIndex&gt;</a>;</div><div class="line"><a name="l00954"></a><span class="lineno">  954</span>&#160;</div><div class="line"><a name="l00955"></a><span class="lineno">  955</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00956"></a><span class="lineno">  956</span>&#160;  <span class="comment">// Invariants</span></div><div class="line"><a name="l00957"></a><span class="lineno">  957</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00958"></a><span class="lineno">  958</span>&#160;</div><div class="line"><a name="l00960"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#af6c844d6ab958e5e32dfdfb452de1578">  960</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kAccessSize = 128;</div><div class="line"><a name="l00961"></a><span class="lineno">  961</span>&#160;</div><div class="line"><a name="l00962"></a><span class="lineno">  962</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00963"></a><span class="lineno">  963</span>&#160;  <span class="comment">// Static constants</span></div><div class="line"><a name="l00964"></a><span class="lineno">  964</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00965"></a><span class="lineno">  965</span>&#160;</div><div class="line"><a name="l00966"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#a105bcb898baf9d42df209f1a6f60ecf2">  966</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kElementSize = ElementSize;</div><div class="line"><a name="l00967"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#a6c0474f980cd125cbcd8b7f65bb004f5">  967</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kElementsPerAccess = kAccessSize / <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a1f35f08a131d76521a98c391acedb4e6">kElementSize</a>;</div><div class="line"><a name="l00968"></a><span class="lineno">  968</span>&#160;</div><div class="line"><a name="l00969"></a><span class="lineno">  969</span>&#160;  <span class="comment">//static int const kThreadBlockStrided = ThreadBlockStrided;</span></div><div class="line"><a name="l00970"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#aec2c91247bbff7c4abb974a6d30ae202">  970</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kInterleavedK = InterleavedK;</div><div class="line"><a name="l00971"></a><span class="lineno">  971</span>&#160;  </div><div class="line"><a name="l00972"></a><span class="lineno">  972</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00973"></a><span class="lineno">  973</span>&#160;</div><div class="line"><a name="l00974"></a><span class="lineno">  974</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00975"></a><span class="lineno">  975</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00976"></a><span class="lineno">  976</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00977"></a><span class="lineno">  977</span>&#160;</div><div class="line"><a name="l00979"></a><span class="lineno">  979</span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> stride_;</div><div class="line"><a name="l00980"></a><span class="lineno">  980</span>&#160;</div><div class="line"><a name="l00981"></a><span class="lineno">  981</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00982"></a><span class="lineno">  982</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00983"></a><span class="lineno">  983</span>&#160;  <span class="comment">// Methods</span></div><div class="line"><a name="l00984"></a><span class="lineno">  984</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00985"></a><span class="lineno">  985</span>&#160;</div><div class="line"><a name="l00987"></a><span class="lineno">  987</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00988"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#ac8d018056fa3e2993f4b85cfd97c2800">  988</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#ac8d018056fa3e2993f4b85cfd97c2800">TensorOpMultiplicandColumnMajorInterleaved</a>(<a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#a20c2a57c8a1c6224de7253cfb7239134">Index</a> ldm = 0): stride_(ldm) { }</div><div class="line"><a name="l00989"></a><span class="lineno">  989</span>&#160;</div><div class="line"><a name="l00991"></a><span class="lineno">  991</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00992"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#a9aa34a2e71b4cbcc412c2a41f0e1c1c2">  992</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#a9aa34a2e71b4cbcc412c2a41f0e1c1c2">TensorOpMultiplicandColumnMajorInterleaved</a>(<a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a57baa00f66f3b3ad24dbc803186e40d4">stride</a>): stride_(stride) { }</div><div class="line"><a name="l00993"></a><span class="lineno">  993</span>&#160;</div><div class="line"><a name="l00995"></a><span class="lineno">  995</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00996"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#a559487c37a4387d88726a51cd08b17b7">  996</a></span>&#160;  <span class="keyword">static</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html">TensorOpMultiplicandColumnMajorInterleaved</a> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#a559487c37a4387d88726a51cd08b17b7">packed</a>(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent) {</div><div class="line"><a name="l00997"></a><span class="lineno">  997</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html">TensorOpMultiplicandColumnMajorInterleaved</a>(extent[0] * kInterleavedK);</div><div class="line"><a name="l00998"></a><span class="lineno">  998</span>&#160;  }</div><div class="line"><a name="l00999"></a><span class="lineno">  999</span>&#160;</div><div class="line"><a name="l01002"></a><span class="lineno"> 1002</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l01003"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#ad20058caa6ef3c9f540a33fea56ac0d3"> 1003</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#a6c2a122f2d16f1ca7957d7b0749248fc">LongIndex</a> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#ad20058caa6ef3c9f540a33fea56ac0d3">operator()</a>(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;coord)<span class="keyword"> const </span>{</div><div class="line"><a name="l01004"></a><span class="lineno"> 1004</span>&#160;    <span class="keywordtype">int</span> <span class="keyword">const</span> rows_per_smem_cache_line = 128 / kInterleavedK;</div><div class="line"><a name="l01005"></a><span class="lineno"> 1005</span>&#160;</div><div class="line"><a name="l01006"></a><span class="lineno"> 1006</span>&#160;    <span class="keywordtype">int</span> row_id = coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#aa828f8dbee3903754b56759c1e6a6043">strided</a>() / rows_per_smem_cache_line;</div><div class="line"><a name="l01007"></a><span class="lineno"> 1007</span>&#160;    <span class="keywordtype">int</span> col_id = (coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#aa828f8dbee3903754b56759c1e6a6043">strided</a>() % rows_per_smem_cache_line) * kInterleavedK + coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#adb31bc9b8cf49dfff64245b70a850834">contiguous</a>();</div><div class="line"><a name="l01008"></a><span class="lineno"> 1008</span>&#160;</div><div class="line"><a name="l01009"></a><span class="lineno"> 1009</span>&#160;    <span class="keywordtype">int</span> access_block_id = col_id &gt;&gt; 4;</div><div class="line"><a name="l01010"></a><span class="lineno"> 1010</span>&#160;    <span class="keywordtype">int</span> swizzle_access_block_id = access_block_id ^ (row_id &amp; 1);</div><div class="line"><a name="l01011"></a><span class="lineno"> 1011</span>&#160;</div><div class="line"><a name="l01012"></a><span class="lineno"> 1012</span>&#160;    <span class="keywordtype">int</span> swizzle_col_id = swizzle_access_block_id &lt;&lt; 4;</div><div class="line"><a name="l01013"></a><span class="lineno"> 1013</span>&#160;</div><div class="line"><a name="l01014"></a><span class="lineno"> 1014</span>&#160;    <span class="keywordflow">return</span> row_id * 128 + swizzle_col_id;</div><div class="line"><a name="l01015"></a><span class="lineno"> 1015</span>&#160;  }</div><div class="line"><a name="l01016"></a><span class="lineno"> 1016</span>&#160;</div><div class="line"><a name="l01018"></a><span class="lineno"> 1018</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l01019"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#a5a04695e79ca5ef76abd105edf6b208a"> 1019</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#a5a04695e79ca5ef76abd105edf6b208a">stride</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l01020"></a><span class="lineno"> 1020</span>&#160;    <span class="keywordflow">return</span> stride_;</div><div class="line"><a name="l01021"></a><span class="lineno"> 1021</span>&#160;  }</div><div class="line"><a name="l01022"></a><span class="lineno"> 1022</span>&#160;</div><div class="line"><a name="l01024"></a><span class="lineno"> 1024</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l01025"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#ac59716a33dc9f73d18ef54649f07e2c6"> 1025</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> &amp; <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#ac59716a33dc9f73d18ef54649f07e2c6">stride</a>() {</div><div class="line"><a name="l01026"></a><span class="lineno"> 1026</span>&#160;    <span class="keywordflow">return</span> stride_;</div><div class="line"><a name="l01027"></a><span class="lineno"> 1027</span>&#160;  }</div><div class="line"><a name="l01028"></a><span class="lineno"> 1028</span>&#160;</div><div class="line"><a name="l01030"></a><span class="lineno"> 1030</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l01031"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#a68ec1121f75551e44ef28edc17763179"> 1031</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#a6c2a122f2d16f1ca7957d7b0749248fc">LongIndex</a> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#a68ec1121f75551e44ef28edc17763179">capacity</a>(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent)<span class="keyword"> const </span>{</div><div class="line"><a name="l01032"></a><span class="lineno"> 1032</span>&#160;    <span class="keywordflow">return</span> (extent[1] / kInterleavedK) * stride_[0];</div><div class="line"><a name="l01033"></a><span class="lineno"> 1033</span>&#160;  }</div><div class="line"><a name="l01034"></a><span class="lineno"> 1034</span>&#160;};</div><div class="line"><a name="l01035"></a><span class="lineno"> 1035</span>&#160;</div><div class="line"><a name="l01037"></a><span class="lineno"> 1037</span>&#160;</div><div class="line"><a name="l01039"></a><span class="lineno"> 1039</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> ElementSize, <span class="keywordtype">int</span> InterleavedK&gt;</div><div class="line"><a name="l01040"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html"> 1040</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html">TensorOpMultiplicandRowMajorInterleaved</a> {</div><div class="line"><a name="l01041"></a><span class="lineno"> 1041</span>&#160;</div><div class="line"><a name="l01043"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#aed810950826eeca7eb63ef7bf8c45568"> 1043</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kRank = 2;</div><div class="line"><a name="l01044"></a><span class="lineno"> 1044</span>&#160;</div><div class="line"><a name="l01046"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#a72aed2ba4379167f238e139801ce0816"> 1046</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kStrideRank = 1;</div><div class="line"><a name="l01047"></a><span class="lineno"> 1047</span>&#160;</div><div class="line"><a name="l01049"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#a149aa646854cd7591c7ee528eb5ea3ab"> 1049</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#a149aa646854cd7591c7ee528eb5ea3ab">Index</a> = int32_t;</div><div class="line"><a name="l01050"></a><span class="lineno"> 1050</span>&#160;</div><div class="line"><a name="l01052"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#ace9bb56ad8e186a09e07a44f96ba8b6e"> 1052</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#ace9bb56ad8e186a09e07a44f96ba8b6e">LongIndex</a> = int64_t;</div><div class="line"><a name="l01053"></a><span class="lineno"> 1053</span>&#160;</div><div class="line"><a name="l01055"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#af6d6d872ef18cae50f64e2bf6704f820"> 1055</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">TensorCoord</a> = <a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">PitchLinearCoord</a>;</div><div class="line"><a name="l01056"></a><span class="lineno"> 1056</span>&#160;</div><div class="line"><a name="l01058"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#a6a34a33c36aee7013ccc31783c4e999c"> 1058</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1Coord.html">Stride</a> = <a class="code" href="structcutlass_1_1Coord.html">Coord&lt;kStrideRank, Index, LongIndex&gt;</a>;</div><div class="line"><a name="l01059"></a><span class="lineno"> 1059</span>&#160;</div><div class="line"><a name="l01060"></a><span class="lineno"> 1060</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l01061"></a><span class="lineno"> 1061</span>&#160;  <span class="comment">// Invariants</span></div><div class="line"><a name="l01062"></a><span class="lineno"> 1062</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l01063"></a><span class="lineno"> 1063</span>&#160;</div><div class="line"><a name="l01065"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#aaa4ff06d1ef03d1a0b9e07d9af1c9323"> 1065</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kAccessSize = 128;</div><div class="line"><a name="l01066"></a><span class="lineno"> 1066</span>&#160;</div><div class="line"><a name="l01067"></a><span class="lineno"> 1067</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l01068"></a><span class="lineno"> 1068</span>&#160;  <span class="comment">// Static constants</span></div><div class="line"><a name="l01069"></a><span class="lineno"> 1069</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l01070"></a><span class="lineno"> 1070</span>&#160;</div><div class="line"><a name="l01071"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#aba9287009e012b428198acab4ad053f9"> 1071</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kElementSize = ElementSize;</div><div class="line"><a name="l01072"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#a098afbb5810784378b875b41966687ee"> 1072</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kElementsPerAccess = kAccessSize / <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a1f35f08a131d76521a98c391acedb4e6">kElementSize</a>;</div><div class="line"><a name="l01073"></a><span class="lineno"> 1073</span>&#160;</div><div class="line"><a name="l01074"></a><span class="lineno"> 1074</span>&#160;  <span class="comment">//static int const kThreadBlockStrided = ThreadBlockStrided;</span></div><div class="line"><a name="l01075"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#a25870e5c3148680a6a0c1b2a04b220d4"> 1075</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kInterleavedK = InterleavedK;</div><div class="line"><a name="l01076"></a><span class="lineno"> 1076</span>&#160;  </div><div class="line"><a name="l01077"></a><span class="lineno"> 1077</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l01078"></a><span class="lineno"> 1078</span>&#160;</div><div class="line"><a name="l01079"></a><span class="lineno"> 1079</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l01080"></a><span class="lineno"> 1080</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l01081"></a><span class="lineno"> 1081</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l01082"></a><span class="lineno"> 1082</span>&#160;</div><div class="line"><a name="l01084"></a><span class="lineno"> 1084</span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> stride_;</div><div class="line"><a name="l01085"></a><span class="lineno"> 1085</span>&#160;</div><div class="line"><a name="l01086"></a><span class="lineno"> 1086</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l01087"></a><span class="lineno"> 1087</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l01088"></a><span class="lineno"> 1088</span>&#160;  <span class="comment">// Methods</span></div><div class="line"><a name="l01089"></a><span class="lineno"> 1089</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l01090"></a><span class="lineno"> 1090</span>&#160;</div><div class="line"><a name="l01092"></a><span class="lineno"> 1092</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l01093"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#ad925d2b3c135bca79f802d53e4c31a6e"> 1093</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#ad925d2b3c135bca79f802d53e4c31a6e">TensorOpMultiplicandRowMajorInterleaved</a>(<a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#a149aa646854cd7591c7ee528eb5ea3ab">Index</a> ldm = 0): stride_(ldm) { }</div><div class="line"><a name="l01094"></a><span class="lineno"> 1094</span>&#160;</div><div class="line"><a name="l01096"></a><span class="lineno"> 1096</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l01097"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#aec5a45abb32fc87ced54e7297d33047f"> 1097</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#aec5a45abb32fc87ced54e7297d33047f">TensorOpMultiplicandRowMajorInterleaved</a>(<a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a57baa00f66f3b3ad24dbc803186e40d4">stride</a>): stride_(stride) { }</div><div class="line"><a name="l01098"></a><span class="lineno"> 1098</span>&#160;</div><div class="line"><a name="l01100"></a><span class="lineno"> 1100</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l01101"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#acb62895338493de3e37649e46b1c8c05"> 1101</a></span>&#160;  <span class="keyword">static</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html">TensorOpMultiplicandRowMajorInterleaved</a> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#acb62895338493de3e37649e46b1c8c05">packed</a>(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent) {</div><div class="line"><a name="l01102"></a><span class="lineno"> 1102</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html">TensorOpMultiplicandRowMajorInterleaved</a>(extent[1] * kInterleavedK);</div><div class="line"><a name="l01103"></a><span class="lineno"> 1103</span>&#160;  }</div><div class="line"><a name="l01104"></a><span class="lineno"> 1104</span>&#160;</div><div class="line"><a name="l01107"></a><span class="lineno"> 1107</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l01108"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#ac7b0202ef4a01f7fdff37ac678a60d34"> 1108</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#ace9bb56ad8e186a09e07a44f96ba8b6e">LongIndex</a> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#ac7b0202ef4a01f7fdff37ac678a60d34">operator()</a>(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;coord)<span class="keyword"> const </span>{</div><div class="line"><a name="l01109"></a><span class="lineno"> 1109</span>&#160;    <span class="keywordtype">int</span> <span class="keyword">const</span> rows_per_smem_cache_line = 128 / kInterleavedK;</div><div class="line"><a name="l01110"></a><span class="lineno"> 1110</span>&#160;</div><div class="line"><a name="l01111"></a><span class="lineno"> 1111</span>&#160;    <span class="keywordtype">int</span> row_id = coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#aa828f8dbee3903754b56759c1e6a6043">strided</a>() / rows_per_smem_cache_line;</div><div class="line"><a name="l01112"></a><span class="lineno"> 1112</span>&#160;    <span class="keywordtype">int</span> col_id = (coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#aa828f8dbee3903754b56759c1e6a6043">strided</a>() % rows_per_smem_cache_line) * kInterleavedK + coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#adb31bc9b8cf49dfff64245b70a850834">contiguous</a>();</div><div class="line"><a name="l01113"></a><span class="lineno"> 1113</span>&#160;</div><div class="line"><a name="l01114"></a><span class="lineno"> 1114</span>&#160;    <span class="keywordtype">int</span> access_block_id = col_id &gt;&gt; 4;</div><div class="line"><a name="l01115"></a><span class="lineno"> 1115</span>&#160;    <span class="keywordtype">int</span> swizzle_access_block_id = access_block_id ^ (row_id &amp; 1);</div><div class="line"><a name="l01116"></a><span class="lineno"> 1116</span>&#160;</div><div class="line"><a name="l01117"></a><span class="lineno"> 1117</span>&#160;    <span class="keywordtype">int</span> swizzle_col_id = swizzle_access_block_id &lt;&lt; 4;</div><div class="line"><a name="l01118"></a><span class="lineno"> 1118</span>&#160;</div><div class="line"><a name="l01119"></a><span class="lineno"> 1119</span>&#160;    <span class="keywordflow">return</span> row_id * 128 + swizzle_col_id;</div><div class="line"><a name="l01120"></a><span class="lineno"> 1120</span>&#160;  }</div><div class="line"><a name="l01121"></a><span class="lineno"> 1121</span>&#160;</div><div class="line"><a name="l01123"></a><span class="lineno"> 1123</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l01124"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#a34b45993ee8f11a8140b3e8ff4a350cd"> 1124</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#a34b45993ee8f11a8140b3e8ff4a350cd">stride</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l01125"></a><span class="lineno"> 1125</span>&#160;    <span class="keywordflow">return</span> stride_;</div><div class="line"><a name="l01126"></a><span class="lineno"> 1126</span>&#160;  }</div><div class="line"><a name="l01127"></a><span class="lineno"> 1127</span>&#160;</div><div class="line"><a name="l01129"></a><span class="lineno"> 1129</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l01130"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#a111a796634fba281399e4563f4d441f8"> 1130</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> &amp; <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#a111a796634fba281399e4563f4d441f8">stride</a>() {</div><div class="line"><a name="l01131"></a><span class="lineno"> 1131</span>&#160;    <span class="keywordflow">return</span> stride_;</div><div class="line"><a name="l01132"></a><span class="lineno"> 1132</span>&#160;  }</div><div class="line"><a name="l01133"></a><span class="lineno"> 1133</span>&#160;</div><div class="line"><a name="l01135"></a><span class="lineno"> 1135</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l01136"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#a0df7088ef67f17f69fd72fb5d238fc3e"> 1136</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#ace9bb56ad8e186a09e07a44f96ba8b6e">LongIndex</a> <a class="code" href="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#a0df7088ef67f17f69fd72fb5d238fc3e">capacity</a>(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent)<span class="keyword"> const </span>{</div><div class="line"><a name="l01137"></a><span class="lineno"> 1137</span>&#160;    <span class="keywordflow">return</span> (extent[0] / kInterleavedK) * stride_[0];</div><div class="line"><a name="l01138"></a><span class="lineno"> 1138</span>&#160;  }</div><div class="line"><a name="l01139"></a><span class="lineno"> 1139</span>&#160;};</div><div class="line"><a name="l01140"></a><span class="lineno"> 1140</span>&#160;</div><div class="line"><a name="l01142"></a><span class="lineno"> 1142</span>&#160;</div><div class="line"><a name="l01143"></a><span class="lineno"> 1143</span>&#160;} <span class="comment">// namespace layout</span></div><div class="line"><a name="l01144"></a><span class="lineno"> 1144</span>&#160;} <span class="comment">// namespace cutlass</span></div><div class="line"><a name="l01145"></a><span class="lineno"> 1145</span>&#160;</div><div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous_html_a37fd29e6b74010e9a3ae089d6a777724"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#a37fd29e6b74010e9a3ae089d6a777724">cutlass::layout::ColumnMajorTensorOpMultiplicandCongruous::LongIndex</a></div><div class="ttdeci">int64_t LongIndex</div><div class="ttdoc">Long index type used for offsets. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:434</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous_html_a41a554dc29e1852fe4cfc21fa250a9ac"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#a41a554dc29e1852fe4cfc21fa250a9ac">cutlass::layout::ColumnMajorTensorOpMultiplicandCongruous::AccessCount</a></div><div class="ttdeci">typename Base::AccessCount AccessCount</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:460</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_html_a4fc7a49e6a7b8fea8224991d5ab1e008"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a4fc7a49e6a7b8fea8224991d5ab1e008">cutlass::layout::TensorOpMultiplicandCongruous::TileShape</a></div><div class="ttdeci">typename Base::TileShape TileShape</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:240</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise_html_a77befaca4f7609243842a4e54d50d010"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#a77befaca4f7609243842a4e54d50d010">cutlass::layout::ColumnMajorTensorOpMultiplicandCrosswise::ColumnMajorTensorOpMultiplicandCrosswise</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE ColumnMajorTensorOpMultiplicandCrosswise(Stride stride)</div><div class="ttdoc">Ctor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:791</div></div>
<div class="ttc" id="structcutlass_1_1MatrixCoord_html_afbdcc5ca5b91f11f29046667b0bfde7b"><div class="ttname"><a href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">cutlass::MatrixCoord::column</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index const &amp; column() const </div><div class="ttdoc">Returns the column of the coordinate. </div><div class="ttdef"><b>Definition:</b> matrix_coord.h:85</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4_html_a8580ab6b5c31b815c9ff4d146cbec442"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#a8580ab6b5c31b815c9ff4d146cbec442">cutlass::layout::TensorOpMultiplicandCongruous&lt; 32, Crosswise &gt;::capacity</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex capacity(TensorCoord const &amp;extent) const </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:412</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved_html_a20c2a57c8a1c6224de7253cfb7239134"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#a20c2a57c8a1c6224de7253cfb7239134">cutlass::layout::TensorOpMultiplicandColumnMajorInterleaved::Index</a></div><div class="ttdeci">int32_t Index</div><div class="ttdoc">Index type used for coordinates. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:944</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise_html_a3ff5e9dcf1e98e074b40a2e06fa56df0"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a3ff5e9dcf1e98e074b40a2e06fa56df0">cutlass::layout::TensorOpMultiplicandCrosswise::PartitionCount</a></div><div class="ttdeci">typename Base::PartitionCount PartitionCount</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:670</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise_html_a4e8aa7fa89ae4ebd8afe284a9786df27"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a4e8aa7fa89ae4ebd8afe284a9786df27">cutlass::layout::TensorOpMultiplicandCrosswise::Index</a></div><div class="ttdeci">int32_t Index</div><div class="ttdoc">Index type used for coordinates. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:640</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise_html_a90513da18bdcebc20f673d83031551f9"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#a90513da18bdcebc20f673d83031551f9">cutlass::layout::RowMajorTensorOpMultiplicandCrosswise::RowMajorTensorOpMultiplicandCrosswise</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE RowMajorTensorOpMultiplicandCrosswise(Index ldm=0)</div><div class="ttdoc">Ctor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:888</div></div>
<div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicand_html_a13c4bd4700ca704d527a4f83f0e58365"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a13c4bd4700ca704d527a4f83f0e58365">cutlass::layout::TensorOpMultiplicand::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex operator()(TensorCoord const &amp;coord) const </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:137</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1PitchLinearCoord_html"><div class="ttname"><a href="structcutlass_1_1layout_1_1PitchLinearCoord.html">cutlass::layout::PitchLinearCoord</a></div><div class="ttdoc">Coordinate in pitch-linear space. </div><div class="ttdef"><b>Definition:</b> pitch_linear.h:52</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise_html_a8de62d289c4d9e3123736e42b4c3b33d"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#a8de62d289c4d9e3123736e42b4c3b33d">cutlass::layout::ColumnMajorTensorOpMultiplicandCrosswise::PartitionShape</a></div><div class="ttdeci">typename Base::PartitionShape PartitionShape</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:762</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise_html_abdbf2109359d6c902676eb3519484ab2"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#abdbf2109359d6c902676eb3519484ab2">cutlass::layout::RowMajorTensorOpMultiplicandCrosswise::PartitionShape</a></div><div class="ttdeci">typename Base::PartitionShape PartitionShape</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:863</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise_html_a06294c449ba29798c1fbfc3baebb0b0c"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a06294c449ba29798c1fbfc3baebb0b0c">cutlass::layout::TensorOpMultiplicandCrosswise::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride &amp; stride()</div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:719</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous_html_aa455da7f56592fdb70977b1cad108003"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#aa455da7f56592fdb70977b1cad108003">cutlass::layout::ColumnMajorTensorOpMultiplicandCongruous::Index</a></div><div class="ttdeci">int32_t Index</div><div class="ttdoc">Index type used for coordinates. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:431</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4_html_adfba7bb7c28bfb734201bac9ca0d2bd0"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#adfba7bb7c28bfb734201bac9ca0d2bd0">cutlass::layout::TensorOpMultiplicandCongruous&lt; 32, Crosswise &gt;::Index</a></div><div class="ttdeci">int32_t Index</div><div class="ttdoc">Index type used for coordinates. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:321</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise_html_a34106f682f912c7d73ce03964e220859"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a34106f682f912c7d73ce03964e220859">cutlass::layout::TensorOpMultiplicandCrosswise::TensorOpMultiplicandCrosswise</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorOpMultiplicandCrosswise(Stride stride)</div><div class="ttdoc">Ctor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:691</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous_html_a32c8623b9c3c8490263ea0af24a1410f"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#a32c8623b9c3c8490263ea0af24a1410f">cutlass::layout::RowMajorTensorOpMultiplicandCongruous::Index</a></div><div class="ttdeci">int32_t Index</div><div class="ttdoc">Index type used for coordinates. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:536</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise_html_a1fa92e43f3d6b1874f9fe5c1f87b8ee0"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#a1fa92e43f3d6b1874f9fe5c1f87b8ee0">cutlass::layout::RowMajorTensorOpMultiplicandCrosswise::inverse</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorCoord inverse(LongIndex offset) const </div><div class="ttdoc">Inverse of layout function, mapping linear offset to logical coordinate. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:910</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise_html_a8adbc25064e0edb48c429a59008f6d6a"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#a8adbc25064e0edb48c429a59008f6d6a">cutlass::layout::ColumnMajorTensorOpMultiplicandCrosswise::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex operator()(TensorCoord const &amp;coord) const </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:803</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise_html"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html">cutlass::layout::ColumnMajorTensorOpMultiplicandCrosswise</a></div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:734</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicand_html_a92637a459d6d39f55aada64522e77dd6"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a92637a459d6d39f55aada64522e77dd6">cutlass::layout::TensorOpMultiplicand::kRank</a></div><div class="ttdeci">static int const kRank</div><div class="ttdoc">Logical rank of tensor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:48</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous_html_ad49467d9028288b0bb93dbf376ef573a"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#ad49467d9028288b0bb93dbf376ef573a">cutlass::layout::RowMajorTensorOpMultiplicandCongruous::LongIndex</a></div><div class="ttdeci">int64_t LongIndex</div><div class="ttdoc">Long index type used for offsets. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:539</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_html_a6ef0968585fcb6bb564e1ea6c4bdc9a3"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a6ef0968585fcb6bb564e1ea6c4bdc9a3">cutlass::layout::TensorOpMultiplicandCongruous::inverse</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorCoord inverse(LongIndex offset) const </div><div class="ttdoc">Inverse of layout function, mapping linear offset to logical coordinate. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:287</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved_html_a9aa34a2e71b4cbcc412c2a41f0e1c1c2"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#a9aa34a2e71b4cbcc412c2a41f0e1c1c2">cutlass::layout::TensorOpMultiplicandColumnMajorInterleaved::TensorOpMultiplicandColumnMajorInterleaved</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorOpMultiplicandColumnMajorInterleaved(Stride stride)</div><div class="ttdoc">Ctor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:992</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicand_html_a68fab741fd24d425c260d274b550bd2f"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a68fab741fd24d425c260d274b550bd2f">cutlass::layout::TensorOpMultiplicand::Index</a></div><div class="ttdeci">int32_t Index</div><div class="ttdoc">Index type used for coordinates. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:54</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous_html_abd26edad15a0ce1c3a24d6a9c96f66a3"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#abd26edad15a0ce1c3a24d6a9c96f66a3">cutlass::layout::ColumnMajorTensorOpMultiplicandCongruous::PartitionCount</a></div><div class="ttdeci">typename Base::PartitionCount PartitionCount</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:459</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_html_ab498476001a090017735113863e28898"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#ab498476001a090017735113863e28898">cutlass::layout::TensorOpMultiplicandCongruous::Index</a></div><div class="ttdeci">int32_t Index</div><div class="ttdoc">Index type used for coordinates. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:221</div></div>
<div class="ttc" id="coord_8h_html"><div class="ttname"><a href="coord_8h.html">coord.h</a></div><div class="ttdoc">A Coord is a coordinate of arbitrary rank into a tensor or matrix. </div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved_html_ace9bb56ad8e186a09e07a44f96ba8b6e"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#ace9bb56ad8e186a09e07a44f96ba8b6e">cutlass::layout::TensorOpMultiplicandRowMajorInterleaved::LongIndex</a></div><div class="ttdeci">int64_t LongIndex</div><div class="ttdoc">Long index type used for offsets. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:1052</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous_html_a443b469af1a3602e3173b132ae3dd40b"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#a443b469af1a3602e3173b132ae3dd40b">cutlass::layout::RowMajorTensorOpMultiplicandCongruous::packed</a></div><div class="ttdeci">static CUTLASS_HOST_DEVICE RowMajorTensorOpMultiplicandCongruous packed(TensorCoord const &amp;extent)</div><div class="ttdoc">Helper returns a layout to a tightly packed tensor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:590</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous_html"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html">cutlass::layout::ColumnMajorTensorOpMultiplicandCongruous</a></div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:422</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise_html_a5b3ff99ea22d5d6c5bce9aa9ab228b46"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#a5b3ff99ea22d5d6c5bce9aa9ab228b46">cutlass::layout::RowMajorTensorOpMultiplicandCrosswise::packed</a></div><div class="ttdeci">static CUTLASS_HOST_DEVICE RowMajorTensorOpMultiplicandCrosswise packed(TensorCoord const &amp;extent)</div><div class="ttdoc">Helper returns a layout to a tightly packed tensor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:896</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise_html_aba317cad410b80b71b703eebe9367a06"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#aba317cad410b80b71b703eebe9367a06">cutlass::layout::TensorOpMultiplicandCrosswise::TileShape</a></div><div class="ttdeci">typename Base::TileShape TileShape</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:659</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise_html_afb16d769e699fe88e09420cce6096d86"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#afb16d769e699fe88e09420cce6096d86">cutlass::layout::RowMajorTensorOpMultiplicandCrosswise::Index</a></div><div class="ttdeci">int32_t Index</div><div class="ttdoc">Index type used for coordinates. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:843</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous_html_a08f78c18f903ffd868934ba443d0533f"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#a08f78c18f903ffd868934ba443d0533f">cutlass::layout::RowMajorTensorOpMultiplicandCongruous::TileShape</a></div><div class="ttdeci">typename Base::TileShape TileShape</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:555</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise_html_af9367fe9d6c5d95367d84dbab02dd0fe"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#af9367fe9d6c5d95367d84dbab02dd0fe">cutlass::layout::ColumnMajorTensorOpMultiplicandCrosswise::Index</a></div><div class="ttdeci">int32_t Index</div><div class="ttdoc">Index type used for coordinates. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:742</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous_html_a67e0749e995824bc35a8cde478f7c631"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#a67e0749e995824bc35a8cde478f7c631">cutlass::layout::RowMajorTensorOpMultiplicandCongruous::inverse</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorCoord inverse(LongIndex offset) const </div><div class="ttdoc">Inverse of layout function, mapping linear offset to logical coordinate. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:603</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous_html_ade26abdd740cb37ed4ef0383a0096c01"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#ade26abdd740cb37ed4ef0383a0096c01">cutlass::layout::ColumnMajorTensorOpMultiplicandCongruous::PartitionShape</a></div><div class="ttdeci">typename Base::PartitionShape PartitionShape</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:451</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise_html"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html">cutlass::layout::RowMajorTensorOpMultiplicandCrosswise</a></div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:835</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicand_html_a28b111ff2701662606d3c69d53c49a84"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a28b111ff2701662606d3c69d53c49a84">cutlass::layout::TensorOpMultiplicand::LongIndex</a></div><div class="ttdeci">int64_t LongIndex</div><div class="ttdoc">Long index type used for offsets. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:57</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise_html_a1a69654a56446c0271037c5df7fbfc86"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a1a69654a56446c0271037c5df7fbfc86">cutlass::layout::TensorOpMultiplicandCrosswise::packed</a></div><div class="ttdeci">static CUTLASS_HOST_DEVICE TensorOpMultiplicandCrosswise packed(TensorCoord const &amp;extent)</div><div class="ttdoc">Helper returns a layout to a tightly packed tensor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:695</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_html"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html">cutlass::layout::TensorOpMultiplicandCongruous</a></div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:213</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_html_a9c818862df1951d9e8ebb31165b61fb5"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a9c818862df1951d9e8ebb31165b61fb5">cutlass::layout::TensorOpMultiplicandCongruous::PartitionCount</a></div><div class="ttdeci">typename Base::PartitionCount PartitionCount</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:249</div></div>
<div class="ttc" id="structcutlass_1_1MatrixCoord_html_a0580610f28427e376b24b71f67602d03"><div class="ttname"><a href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">cutlass::MatrixCoord::row</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index const &amp; row() const </div><div class="ttdoc">Returns the row of the coordinate. </div><div class="ttdef"><b>Definition:</b> matrix_coord.h:77</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_html_a016ea88480ec49f60b311b00e06dba54"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a016ea88480ec49f60b311b00e06dba54">cutlass::layout::TensorOpMultiplicandCongruous::capacity</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex capacity(TensorCoord const &amp;extent) const </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:303</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise_html_afe4b1ec877e6f1266e4e57a862d43767"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#afe4b1ec877e6f1266e4e57a862d43767">cutlass::layout::RowMajorTensorOpMultiplicandCrosswise::RowMajorTensorOpMultiplicandCrosswise</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE RowMajorTensorOpMultiplicandCrosswise(Stride stride)</div><div class="ttdoc">Ctor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:892</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicand_html_a1a8ae93363b37a31b2b0ec0df2ea00d2"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a1a8ae93363b37a31b2b0ec0df2ea00d2">cutlass::layout::TensorOpMultiplicand::kTileShapeContiguous</a></div><div class="ttdeci">static int const kTileShapeContiguous</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:78</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise_html_ad1b74e4f166e09476444c56fec627504"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#ad1b74e4f166e09476444c56fec627504">cutlass::layout::ColumnMajorTensorOpMultiplicandCrosswise::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride &amp; stride()</div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:820</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved_html_acb62895338493de3e37649e46b1c8c05"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#acb62895338493de3e37649e46b1c8c05">cutlass::layout::TensorOpMultiplicandRowMajorInterleaved::packed</a></div><div class="ttdeci">static CUTLASS_HOST_DEVICE TensorOpMultiplicandRowMajorInterleaved packed(TensorCoord const &amp;extent)</div><div class="ttdoc">Helper returns a layout to a tightly packed tensor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:1101</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved_html_a559487c37a4387d88726a51cd08b17b7"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#a559487c37a4387d88726a51cd08b17b7">cutlass::layout::TensorOpMultiplicandColumnMajorInterleaved::packed</a></div><div class="ttdeci">static CUTLASS_HOST_DEVICE TensorOpMultiplicandColumnMajorInterleaved packed(TensorCoord const &amp;extent)</div><div class="ttdoc">Helper returns a layout to a tightly packed tensor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:996</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_html_a45607ffb63b8f3e307a2537777c88491"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a45607ffb63b8f3e307a2537777c88491">cutlass::layout::TensorOpMultiplicandCongruous::AccessCount</a></div><div class="ttdeci">typename Base::AccessCount AccessCount</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:250</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise_html_add5bc9679d4ad1bf2ee2c5b81c8ff0ca"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#add5bc9679d4ad1bf2ee2c5b81c8ff0ca">cutlass::layout::RowMajorTensorOpMultiplicandCrosswise::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride stride() const </div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:917</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise_html_afeba15c02217a7e06c3cb96c7bef2bd0"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#afeba15c02217a7e06c3cb96c7bef2bd0">cutlass::layout::ColumnMajorTensorOpMultiplicandCrosswise::capacity</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex capacity(TensorCoord const &amp;extent) const </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:825</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved_html_ac59716a33dc9f73d18ef54649f07e2c6"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#ac59716a33dc9f73d18ef54649f07e2c6">cutlass::layout::TensorOpMultiplicandColumnMajorInterleaved::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride &amp; stride()</div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:1025</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise_html_ae9010e219ceb098de21d2673a9112b50"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#ae9010e219ceb098de21d2673a9112b50">cutlass::layout::ColumnMajorTensorOpMultiplicandCrosswise::ColumnMajorTensorOpMultiplicandCrosswise</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE ColumnMajorTensorOpMultiplicandCrosswise(Index ldm=0)</div><div class="ttdoc">Ctor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:787</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved_html_a6c2a122f2d16f1ca7957d7b0749248fc"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#a6c2a122f2d16f1ca7957d7b0749248fc">cutlass::layout::TensorOpMultiplicandColumnMajorInterleaved::LongIndex</a></div><div class="ttdeci">int64_t LongIndex</div><div class="ttdoc">Long index type used for offsets. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:947</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_html_aa04ef0da8d8a859c2b7bb08cb3752d3d"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#aa04ef0da8d8a859c2b7bb08cb3752d3d">cutlass::layout::TensorOpMultiplicandCongruous::LongIndex</a></div><div class="ttdeci">int64_t LongIndex</div><div class="ttdoc">Long index type used for offsets. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:224</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous_html_a6db9483dd8c793e687445514fd00124f"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#a6db9483dd8c793e687445514fd00124f">cutlass::layout::RowMajorTensorOpMultiplicandCongruous::PartitionCount</a></div><div class="ttdeci">typename Base::PartitionCount PartitionCount</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:564</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4_html_a659ba11b4c1dc1d06005e160feb13d4b"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#a659ba11b4c1dc1d06005e160feb13d4b">cutlass::layout::TensorOpMultiplicandCongruous&lt; 32, Crosswise &gt;::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride &amp; stride()</div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:407</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_html_a3b9ea3abdcd5cc5e4c5b14f7f329bb4f"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a3b9ea3abdcd5cc5e4c5b14f7f329bb4f">cutlass::layout::TensorOpMultiplicandCongruous::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex operator()(TensorCoord const &amp;coord) const </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:281</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1PitchLinearShape_html"><div class="ttname"><a href="structcutlass_1_1layout_1_1PitchLinearShape.html">cutlass::layout::PitchLinearShape</a></div><div class="ttdoc">Template defining a shape used by pitch-linear operators. </div><div class="ttdef"><b>Definition:</b> pitch_linear.h:43</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise_html_abf4bd0dac231dfa83fc109420371fe8e"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#abf4bd0dac231dfa83fc109420371fe8e">cutlass::layout::TensorOpMultiplicandCrosswise::capacity</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex capacity(TensorCoord const &amp;extent) const </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:724</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise_html_a60953e9a77d09691d2320df1a9ad3f3e"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#a60953e9a77d09691d2320df1a9ad3f3e">cutlass::layout::ColumnMajorTensorOpMultiplicandCrosswise::TileShape</a></div><div class="ttdeci">typename Base::TileShape TileShape</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:761</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved_html_ac8d018056fa3e2993f4b85cfd97c2800"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#ac8d018056fa3e2993f4b85cfd97c2800">cutlass::layout::TensorOpMultiplicandColumnMajorInterleaved::TensorOpMultiplicandColumnMajorInterleaved</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorOpMultiplicandColumnMajorInterleaved(Index ldm=0)</div><div class="ttdoc">Ctor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:988</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous_html_ad00403d7a4792d15fddcf3d229556351"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#ad00403d7a4792d15fddcf3d229556351">cutlass::layout::ColumnMajorTensorOpMultiplicandCongruous::TileShape</a></div><div class="ttdeci">typename Base::TileShape TileShape</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:450</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise_html_aa7c6cfa96263de67b132a54010f36189"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#aa7c6cfa96263de67b132a54010f36189">cutlass::layout::ColumnMajorTensorOpMultiplicandCrosswise::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride stride() const </div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:816</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise_html_a85b214a913b534fc9cc5366664a414e3"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#a85b214a913b534fc9cc5366664a414e3">cutlass::layout::RowMajorTensorOpMultiplicandCrosswise::LongIndex</a></div><div class="ttdeci">int64_t LongIndex</div><div class="ttdoc">Long index type used for offsets. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:846</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous_html_a789b0744a97926b5447be3861f184122"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#a789b0744a97926b5447be3861f184122">cutlass::layout::ColumnMajorTensorOpMultiplicandCongruous::capacity</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex capacity(TensorCoord const &amp;extent) const </div><div class="ttdoc">Compute the number of contiguous elements needed to store a tensor with the given size...</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:517</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_html_a6b9e003e72d27841f42ee2e74689c632"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a6b9e003e72d27841f42ee2e74689c632">cutlass::layout::TensorOpMultiplicandCongruous::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride stride() const </div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:294</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicand_html_a653fa6fd827643bcb835f6a715ac8d25"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a653fa6fd827643bcb835f6a715ac8d25">cutlass::layout::TensorOpMultiplicand::kElementsPerAccess</a></div><div class="ttdeci">static int const kElementsPerAccess</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:73</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1PitchLinearShape_html_aaec0afa0c26627d951d2d2b98a3e5601"><div class="ttname"><a href="structcutlass_1_1layout_1_1PitchLinearShape.html#aaec0afa0c26627d951d2d2b98a3e5601">cutlass::layout::PitchLinearShape::kStrided</a></div><div class="ttdeci">static int const kStrided</div><div class="ttdef"><b>Definition:</b> pitch_linear.h:45</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved_html_a149aa646854cd7591c7ee528eb5ea3ab"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#a149aa646854cd7591c7ee528eb5ea3ab">cutlass::layout::TensorOpMultiplicandRowMajorInterleaved::Index</a></div><div class="ttdeci">int32_t Index</div><div class="ttdoc">Index type used for coordinates. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:1049</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise_html_a8e1f0b54a8c1b3f37df9ec0dd3548985"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#a8e1f0b54a8c1b3f37df9ec0dd3548985">cutlass::layout::ColumnMajorTensorOpMultiplicandCrosswise::LongIndex</a></div><div class="ttdeci">int64_t LongIndex</div><div class="ttdoc">Long index type used for offsets. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:745</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise_html_ac0f9fe7e728edb0eff201fd1c356db4a"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#ac0f9fe7e728edb0eff201fd1c356db4a">cutlass::layout::RowMajorTensorOpMultiplicandCrosswise::PartitionCount</a></div><div class="ttdeci">typename Base::PartitionCount PartitionCount</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:871</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous_html_a863ff7fbc770524406c5f3a72ec4e919"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#a863ff7fbc770524406c5f3a72ec4e919">cutlass::layout::RowMajorTensorOpMultiplicandCongruous::RowMajorTensorOpMultiplicandCongruous</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE RowMajorTensorOpMultiplicandCongruous(Stride stride)</div><div class="ttdoc">Ctor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:586</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicand_html"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html">cutlass::layout::TensorOpMultiplicand</a></div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:46</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1PitchLinearShape_html_a0d1cfb72e7511d162d123fcb36d181b7"><div class="ttname"><a href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">cutlass::layout::PitchLinearShape::kContiguous</a></div><div class="ttdeci">static int const kContiguous</div><div class="ttdef"><b>Definition:</b> pitch_linear.h:44</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved_html"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html">cutlass::layout::TensorOpMultiplicandColumnMajorInterleaved</a></div><div class="ttdoc">Template based on element size (in bits) - defined in terms of pitch-linear memory. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:935</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous_html_a7e55b41a76b968ade75d77330471e10e"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#a7e55b41a76b968ade75d77330471e10e">cutlass::layout::RowMajorTensorOpMultiplicandCongruous::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex operator()(TensorCoord const &amp;coord) const </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:597</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise_html_ab447aac95eab1ad7d24767d4657990a4"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#ab447aac95eab1ad7d24767d4657990a4">cutlass::layout::ColumnMajorTensorOpMultiplicandCrosswise::AccessCount</a></div><div class="ttdeci">typename Base::AccessCount AccessCount</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:771</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicand_html_aeee51a96d48446dd30a0024f6a88a95d"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#aeee51a96d48446dd30a0024f6a88a95d">cutlass::layout::TensorOpMultiplicand::TensorOpMultiplicand</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorOpMultiplicand(Stride stride)</div><div class="ttdoc">Ctor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:126</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous_html_a910ff464fc55a153bf3e54cb7b816ccb"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#a910ff464fc55a153bf3e54cb7b816ccb">cutlass::layout::ColumnMajorTensorOpMultiplicandCongruous::ColumnMajorTensorOpMultiplicandCongruous</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE ColumnMajorTensorOpMultiplicandCongruous(Index ldm=0)</div><div class="ttdoc">Ctor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:477</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved_html_a68ec1121f75551e44ef28edc17763179"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#a68ec1121f75551e44ef28edc17763179">cutlass::layout::TensorOpMultiplicandColumnMajorInterleaved::capacity</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex capacity(TensorCoord const &amp;extent) const </div><div class="ttdoc">Compute the number of contiguous elements needed to store a tensor with the given size...</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:1031</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_html_a3fbf6b274197d5f90513aa10360e667e"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a3fbf6b274197d5f90513aa10360e667e">cutlass::layout::TensorOpMultiplicandCongruous::TensorOpMultiplicandCongruous</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorOpMultiplicandCongruous(Index ldm=0)</div><div class="ttdoc">Ctor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:266</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4_html_a3881a944fbeeda82884adfd5853f7577"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#a3881a944fbeeda82884adfd5853f7577">cutlass::layout::TensorOpMultiplicandCongruous&lt; 32, Crosswise &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex operator()(TensorCoord const &amp;coord) const </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:388</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise_html_a7f702111a44362cf73b81c11a1907b3a"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#a7f702111a44362cf73b81c11a1907b3a">cutlass::layout::RowMajorTensorOpMultiplicandCrosswise::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex operator()(TensorCoord const &amp;coord) const </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:904</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved_html_a34b45993ee8f11a8140b3e8ff4a350cd"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#a34b45993ee8f11a8140b3e8ff4a350cd">cutlass::layout::TensorOpMultiplicandRowMajorInterleaved::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride stride() const </div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:1124</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_html_a9307437b4b93a3dd450244bb54aa50d4"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a9307437b4b93a3dd450244bb54aa50d4">cutlass::layout::TensorOpMultiplicandCongruous::packed</a></div><div class="ttdeci">static CUTLASS_HOST_DEVICE TensorOpMultiplicandCongruous packed(TensorCoord const &amp;extent)</div><div class="ttdoc">Helper returns a layout to a tightly packed tensor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:274</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4_html_a8873becf0049da7289f853021e3beec3"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#a8873becf0049da7289f853021e3beec3">cutlass::layout::TensorOpMultiplicandCongruous&lt; 32, Crosswise &gt;::LongIndex</a></div><div class="ttdeci">int64_t LongIndex</div><div class="ttdoc">Long index type used for offsets. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:324</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous_html_a588582ed7496bb9f6cfbb7f79873affa"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#a588582ed7496bb9f6cfbb7f79873affa">cutlass::layout::ColumnMajorTensorOpMultiplicandCongruous::ColumnMajorTensorOpMultiplicandCongruous</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE ColumnMajorTensorOpMultiplicandCongruous(Stride stride)</div><div class="ttdoc">Ctor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:481</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous_html_aef0305eeba98299968d5b42b262425d0"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#aef0305eeba98299968d5b42b262425d0">cutlass::layout::ColumnMajorTensorOpMultiplicandCongruous::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride stride() const </div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:505</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise_html_ae9c97a64123f074907e805d3642d924b"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#ae9c97a64123f074907e805d3642d924b">cutlass::layout::TensorOpMultiplicandCrosswise::AccessCount</a></div><div class="ttdeci">typename Base::AccessCount AccessCount</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:671</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise_html_a04e4211bb2e725d434371b2cf1de696e"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#a04e4211bb2e725d434371b2cf1de696e">cutlass::layout::RowMajorTensorOpMultiplicandCrosswise::capacity</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex capacity(TensorCoord const &amp;extent) const </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:926</div></div>
<div class="ttc" id="cutlass_8h_html_a28c2443a142676d3d71effdae1a986b1"><div class="ttname"><a href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="ttdeci">#define CUTLASS_HOST_DEVICE</div><div class="ttdef"><b>Definition:</b> cutlass.h:89</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise_html_a04d8e6cbf2723e8afaa7fd0e5f18c66f"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#a04d8e6cbf2723e8afaa7fd0e5f18c66f">cutlass::layout::RowMajorTensorOpMultiplicandCrosswise::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride &amp; stride()</div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:921</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1PitchLinearCoord_html_adb31bc9b8cf49dfff64245b70a850834"><div class="ttname"><a href="structcutlass_1_1layout_1_1PitchLinearCoord.html#adb31bc9b8cf49dfff64245b70a850834">cutlass::layout::PitchLinearCoord::contiguous</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index const &amp; contiguous() const </div><div class="ttdoc">Returns the contiguous dimension. </div><div class="ttdef"><b>Definition:</b> pitch_linear.h:89</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved_html_ad20058caa6ef3c9f540a33fea56ac0d3"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#ad20058caa6ef3c9f540a33fea56ac0d3">cutlass::layout::TensorOpMultiplicandColumnMajorInterleaved::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex operator()(TensorCoord const &amp;coord) const </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:1003</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous_html_acd7ea2b943914f72f0040b208915aa89"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#acd7ea2b943914f72f0040b208915aa89">cutlass::layout::RowMajorTensorOpMultiplicandCongruous::RowMajorTensorOpMultiplicandCongruous</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE RowMajorTensorOpMultiplicandCongruous(Index ldm=0)</div><div class="ttdoc">Ctor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:582</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous_html_a83a61ac278a06817c63cdc71fe823675"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#a83a61ac278a06817c63cdc71fe823675">cutlass::layout::ColumnMajorTensorOpMultiplicandCongruous::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride &amp; stride()</div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:511</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous_html_a080e343a32d097ab57d86362986cfb96"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#a080e343a32d097ab57d86362986cfb96">cutlass::layout::RowMajorTensorOpMultiplicandCongruous::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride stride() const </div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:610</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous_html_aef568e91d5233b70fffe5315633bcc0d"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#aef568e91d5233b70fffe5315633bcc0d">cutlass::layout::RowMajorTensorOpMultiplicandCongruous::PartitionShape</a></div><div class="ttdeci">typename Base::PartitionShape PartitionShape</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:556</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous_html_ae068c7ff740e38ea4c06a35a57416595"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#ae068c7ff740e38ea4c06a35a57416595">cutlass::layout::ColumnMajorTensorOpMultiplicandCongruous::packed</a></div><div class="ttdeci">static CUTLASS_HOST_DEVICE ColumnMajorTensorOpMultiplicandCongruous packed(TensorCoord const &amp;extent)</div><div class="ttdoc">Helper returns a layout to a tightly packed tensor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:485</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise_html_a07de92ffadfe255537479c37b04cde6f"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a07de92ffadfe255537479c37b04cde6f">cutlass::layout::TensorOpMultiplicandCrosswise::LongIndex</a></div><div class="ttdeci">int64_t LongIndex</div><div class="ttdoc">Long index type used for offsets. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:643</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise_html_a51b6769545027ba9304cf9d1f1dd05f6"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a51b6769545027ba9304cf9d1f1dd05f6">cutlass::layout::TensorOpMultiplicandCrosswise::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride stride() const </div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:715</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicand_html_a57baa00f66f3b3ad24dbc803186e40d4"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a57baa00f66f3b3ad24dbc803186e40d4">cutlass::layout::TensorOpMultiplicand::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride stride() const </div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:194</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise_html_a1ec5d2e030810801e5e5816916a1cf7f"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a1ec5d2e030810801e5e5816916a1cf7f">cutlass::layout::TensorOpMultiplicandCrosswise::TensorOpMultiplicandCrosswise</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorOpMultiplicandCrosswise(Index ldm=0)</div><div class="ttdoc">Ctor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:687</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise_html_ac4ff3d7bfc5303cb2dee432a7cc430da"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#ac4ff3d7bfc5303cb2dee432a7cc430da">cutlass::layout::TensorOpMultiplicandCrosswise::inverse</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorCoord inverse(LongIndex offset) const </div><div class="ttdoc">Inverse of layout function, mapping linear offset to logical coordinate. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:708</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_html_afa388d90ae2f571c85c8988a1a15f934"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#afa388d90ae2f571c85c8988a1a15f934">cutlass::layout::TensorOpMultiplicandCongruous::TensorOpMultiplicandCongruous</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorOpMultiplicandCongruous(Stride stride)</div><div class="ttdoc">Ctor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:270</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved_html"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html">cutlass::layout::TensorOpMultiplicandRowMajorInterleaved</a></div><div class="ttdoc">Template based on element size (in bits) - defined in terms of pitch-linear memory. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:1040</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved_html_ad925d2b3c135bca79f802d53e4c31a6e"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#ad925d2b3c135bca79f802d53e4c31a6e">cutlass::layout::TensorOpMultiplicandRowMajorInterleaved::TensorOpMultiplicandRowMajorInterleaved</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorOpMultiplicandRowMajorInterleaved(Index ldm=0)</div><div class="ttdoc">Ctor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:1093</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise_html_afd79156b28c636812e72d0fcccd455e7"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#afd79156b28c636812e72d0fcccd455e7">cutlass::layout::ColumnMajorTensorOpMultiplicandCrosswise::packed</a></div><div class="ttdeci">static CUTLASS_HOST_DEVICE ColumnMajorTensorOpMultiplicandCrosswise packed(TensorCoord const &amp;extent)</div><div class="ttdoc">Helper returns a layout to a tightly packed tensor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:795</div></div>
<div class="ttc" id="structcutlass_1_1Coord_html"><div class="ttname"><a href="structcutlass_1_1Coord.html">cutlass::Coord&lt; kStrideRank, Index, LongIndex &gt;</a></div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous_html_a274f0531c54b7e15243b0460d59e0c3b"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#a274f0531c54b7e15243b0460d59e0c3b">cutlass::layout::RowMajorTensorOpMultiplicandCongruous::AccessCount</a></div><div class="ttdeci">typename Base::AccessCount AccessCount</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:565</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicand_html_acf086249495fdc5f55f7d4a03ce3c4dc"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#acf086249495fdc5f55f7d4a03ce3c4dc">cutlass::layout::TensorOpMultiplicand::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride &amp; stride()</div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:198</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise_html_a860acbb4cce4be26a6a72351b832414e"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#a860acbb4cce4be26a6a72351b832414e">cutlass::layout::RowMajorTensorOpMultiplicandCrosswise::TileShape</a></div><div class="ttdeci">typename Base::TileShape TileShape</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:862</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4_html_aafa9812d3c82b30b2d416738c586bfe0"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#aafa9812d3c82b30b2d416738c586bfe0">cutlass::layout::TensorOpMultiplicandCongruous&lt; 32, Crosswise &gt;::packed</a></div><div class="ttdeci">static CUTLASS_HOST_DEVICE TensorOpMultiplicandCongruous packed(TensorCoord const &amp;extent)</div><div class="ttdoc">Helper returns a layout to a tightly packed tensor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:381</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_html_a8d2117bfc734389ab04e40288e783a4d"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a8d2117bfc734389ab04e40288e783a4d">cutlass::layout::TensorOpMultiplicandCongruous::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride &amp; stride()</div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:298</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous_html_afcbbd0705f41dcc61aec0ba0ecaacc6a"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#afcbbd0705f41dcc61aec0ba0ecaacc6a">cutlass::layout::RowMajorTensorOpMultiplicandCongruous::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride &amp; stride()</div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:616</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicand_html_a58b9d5809fed8924636d1caa32f661ef"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a58b9d5809fed8924636d1caa32f661ef">cutlass::layout::TensorOpMultiplicand::kCrosswise</a></div><div class="ttdeci">static int const kCrosswise</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:74</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicand_html_a56de60deb0ebd18347ad4fa33e10ffce"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a56de60deb0ebd18347ad4fa33e10ffce">cutlass::layout::TensorOpMultiplicand::kTileShapeStride</a></div><div class="ttdeci">static int const kTileShapeStride</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:87</div></div>
<div class="ttc" id="matrix__coord_8h_html"><div class="ttname"><a href="matrix__coord_8h.html">matrix_coord.h</a></div><div class="ttdoc">Defines a canonical coordinate for rank=2 matrices offering named indices. </div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved_html_a5a04695e79ca5ef76abd105edf6b208a"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#a5a04695e79ca5ef76abd105edf6b208a">cutlass::layout::TensorOpMultiplicandColumnMajorInterleaved::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride stride() const </div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:1019</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicand_html_a8a437591f9e9aa95ce0616dd931f48bd"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a8a437591f9e9aa95ce0616dd931f48bd">cutlass::layout::TensorOpMultiplicand::TensorOpMultiplicand</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorOpMultiplicand(Index ldm=0)</div><div class="ttdoc">Ctor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:122</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise_html_a09eaa824aea4b98af2e71c14f572fd56"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#a09eaa824aea4b98af2e71c14f572fd56">cutlass::layout::ColumnMajorTensorOpMultiplicandCrosswise::inverse</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorCoord inverse(LongIndex offset) const </div><div class="ttdoc">Inverse of layout function, mapping linear offset to logical coordinate. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:809</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicand_html_a1f35f08a131d76521a98c391acedb4e6"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a1f35f08a131d76521a98c391acedb4e6">cutlass::layout::TensorOpMultiplicand::kElementSize</a></div><div class="ttdeci">static int const kElementSize</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:72</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicand_html_abb0b4f936c0e251e9b3560fc9b7820f6"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#abb0b4f936c0e251e9b3560fc9b7820f6">cutlass::layout::TensorOpMultiplicand::kFactor</a></div><div class="ttdeci">static int const kFactor</div><div class="ttdoc">Number of kblocks to store PartitionShape::kContiguous Elements. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:81</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicand_html_a9ef7a95265d1602eb5d050eb89ad8b6b"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a9ef7a95265d1602eb5d050eb89ad8b6b">cutlass::layout::TensorOpMultiplicand::capacity</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex capacity(TensorCoord const &amp;extent) const </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:203</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise_html_a8ef78b952d245778ef8df144df21de2e"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a8ef78b952d245778ef8df144df21de2e">cutlass::layout::TensorOpMultiplicandCrosswise::PartitionShape</a></div><div class="ttdeci">typename Base::PartitionShape PartitionShape</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:660</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_html_a33b3e232203ea774f1bfd568f710f36d"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a33b3e232203ea774f1bfd568f710f36d">cutlass::layout::TensorOpMultiplicandCongruous::PartitionShape</a></div><div class="ttdeci">typename Base::PartitionShape PartitionShape</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:241</div></div>
<div class="ttc" id="pitch__linear_8h_html"><div class="ttname"><a href="pitch__linear_8h.html">pitch_linear.h</a></div><div class="ttdoc">Defines layout functions used by TensorRef and derived classes for pitch-linear memory. </div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4_html_a842b22d0cb0974b95aa9d9b05d90524e"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#a842b22d0cb0974b95aa9d9b05d90524e">cutlass::layout::TensorOpMultiplicandCongruous&lt; 32, Crosswise &gt;::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride stride() const </div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:403</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous_html_ae7834e63ec3b61d181f847011be413ab"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#ae7834e63ec3b61d181f847011be413ab">cutlass::layout::ColumnMajorTensorOpMultiplicandCongruous::inverse</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorCoord inverse(LongIndex offset) const </div><div class="ttdoc">Inverse of layout function, mapping linear offset to logical coordinate. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:498</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise_html_ab7adede333905f5e87178fee4c0d530c"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#ab7adede333905f5e87178fee4c0d530c">cutlass::layout::RowMajorTensorOpMultiplicandCrosswise::AccessCount</a></div><div class="ttdeci">typename Base::AccessCount AccessCount</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:872</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise_html"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html">cutlass::layout::TensorOpMultiplicandCrosswise</a></div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:632</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved_html_a0df7088ef67f17f69fd72fb5d238fc3e"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#a0df7088ef67f17f69fd72fb5d238fc3e">cutlass::layout::TensorOpMultiplicandRowMajorInterleaved::capacity</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex capacity(TensorCoord const &amp;extent) const </div><div class="ttdoc">Compute the number of contiguous elements needed to store a tensor with the given size...</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:1136</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved_html_aec5a45abb32fc87ced54e7297d33047f"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#aec5a45abb32fc87ced54e7297d33047f">cutlass::layout::TensorOpMultiplicandRowMajorInterleaved::TensorOpMultiplicandRowMajorInterleaved</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorOpMultiplicandRowMajorInterleaved(Stride stride)</div><div class="ttdoc">Ctor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:1097</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicand_html_a8c6e0632e7a38d03612a73a93f5f6a1f"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a8c6e0632e7a38d03612a73a93f5f6a1f">cutlass::layout::TensorOpMultiplicand::kStrideRank</a></div><div class="ttdeci">static int const kStrideRank</div><div class="ttdoc">Rank of stride vector. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:51</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise_html_ab78912683077ccfdd5c664069de713f1"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#ab78912683077ccfdd5c664069de713f1">cutlass::layout::TensorOpMultiplicandCrosswise::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex operator()(TensorCoord const &amp;coord) const </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:702</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicand_html_aae09584d5e60d401d73484d579722007"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#aae09584d5e60d401d73484d579722007">cutlass::layout::TensorOpMultiplicand::packed</a></div><div class="ttdeci">static CUTLASS_HOST_DEVICE TensorOpMultiplicand packed(TensorCoord const &amp;extent)</div><div class="ttdoc">Helper returns a layout to a tightly packed tensor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:130</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous_html_a4707586d7a899943c62fa9aaee2c612e"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#a4707586d7a899943c62fa9aaee2c612e">cutlass::layout::ColumnMajorTensorOpMultiplicandCongruous::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex operator()(TensorCoord const &amp;coord) const </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:492</div></div>
<div class="ttc" id="cutlass_8h_html"><div class="ttname"><a href="cutlass_8h.html">cutlass.h</a></div><div class="ttdoc">Basic include for CUTLASS. </div></div>
<div class="ttc" id="structcutlass_1_1MatrixCoord_html"><div class="ttname"><a href="structcutlass_1_1MatrixCoord.html">cutlass::MatrixCoord</a></div><div class="ttdef"><b>Definition:</b> matrix_coord.h:39</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4_html_acb38ed15663b5dfc87d071e18ca29682"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#acb38ed15663b5dfc87d071e18ca29682">cutlass::layout::TensorOpMultiplicandCongruous&lt; 32, Crosswise &gt;::TensorOpMultiplicandCongruous</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorOpMultiplicandCongruous(Index ldm=0)</div><div class="ttdoc">Ctor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:373</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1PitchLinearCoord_html_aa828f8dbee3903754b56759c1e6a6043"><div class="ttname"><a href="structcutlass_1_1layout_1_1PitchLinearCoord.html#aa828f8dbee3903754b56759c1e6a6043">cutlass::layout::PitchLinearCoord::strided</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index const &amp; strided() const </div><div class="ttdoc">Returns the column of the coordinate. </div><div class="ttdef"><b>Definition:</b> pitch_linear.h:97</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous_html_aa5e728fbc807f398363a43dafd0118f5"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#aa5e728fbc807f398363a43dafd0118f5">cutlass::layout::RowMajorTensorOpMultiplicandCongruous::capacity</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex capacity(TensorCoord const &amp;extent) const </div><div class="ttdoc">Compute the number of contiguous elements needed to store a tensor with the given size...</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:622</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved_html_ac7b0202ef4a01f7fdff37ac678a60d34"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#ac7b0202ef4a01f7fdff37ac678a60d34">cutlass::layout::TensorOpMultiplicandRowMajorInterleaved::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex operator()(TensorCoord const &amp;coord) const </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:1108</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved_html_a111a796634fba281399e4563f4d441f8"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#a111a796634fba281399e4563f4d441f8">cutlass::layout::TensorOpMultiplicandRowMajorInterleaved::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride &amp; stride()</div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:1130</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise_html_afe8f9e93641b51a0172a0a724e6cfb9c"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#afe8f9e93641b51a0172a0a724e6cfb9c">cutlass::layout::ColumnMajorTensorOpMultiplicandCrosswise::PartitionCount</a></div><div class="ttdeci">typename Base::PartitionCount PartitionCount</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:770</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicand_html_a627189c6253996de45f23c2dc04df72e"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a627189c6253996de45f23c2dc04df72e">cutlass::layout::TensorOpMultiplicand::kAccessSize</a></div><div class="ttdeci">static int const kAccessSize</div><div class="ttdoc">This layout is optimized for 128b accesses. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:70</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4_html_a91245dbf28b2768acf9aa3d77ba20ee1"><div class="ttname"><a href="structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#a91245dbf28b2768acf9aa3d77ba20ee1">cutlass::layout::TensorOpMultiplicandCongruous&lt; 32, Crosswise &gt;::TensorOpMultiplicandCongruous</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorOpMultiplicandCongruous(Stride stride)</div><div class="ttdoc">Ctor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:377</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous_html"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html">cutlass::layout::RowMajorTensorOpMultiplicandCongruous</a></div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm75.h:527</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
