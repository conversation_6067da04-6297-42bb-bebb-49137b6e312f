<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: debug.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_4eeb864c4eec08c7d6b9d3b0352cfdde.html">tools</a></li><li class="navelem"><a class="el" href="dir_88de82f9e8d739a2f42f92d95f0d7933.html">util</a></li><li class="navelem"><a class="el" href="dir_7e9e609009df72bf6226de354e72c328.html">include</a></li><li class="navelem"><a class="el" href="dir_ade2f6ff57439d30f4164e14e54bcf30.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_ff60863f958a43c892071bb1f8a4c81a.html">util</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">tools/util/include/cutlass/util/debug.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="tools_2util_2include_2cutlass_2util_2debug_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2017-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> **************************************************************************************************/</span></div><div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;</div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;</div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="device__dump_8h.html">device_dump.h</a>&quot;</span></div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;</div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;</div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;<span class="comment">/******************************************************************************</span></div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;<span class="comment"> * Debug and logging macros</span></div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;<span class="comment"> ******************************************************************************/</span></div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;</div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;<span class="preprocessor">#if !defined(CUDA_LOG)</span></div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;<span class="preprocessor">#if !defined(__CUDA_ARCH__)</span></div><div class="line"><a name="l00045"></a><span class="lineno"><a class="line" href="tools_2util_2include_2cutlass_2util_2debug_8h.html#a27e3466bcf1ec7fda4f6f95aa0a51177">   45</a></span>&#160;<span class="preprocessor">#define CUDA_LOG(format, ...) printf(format, __VA_ARGS__)</span></div><div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;<span class="preprocessor">#else</span></div><div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;<span class="preprocessor">#define CUDA_LOG(format, ...)                              \</span></div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;<span class="preprocessor">  printf(&quot;[block (%d,%d,%d), thread (%d,%d,%d)]: &quot; format, \</span></div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;<span class="preprocessor">         blockIdx.x,                                       \</span></div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;<span class="preprocessor">         blockIdx.y,                                       \</span></div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;<span class="preprocessor">         blockIdx.z,                                       \</span></div><div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;<span class="preprocessor">         threadIdx.x,                                      \</span></div><div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;<span class="preprocessor">         threadIdx.y,                                      \</span></div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;<span class="preprocessor">         threadIdx.z,                                      \</span></div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;<span class="preprocessor">         __VA_ARGS__);</span></div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;<span class="preprocessor">#endif</span></div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;<span class="preprocessor">#endif</span></div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;</div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;<span class="preprocessor">#if !defined(CUDA_LOG_DEBUG)</span></div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;<span class="preprocessor">#ifdef DEBUG</span></div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;<span class="preprocessor">#define CUDA_LOG_DEBUG(format, ...) CUDA_LOG(format, __VA_ARGS__)</span></div><div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;<span class="preprocessor">#else</span></div><div class="line"><a name="l00066"></a><span class="lineno"><a class="line" href="tools_2util_2include_2cutlass_2util_2debug_8h.html#a8d6986db819719ada8b29d53dfc104a6">   66</a></span>&#160;<span class="preprocessor">#define CUDA_LOG_DEBUG(format, ...)</span></div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;<span class="preprocessor">#endif</span></div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;<span class="preprocessor">#endif</span></div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;</div><div class="line"><a name="l00076"></a><span class="lineno"><a class="line" href="tools_2util_2include_2cutlass_2util_2debug_8h.html#aed67644b6ffaa6d88a2efa09fcaafdce">   76</a></span>&#160;__host__ CUTLASS_DEVICE cudaError_t <a class="code" href="tools_2util_2include_2cutlass_2util_2debug_8h.html#aed67644b6ffaa6d88a2efa09fcaafdce">cuda_perror_impl</a>(cudaError_t error,</div><div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;                                                     <span class="keyword">const</span> <span class="keywordtype">char</span>* filename,</div><div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;                                                     <span class="keywordtype">int</span> line) {</div><div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;  (void)filename;</div><div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;  (void)line;</div><div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;  <span class="keywordflow">if</span> (error) {</div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;<span class="preprocessor">#if !defined(__CUDA_ARCH__)</span></div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;    fprintf(</div><div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;        stderr, <span class="stringliteral">&quot;CUDA error %d [%s, %d]: %s\n&quot;</span>, error, filename, line, cudaGetErrorString(error));</div><div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;    fflush(stderr);</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;<span class="preprocessor">#else</span></div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;    printf(<span class="stringliteral">&quot;CUDA error %d [%s, %d]\n&quot;</span>, error, filename, line);</div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;<span class="preprocessor">#endif</span></div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;  }</div><div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;  <span class="keywordflow">return</span> error;</div><div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;}</div><div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;</div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;<span class="preprocessor">#ifndef CUDA_PERROR</span></div><div class="line"><a name="l00097"></a><span class="lineno"><a class="line" href="tools_2util_2include_2cutlass_2util_2debug_8h.html#aed8337b88d71895f95f8980ef0b3a50b">   97</a></span>&#160;<span class="preprocessor">#define CUDA_PERROR(e) cuda_perror_impl((cudaError_t)(e), __FILE__, __LINE__)</span></div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;<span class="preprocessor">#endif</span></div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;</div><div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;<span class="preprocessor">#ifndef CUDA_PERROR_EXIT</span></div><div class="line"><a name="l00104"></a><span class="lineno"><a class="line" href="tools_2util_2include_2cutlass_2util_2debug_8h.html#a002632ff687c83cff0484476be401f05">  104</a></span>&#160;<span class="preprocessor">#define CUDA_PERROR_EXIT(e)                                     \</span></div><div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;<span class="preprocessor">  if (cuda_perror_impl((cudaError_t)(e), __FILE__, __LINE__)) { \</span></div><div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;<span class="preprocessor">    exit(1);                                                    \</span></div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;<span class="preprocessor">  }</span></div><div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;<span class="preprocessor">#endif</span></div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;</div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;<span class="preprocessor">#ifndef CUDA_PERROR_DEBUG</span></div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;<span class="preprocessor">#ifdef DEBUG</span></div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;<span class="preprocessor">#define CUDA_PERROR_DEBUG(e) CUDA_PERROR(e)</span></div><div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;<span class="preprocessor">#else</span></div><div class="line"><a name="l00117"></a><span class="lineno"><a class="line" href="tools_2util_2include_2cutlass_2util_2debug_8h.html#a36436f5408940a47ac5cdfc9b31648db">  117</a></span>&#160;<span class="preprocessor">#define CUDA_PERROR_DEBUG(e) (e)</span></div><div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;<span class="preprocessor">#endif</span></div><div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;<span class="preprocessor">#endif</span></div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;</div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;</div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;<span class="comment">// A small helper class to dump a type at compile time</span></div><div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;<span class="comment">// Usage:: DumpType&lt;Class&gt;::Class</span></div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T&gt;</div><div class="line"><a name="l00126"></a><span class="lineno"><a class="line" href="structDebugType.html">  126</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structDebugType.html">DebugType</a> {};</div><div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;</div><div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T&gt;</div><div class="line"><a name="l00129"></a><span class="lineno"><a class="line" href="tools_2util_2include_2cutlass_2util_2debug_8h.html#ab7e23b523490567225b20e2c72649f20">  129</a></span>&#160;<span class="keywordtype">void</span> <a class="code" href="tools_2util_2include_2cutlass_2util_2debug_8h.html#ab7e23b523490567225b20e2c72649f20">DebugTypeFunc</a>(T <span class="keyword">const</span>&amp; t) {</div><div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;  T::t;</div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;}</div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;</div><div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;<span class="comment">// A small helper class to dump a compile time constant at compile time</span></div><div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;<span class="comment">// Usage: DumpValue&lt;Class::kConstant&gt;::kConstant</span></div><div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> Value&gt;</div><div class="line"><a name="l00136"></a><span class="lineno"><a class="line" href="structDebugValue.html">  136</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structDebugValue.html">DebugValue</a> {};</div><div class="ttc" id="structDebugValue_html"><div class="ttname"><a href="structDebugValue.html">DebugValue</a></div><div class="ttdef"><b>Definition:</b> tools/util/include/cutlass/util/debug.h:136</div></div>
<div class="ttc" id="device__dump_8h_html"><div class="ttname"><a href="device__dump_8h.html">device_dump.h</a></div><div class="ttdoc">C++ interface to dump fragments and shared memory contents for debugging. </div></div>
<div class="ttc" id="structDebugType_html"><div class="ttname"><a href="structDebugType.html">DebugType</a></div><div class="ttdef"><b>Definition:</b> tools/util/include/cutlass/util/debug.h:126</div></div>
<div class="ttc" id="tools_2util_2include_2cutlass_2util_2debug_8h_html_aed67644b6ffaa6d88a2efa09fcaafdce"><div class="ttname"><a href="tools_2util_2include_2cutlass_2util_2debug_8h.html#aed67644b6ffaa6d88a2efa09fcaafdce">cuda_perror_impl</a></div><div class="ttdeci">__host__ CUTLASS_DEVICE cudaError_t cuda_perror_impl(cudaError_t error, const char *filename, int line)</div><div class="ttdoc">The corresponding error message is printed to stderr (or stdout in device code) along with the suppli...</div><div class="ttdef"><b>Definition:</b> tools/util/include/cutlass/util/debug.h:76</div></div>
<div class="ttc" id="tools_2util_2include_2cutlass_2util_2debug_8h_html_ab7e23b523490567225b20e2c72649f20"><div class="ttname"><a href="tools_2util_2include_2cutlass_2util_2debug_8h.html#ab7e23b523490567225b20e2c72649f20">DebugTypeFunc</a></div><div class="ttdeci">void DebugTypeFunc(T const &amp;t)</div><div class="ttdef"><b>Definition:</b> tools/util/include/cutlass/util/debug.h:129</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
