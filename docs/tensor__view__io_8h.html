<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: tensor_view_io.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_4eeb864c4eec08c7d6b9d3b0352cfdde.html">tools</a></li><li class="navelem"><a class="el" href="dir_88de82f9e8d739a2f42f92d95f0d7933.html">util</a></li><li class="navelem"><a class="el" href="dir_7e9e609009df72bf6226de354e72c328.html">include</a></li><li class="navelem"><a class="el" href="dir_ade2f6ff57439d30f4164e14e54bcf30.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_ff60863f958a43c892071bb1f8a4c81a.html">util</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#namespaces">Namespaces</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">tensor_view_io.h File Reference</div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock"><code>#include &quot;<a class="el" href="core__io_8h_source.html">cutlass/core_io.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="tensor__view_8h_source.html">cutlass/tensor_view.h</a>&quot;</code><br />
</div><div class="textblock"><div class="dynheader">
Include dependency graph for tensor_view_io.h:</div>
<div class="dyncontent">
<div class="center"><img src="tensor__view__io_8h__incl.png" border="0" usemap="#tensor__view__io_8h" alt=""/></div>
<map name="tensor__view__io_8h" id="tensor__view__io_8h">
</map>
</div>
</div><div class="textblock"><div class="dynheader">
This graph shows which files directly or indirectly include this file:</div>
<div class="dyncontent">
<div class="center"><img src="tensor__view__io_8h__dep__incl.png" border="0" usemap="#tensor__view__io_8hdep" alt=""/></div>
<map name="tensor__view__io_8hdep" id="tensor__view__io_8hdep">
</map>
</div>
</div>
<p><a href="tensor__view__io_8h_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="namespaces"></a>
Namespaces</h2></td></tr>
<tr class="memitem:namespacecutlass"><td class="memItemLeft" align="right" valign="top"> &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespacecutlass.html">cutlass</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:namespacecutlass_1_1detail"><td class="memItemLeft" align="right" valign="top"> &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespacecutlass_1_1detail.html">cutlass::detail</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a5b1d94c43ee400779843fb8c297257e3"><td class="memTemplParams" colspan="2">template&lt;typename Element , typename Layout &gt; </td></tr>
<tr class="memitem:a5b1d94c43ee400779843fb8c297257e3"><td class="memTemplItemLeft" align="right" valign="top">std::ostream &amp;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespacecutlass_1_1detail.html#a5b1d94c43ee400779843fb8c297257e3">cutlass::detail::TensorView_WriteLeastSignificantRank</a> (std::ostream &amp;out, TensorView&lt; Element, Layout &gt; const &amp;view, Coord&lt; Layout::kRank &gt; const &amp;start_coord, int rank, std::streamsize width)</td></tr>
<tr class="memdesc:a5b1d94c43ee400779843fb8c297257e3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Helper to write the least significant rank of a <a class="el" href="classcutlass_1_1TensorView.html">TensorView</a>.  <a href="namespacecutlass_1_1detail.html#a5b1d94c43ee400779843fb8c297257e3">More...</a><br /></td></tr>
<tr class="separator:a5b1d94c43ee400779843fb8c297257e3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa643d9608b71a6eedb7dc48d838c9b52"><td class="memTemplParams" colspan="2">template&lt;typename Element , typename Layout &gt; </td></tr>
<tr class="memitem:aa643d9608b71a6eedb7dc48d838c9b52"><td class="memTemplItemLeft" align="right" valign="top">std::ostream &amp;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespacecutlass_1_1detail.html#aa643d9608b71a6eedb7dc48d838c9b52">cutlass::detail::TensorView_WriteRank</a> (std::ostream &amp;out, TensorView&lt; Element, Layout &gt; const &amp;view, Coord&lt; Layout::kRank &gt; const &amp;start_coord, int rank, std::streamsize width)</td></tr>
<tr class="memdesc:aa643d9608b71a6eedb7dc48d838c9b52"><td class="mdescLeft">&#160;</td><td class="mdescRight">Helper to write a rank of a <a class="el" href="classcutlass_1_1TensorView.html">TensorView</a>.  <a href="namespacecutlass_1_1detail.html#aa643d9608b71a6eedb7dc48d838c9b52">More...</a><br /></td></tr>
<tr class="separator:aa643d9608b71a6eedb7dc48d838c9b52"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab6898de9565b78dc1c446901b899208b"><td class="memTemplParams" colspan="2">template&lt;typename Element , typename Layout &gt; </td></tr>
<tr class="memitem:ab6898de9565b78dc1c446901b899208b"><td class="memTemplItemLeft" align="right" valign="top">std::ostream &amp;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespacecutlass.html#ab6898de9565b78dc1c446901b899208b">cutlass::TensorViewWrite</a> (std::ostream &amp;out, TensorView&lt; Element, Layout &gt; const &amp;view)</td></tr>
<tr class="memdesc:ab6898de9565b78dc1c446901b899208b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Prints human-readable representation of a <a class="el" href="classcutlass_1_1TensorView.html">TensorView</a> to an ostream.  <a href="namespacecutlass.html#ab6898de9565b78dc1c446901b899208b">More...</a><br /></td></tr>
<tr class="separator:ab6898de9565b78dc1c446901b899208b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a806ce4c816ee2c20ed853715f74c092c"><td class="memTemplParams" colspan="2">template&lt;typename Element , typename Layout &gt; </td></tr>
<tr class="memitem:a806ce4c816ee2c20ed853715f74c092c"><td class="memTemplItemLeft" align="right" valign="top">std::ostream &amp;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespacecutlass.html#a806ce4c816ee2c20ed853715f74c092c">cutlass::operator&lt;&lt;</a> (std::ostream &amp;out, TensorView&lt; Element, Layout &gt; const &amp;view)</td></tr>
<tr class="memdesc:a806ce4c816ee2c20ed853715f74c092c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Prints human-readable representation of a <a class="el" href="classcutlass_1_1TensorView.html">TensorView</a> to an ostream.  <a href="namespacecutlass.html#a806ce4c816ee2c20ed853715f74c092c">More...</a><br /></td></tr>
<tr class="separator:a806ce4c816ee2c20ed853715f74c092c"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
