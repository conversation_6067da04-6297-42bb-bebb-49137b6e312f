<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: tile_iterator_wmma_tensor_op.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_d9e7e9e63637345b8b26a82972709306.html">epilogue</a></li><li class="navelem"><a class="el" href="dir_e7fd38dbfb1fb5decd4aa6571e13ec6b.html">warp</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">tile_iterator_wmma_tensor_op.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="tile__iterator__wmma__tensor__op_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2017-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> **************************************************************************************************/</span></div><div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;</div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="preprocessor">#if !defined(__clang__)</span></div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;</div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="cutlass_8h.html">cutlass/cutlass.h</a>&quot;</span></div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="wmma__array_8h.html">cutlass/wmma_array.h</a>&quot;</span></div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="layout_2matrix_8h.html">cutlass/layout/matrix.h</a>&quot;</span></div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="pitch__linear_8h.html">cutlass/layout/pitch_linear.h</a>&quot;</span></div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="tensor__ref_8h.html">cutlass/tensor_ref.h</a>&quot;</span></div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;</div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="wmma__tensor__op__policy_8h.html">cutlass/epilogue/warp/wmma_tensor_op_policy.h</a>&quot;</span></div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;</div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;</div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;<span class="keyword">namespace </span>epilogue {</div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;<span class="keyword">namespace </span>warp {</div><div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;</div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;</div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;  <span class="keyword">typename</span> WarpShape,           </div><div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;  <span class="keyword">typename</span> OperatorShape,       </div><div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;  <span class="keyword">typename</span> OperatorFragment,    </div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;  <span class="keyword">typename</span> Layout               </div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;&gt;</div><div class="line"><a name="l00056"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp.html">   56</a></span>&#160;<span class="keyword">class </span><a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp.html">TileIteratorWmmaTensorOp</a>;</div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;</div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;</div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;  <span class="keyword">typename</span> WarpShape_,          </div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;  <span class="keyword">typename</span> OperatorShape_,      </div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;  <span class="keyword">typename</span> OperatorFragment_    </div><div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;&gt;</div><div class="line"><a name="l00066"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html">   66</a></span>&#160;<span class="keyword">class </span><a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp.html">TileIteratorWmmaTensorOp</a>&lt;WarpShape_, OperatorShape_, OperatorFragment_, layout::RowMajor&gt; {</div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;</div><div class="line"><a name="l00069"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#aa1320d8bdb4b3d7877cfa26286ad596b">   69</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#aa1320d8bdb4b3d7877cfa26286ad596b">WarpShape</a> = WarpShape_;</div><div class="line"><a name="l00070"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#abaa1d6e9e398e953e6a21410a2ea267f">   70</a></span>&#160;  <span class="keyword">using</span> OperatorShape = OperatorShape_;</div><div class="line"><a name="l00071"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#a4f7743b6de9c56c7aac0fcc61a7dc98a">   71</a></span>&#160;  <span class="keyword">using</span> OperatorFragment = OperatorFragment_;</div><div class="line"><a name="l00072"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#adc20ef932ebacdc3db1d73399d3c0b4a">   72</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html">Layout</a> = <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>;</div><div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;</div><div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;  <span class="comment">// Derived types</span></div><div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00077"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#a9a53109d4513ac0b14cbe75a05860ebb">   77</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#a9a53109d4513ac0b14cbe75a05860ebb">WmmaDataType</a> = <span class="keyword">typename</span> OperatorFragment::element_type;</div><div class="line"><a name="l00078"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#af51b23496d3634fa13be9d1f1cb33db5">   78</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#af51b23496d3634fa13be9d1f1cb33db5">Element</a> = <span class="keyword">typename</span> cutlass::arch::WmmaToCutlassDataType&lt;WmmaDataType&gt;::Type; </div><div class="line"><a name="l00079"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#ad31bea791a0c7144061b3d6925119597">   79</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef</a> = <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;Element, Layout&gt;</a>;                                      </div><div class="line"><a name="l00080"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#aa6510f6da8eec7a58ca9cc9e40f6d587">   80</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> = <a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>;                                                   </div><div class="line"><a name="l00081"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#a6f65512b815630e138f4df3a120d7475">   81</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#a6f65512b815630e138f4df3a120d7475">Index</a> = <span class="keyword">typename</span> <a class="code" href="classcutlass_1_1TensorRef.html#a11ec4b07a2132e647ca2ebe5112ce5ec">TensorRef::Index</a>;</div><div class="line"><a name="l00082"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#a8dac1c533b728593e7552dfea06c96f4">   82</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#a8dac1c533b728593e7552dfea06c96f4">LongIndex</a> = <span class="keyword">typename</span> <a class="code" href="classcutlass_1_1TensorRef.html#adeada5e33b231f125a4aaeaf963bd3a3">TensorRef::LongIndex</a>;</div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;</div><div class="line"><a name="l00084"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#ae1270cd0109c82ba4f315e3b3de6d1c4">   84</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#ae1270cd0109c82ba4f315e3b3de6d1c4">Policy</a> = WmmaTensorOpPolicy&lt;WarpShape, OperatorShape, Layout&gt;;</div><div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;</div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1MatrixShape.html">Shape</a> = <a class="code" href="structcutlass_1_1MatrixShape.html">MatrixShape</a>&lt;</div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;    Policy::kRowsPerIteration,</div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;    WarpShape::kN</div><div class="line"><a name="l00090"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#acce66f28a0e5d2f9844cfff503f5b2be">   90</a></span>&#160;  &gt;;</div><div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;</div><div class="line"><a name="l00093"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#aa91a85829f23d19fcd9c406aa45683e5">   93</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#aa91a85829f23d19fcd9c406aa45683e5">Fragment</a> = WmmaFragmentArray&lt;OperatorFragment, Policy::OperatorCount::kColumn * Policy::kWmmaFragmentsPerAccess&gt;;</div><div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;</div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;</div><div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;  <span class="comment">//using AccumulatorTile = typename Operator::FragmentC;</span></div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;</div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;</div><div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;  <span class="comment">// (Epilogue shared memory padding for WMMA Gemm kernel is set to run optimaly on Turing)</span></div><div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1MatrixShape.html">Padding</a> = <a class="code" href="structcutlass_1_1MatrixShape.html">MatrixShape</a>&lt;</div><div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;    0,</div><div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;    4 * Policy::kElementsPerAccess</div><div class="line"><a name="l00105"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#aab81005bb67d46819c7d9c2571295876">  105</a></span>&#160;  &gt;;</div><div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;</div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;</div><div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;  <span class="comment">//using AccessType = AlignedArray&lt;Element, Policy::kElementsPerAccess&gt;;</span></div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;</div><div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;</div><div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;  <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef</a> ref_;</div><div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;</div><div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;</div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;</div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00124"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#ab2d5b947844de7c567afabc8a86b52c0">  124</a></span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#ab2d5b947844de7c567afabc8a86b52c0">TileIteratorWmmaTensorOp</a>(): ref_(<a class="code" href="platform_8h.html#ab979d9d4b4923f7c54d6caa6e1a61936">nullptr</a>) { </div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;</div><div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;  }</div><div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;</div><div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00130"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#a89c46e0f38888b0e9168961a07367844">  130</a></span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#a89c46e0f38888b0e9168961a07367844">TileIteratorWmmaTensorOp</a>(</div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;    <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef</a> <span class="keyword">const</span> &amp;ref,</div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;    <span class="keywordtype">unsigned</span> lane_id</div><div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;  ): ref_(ref) {</div><div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;  }</div><div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;</div><div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00138"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#a2c2595d138f2166285948eb8384149a1">  138</a></span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp.html">TileIteratorWmmaTensorOp</a> &amp; <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#a2c2595d138f2166285948eb8384149a1">add_pointer_offset</a>(<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#a6f65512b815630e138f4df3a120d7475">Index</a> pointer_offset) {</div><div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;    ref_.<a class="code" href="classcutlass_1_1TensorRef.html#a6bbcd0e512915565cabfeccdb1b6417d">add_pointer_offset</a>(pointer_offset);</div><div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;  }</div><div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;</div><div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00145"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#aef2b622345c91bc99f1baec9cefc8392">  145</a></span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp.html">TileIteratorWmmaTensorOp</a> &amp; <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#aef2b622345c91bc99f1baec9cefc8392">add_tile_offset</a>(<a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;tile_offset) {</div><div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;    ref_.<a class="code" href="classcutlass_1_1TensorRef.html#a4bed879c428963070de8ffbdc5d6e4f9">add_coord_offset</a>({tile_offset.<a class="code" href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">row</a>() * OperatorShape::kM, tile_offset.<a class="code" href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">column</a>() * WarpShape::kN});</div><div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;  }</div><div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;</div><div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00152"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#a10fd20879beda76f48772465a1b491f4">  152</a></span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp.html">TileIteratorWmmaTensorOp</a> &amp; <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#a10fd20879beda76f48772465a1b491f4">operator+=</a>(<a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;tile_offset) {</div><div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;    add_tile_offset(tile_offset);</div><div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;  }</div><div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;</div><div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00159"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#a672b6561ba5c275b6237725e9280b6c3">  159</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#a672b6561ba5c275b6237725e9280b6c3">store_with_pointer_offset</a>(<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#aa91a85829f23d19fcd9c406aa45683e5">Fragment</a> <span class="keyword">const</span> &amp;frag, <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#a6f65512b815630e138f4df3a120d7475">Index</a> pointer_offset) {</div><div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;</div><div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;    <span class="keywordflow">for</span>(<span class="keywordtype">int</span> n=0; n &lt; Policy::OperatorCount::kColumn; n++) {</div><div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;      </div><div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;      <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#a9a53109d4513ac0b14cbe75a05860ebb">WmmaDataType</a>* ptr = <span class="keyword">reinterpret_cast&lt;</span><a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#a9a53109d4513ac0b14cbe75a05860ebb">WmmaDataType</a>*<span class="keyword">&gt;</span> (ref_.<a class="code" href="classcutlass_1_1TensorRef.html#ac7db3ca62ab1dfe0d3ea08bcadbc9352">data</a>() + ref_.<a class="code" href="classcutlass_1_1TensorRef.html#a4166ac2a0754574ac21d5d57d74f34e5">offset</a>({0, n * OperatorShape::kN}) + pointer_offset);</div><div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;</div><div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;      nvcuda::wmma::store_matrix_sync(</div><div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;        ptr, </div><div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;        frag[n], </div><div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;        ref_.<a class="code" href="classcutlass_1_1TensorRef.html#a191e88bc0fb310be655d700e937ab97c">stride</a>()[0], </div><div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;        nvcuda::wmma::layout_t::mem_row_major</div><div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;      ); </div><div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;    </div><div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;    }</div><div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;  }</div><div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;</div><div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00177"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#af8b63e0d1f9a4547c8c7a464fabf1dd0">  177</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#af8b63e0d1f9a4547c8c7a464fabf1dd0">store</a>(<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#aa91a85829f23d19fcd9c406aa45683e5">Fragment</a> <span class="keyword">const</span> &amp;frag) {</div><div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;    store_with_pointer_offset(frag, 0);</div><div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;  }</div><div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;</div><div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00183"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#a462bcb6dc93d488ea524812ce154a506">  183</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#a462bcb6dc93d488ea524812ce154a506">load_with_pointer_offset</a>(<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#aa91a85829f23d19fcd9c406aa45683e5">Fragment</a> &amp;frag, <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#a6f65512b815630e138f4df3a120d7475">Index</a> pointer_offset)<span class="keyword"> const </span>{</div><div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160; </div><div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;    <span class="keywordflow">for</span>(<span class="keywordtype">int</span> n=0; n &lt; Policy::OperatorCount::kColumn; n++) {</div><div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;</div><div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;      <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#a9a53109d4513ac0b14cbe75a05860ebb">WmmaDataType</a>* ptr = <span class="keyword">reinterpret_cast&lt;</span><a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#a9a53109d4513ac0b14cbe75a05860ebb">WmmaDataType</a>*<span class="keyword">&gt;</span> (ref_.<a class="code" href="classcutlass_1_1TensorRef.html#ac7db3ca62ab1dfe0d3ea08bcadbc9352">data</a>() + ref_.<a class="code" href="classcutlass_1_1TensorRef.html#a4166ac2a0754574ac21d5d57d74f34e5">offset</a>({0, n * OperatorShape::kN}) + pointer_offset);</div><div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;</div><div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;      nvcuda::wmma::load_matrix_sync(         </div><div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;        frag[n], </div><div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;        ptr,</div><div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;        ref_.<a class="code" href="classcutlass_1_1TensorRef.html#a191e88bc0fb310be655d700e937ab97c">stride</a>()[0], </div><div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;        nvcuda::wmma::layout_t::mem_row_major</div><div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;      ); </div><div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;    </div><div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;    }</div><div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;  }</div><div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;</div><div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00201"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#a71aad93445a7ff852d1b7cea2ec072a3">  201</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#a71aad93445a7ff852d1b7cea2ec072a3">load</a>(<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#aa91a85829f23d19fcd9c406aa45683e5">Fragment</a> &amp;frag)<span class="keyword"> const </span>{</div><div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;    load_with_pointer_offset(frag, 0);</div><div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;  }</div><div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;};</div><div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;</div><div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;</div><div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;} <span class="comment">// namespace warp</span></div><div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160;} <span class="comment">// namespace epilogue</span></div><div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;} <span class="comment">// namespace cutlass</span></div><div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;</div><div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;</div><div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160;<span class="preprocessor">#endif // !defined(__clang__)</span></div><div class="line"><a name="l00215"></a><span class="lineno">  215</span>&#160;</div><div class="ttc" id="structcutlass_1_1MatrixShape_html"><div class="ttname"><a href="structcutlass_1_1MatrixShape.html">cutlass::MatrixShape</a></div><div class="ttdoc">Describes the size of a matrix tile. </div><div class="ttdef"><b>Definition:</b> matrix_shape.h:42</div></div>
<div class="ttc" id="structcutlass_1_1MatrixCoord_html_afbdcc5ca5b91f11f29046667b0bfde7b"><div class="ttname"><a href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">cutlass::MatrixCoord::column</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index const &amp; column() const </div><div class="ttdoc">Returns the column of the coordinate. </div><div class="ttdef"><b>Definition:</b> matrix_coord.h:85</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4_html_a2c2595d138f2166285948eb8384149a1"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#a2c2595d138f2166285948eb8384149a1">cutlass::epilogue::warp::TileIteratorWmmaTensorOp&lt; WarpShape_, OperatorShape_, OperatorFragment_, layout::RowMajor &gt;::add_pointer_offset</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TileIteratorWmmaTensorOp &amp; add_pointer_offset(Index pointer_offset)</div><div class="ttdoc">Adds a pointer offset. </div><div class="ttdef"><b>Definition:</b> tile_iterator_wmma_tensor_op.h:138</div></div>
<div class="ttc" id="wmma__array_8h_html"><div class="ttname"><a href="wmma__array_8h.html">wmma_array.h</a></div><div class="ttdoc">Statically sized array of elements that accommodates all CUTLASS-supported numeric types and is safe ...</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4_html_aef2b622345c91bc99f1baec9cefc8392"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#aef2b622345c91bc99f1baec9cefc8392">cutlass::epilogue::warp::TileIteratorWmmaTensorOp&lt; WarpShape_, OperatorShape_, OperatorFragment_, layout::RowMajor &gt;::add_tile_offset</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TileIteratorWmmaTensorOp &amp; add_tile_offset(TensorCoord const &amp;tile_offset)</div><div class="ttdoc">advances in units of whole tiles along the logical coordinate space of the tensor ...</div><div class="ttdef"><b>Definition:</b> tile_iterator_wmma_tensor_op.h:145</div></div>
<div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="tensor__ref_8h_html"><div class="ttname"><a href="tensor__ref_8h.html">tensor_ref.h</a></div><div class="ttdoc">Defines a structure containing strides, bounds, and a pointer to tensor data. </div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4_html_ab2d5b947844de7c567afabc8a86b52c0"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#ab2d5b947844de7c567afabc8a86b52c0">cutlass::epilogue::warp::TileIteratorWmmaTensorOp&lt; WarpShape_, OperatorShape_, OperatorFragment_, layout::RowMajor &gt;::TileIteratorWmmaTensorOp</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TileIteratorWmmaTensorOp()</div><div class="ttdoc">Default constructor. </div><div class="ttdef"><b>Definition:</b> tile_iterator_wmma_tensor_op.h:124</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_ac7db3ca62ab1dfe0d3ea08bcadbc9352"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#ac7db3ca62ab1dfe0d3ea08bcadbc9352">cutlass::TensorRef::data</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Element * data() const </div><div class="ttdoc">Returns the pointer to referenced data. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:254</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4_html_ae1270cd0109c82ba4f315e3b3de6d1c4"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#ae1270cd0109c82ba4f315e3b3de6d1c4">cutlass::epilogue::warp::TileIteratorWmmaTensorOp&lt; WarpShape_, OperatorShape_, OperatorFragment_, layout::RowMajor &gt;::Policy</a></div><div class="ttdeci">WmmaTensorOpPolicy&lt; WarpShape, OperatorShape, Layout &gt; Policy</div><div class="ttdef"><b>Definition:</b> tile_iterator_wmma_tensor_op.h:84</div></div>
<div class="ttc" id="structcutlass_1_1MatrixCoord_html_a0580610f28427e376b24b71f67602d03"><div class="ttname"><a href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">cutlass::MatrixCoord::row</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index const &amp; row() const </div><div class="ttdoc">Returns the row of the coordinate. </div><div class="ttdef"><b>Definition:</b> matrix_coord.h:77</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4_html_a6f65512b815630e138f4df3a120d7475"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#a6f65512b815630e138f4df3a120d7475">cutlass::epilogue::warp::TileIteratorWmmaTensorOp&lt; WarpShape_, OperatorShape_, OperatorFragment_, layout::RowMajor &gt;::Index</a></div><div class="ttdeci">typename TensorRef::Index Index</div><div class="ttdef"><b>Definition:</b> tile_iterator_wmma_tensor_op.h:81</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4_html_a10fd20879beda76f48772465a1b491f4"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#a10fd20879beda76f48772465a1b491f4">cutlass::epilogue::warp::TileIteratorWmmaTensorOp&lt; WarpShape_, OperatorShape_, OperatorFragment_, layout::RowMajor &gt;::operator+=</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TileIteratorWmmaTensorOp &amp; operator+=(TensorCoord const &amp;tile_offset)</div><div class="ttdef"><b>Definition:</b> tile_iterator_wmma_tensor_op.h:152</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_a4bed879c428963070de8ffbdc5d6e4f9"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#a4bed879c428963070de8ffbdc5d6e4f9">cutlass::TensorRef::add_coord_offset</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorRef &amp; add_coord_offset(TensorCoord const &amp;coord)</div><div class="ttdoc">Adds an offset to each pointer. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:326</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4_html_a9a53109d4513ac0b14cbe75a05860ebb"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#a9a53109d4513ac0b14cbe75a05860ebb">cutlass::epilogue::warp::TileIteratorWmmaTensorOp&lt; WarpShape_, OperatorShape_, OperatorFragment_, layout::RowMajor &gt;::WmmaDataType</a></div><div class="ttdeci">typename OperatorFragment::element_type WmmaDataType</div><div class="ttdef"><b>Definition:</b> tile_iterator_wmma_tensor_op.h:77</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_a191e88bc0fb310be655d700e937ab97c"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#a191e88bc0fb310be655d700e937ab97c">cutlass::TensorRef::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride stride() const </div><div class="ttdoc">Returns the layout object&amp;#39;s stride vector. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:277</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4_html_a71aad93445a7ff852d1b7cea2ec072a3"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#a71aad93445a7ff852d1b7cea2ec072a3">cutlass::epilogue::warp::TileIteratorWmmaTensorOp&lt; WarpShape_, OperatorShape_, OperatorFragment_, layout::RowMajor &gt;::load</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void load(Fragment &amp;frag) const </div><div class="ttdoc">Load. </div><div class="ttdef"><b>Definition:</b> tile_iterator_wmma_tensor_op.h:201</div></div>
<div class="ttc" id="platform_8h_html_ab979d9d4b4923f7c54d6caa6e1a61936"><div class="ttname"><a href="platform_8h.html#ab979d9d4b4923f7c54d6caa6e1a61936">nullptr</a></div><div class="ttdeci">#define nullptr</div><div class="ttdoc">nullptr </div><div class="ttdef"><b>Definition:</b> platform.h:144</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html"><div class="ttname"><a href="classcutlass_1_1TensorRef.html">cutlass::TensorRef&lt; Element, Layout &gt;</a></div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4_html_a672b6561ba5c275b6237725e9280b6c3"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#a672b6561ba5c275b6237725e9280b6c3">cutlass::epilogue::warp::TileIteratorWmmaTensorOp&lt; WarpShape_, OperatorShape_, OperatorFragment_, layout::RowMajor &gt;::store_with_pointer_offset</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void store_with_pointer_offset(Fragment const &amp;frag, Index pointer_offset)</div><div class="ttdoc">Store. </div><div class="ttdef"><b>Definition:</b> tile_iterator_wmma_tensor_op.h:159</div></div>
<div class="ttc" id="cutlass_8h_html_a28c2443a142676d3d71effdae1a986b1"><div class="ttname"><a href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="ttdeci">#define CUTLASS_HOST_DEVICE</div><div class="ttdef"><b>Definition:</b> cutlass.h:89</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_a4166ac2a0754574ac21d5d57d74f34e5"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#a4166ac2a0754574ac21d5d57d74f34e5">cutlass::TensorRef::offset</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex offset(TensorCoord const &amp;coord) const </div><div class="ttdoc">Computes the offset of an index from the origin of the tensor. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:301</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4_html_a89c46e0f38888b0e9168961a07367844"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#a89c46e0f38888b0e9168961a07367844">cutlass::epilogue::warp::TileIteratorWmmaTensorOp&lt; WarpShape_, OperatorShape_, OperatorFragment_, layout::RowMajor &gt;::TileIteratorWmmaTensorOp</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TileIteratorWmmaTensorOp(TensorRef const &amp;ref, unsigned lane_id)</div><div class="ttdoc">Constructor from TensorRef. </div><div class="ttdef"><b>Definition:</b> tile_iterator_wmma_tensor_op.h:130</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4_html_aa1320d8bdb4b3d7877cfa26286ad596b"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#aa1320d8bdb4b3d7877cfa26286ad596b">cutlass::epilogue::warp::TileIteratorWmmaTensorOp&lt; WarpShape_, OperatorShape_, OperatorFragment_, layout::RowMajor &gt;::WarpShape</a></div><div class="ttdeci">WarpShape_ WarpShape</div><div class="ttdef"><b>Definition:</b> tile_iterator_wmma_tensor_op.h:69</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4_html_a462bcb6dc93d488ea524812ce154a506"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#a462bcb6dc93d488ea524812ce154a506">cutlass::epilogue::warp::TileIteratorWmmaTensorOp&lt; WarpShape_, OperatorShape_, OperatorFragment_, layout::RowMajor &gt;::load_with_pointer_offset</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void load_with_pointer_offset(Fragment &amp;frag, Index pointer_offset) const </div><div class="ttdoc">Load. </div><div class="ttdef"><b>Definition:</b> tile_iterator_wmma_tensor_op.h:183</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_a11ec4b07a2132e647ca2ebe5112ce5ec"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#a11ec4b07a2132e647ca2ebe5112ce5ec">cutlass::TensorRef::Index</a></div><div class="ttdeci">typename Layout::Index Index</div><div class="ttdoc">Index type. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:165</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1RowMajor_html"><div class="ttname"><a href="classcutlass_1_1layout_1_1RowMajor.html">cutlass::layout::RowMajor</a></div><div class="ttdoc">Mapping function for row-major matrices. </div><div class="ttdef"><b>Definition:</b> layout/matrix.h:50</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4_html_a8dac1c533b728593e7552dfea06c96f4"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#a8dac1c533b728593e7552dfea06c96f4">cutlass::epilogue::warp::TileIteratorWmmaTensorOp&lt; WarpShape_, OperatorShape_, OperatorFragment_, layout::RowMajor &gt;::LongIndex</a></div><div class="ttdeci">typename TensorRef::LongIndex LongIndex</div><div class="ttdef"><b>Definition:</b> tile_iterator_wmma_tensor_op.h:82</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4_html_aa91a85829f23d19fcd9c406aa45683e5"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#aa91a85829f23d19fcd9c406aa45683e5">cutlass::epilogue::warp::TileIteratorWmmaTensorOp&lt; WarpShape_, OperatorShape_, OperatorFragment_, layout::RowMajor &gt;::Fragment</a></div><div class="ttdeci">WmmaFragmentArray&lt; OperatorFragment, Policy::OperatorCount::kColumn *Policy::kWmmaFragmentsPerAccess &gt; Fragment</div><div class="ttdoc">This is the fragment size produced by one access of the iterator. </div><div class="ttdef"><b>Definition:</b> tile_iterator_wmma_tensor_op.h:93</div></div>
<div class="ttc" id="wmma__tensor__op__policy_8h_html"><div class="ttname"><a href="wmma__tensor__op__policy_8h.html">wmma_tensor_op_policy.h</a></div><div class="ttdoc">Defines basic structures needed for implementing the warp-scoped phase of the epilogue. These quantities assume a &amp;#39;column-major&amp;#39; arrangement of TensorOp instructions, of which a row-oriented slice is visible per iteration. </div></div>
<div class="ttc" id="layout_2matrix_8h_html"><div class="ttname"><a href="layout_2matrix_8h.html">matrix.h</a></div><div class="ttdoc">Defines layout functions used by TensorRef and derived classes. </div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4_html_af51b23496d3634fa13be9d1f1cb33db5"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#af51b23496d3634fa13be9d1f1cb33db5">cutlass::epilogue::warp::TileIteratorWmmaTensorOp&lt; WarpShape_, OperatorShape_, OperatorFragment_, layout::RowMajor &gt;::Element</a></div><div class="ttdeci">typename cutlass::arch::WmmaToCutlassDataType&lt; WmmaDataType &gt;::Type Element</div><div class="ttdoc">Data Type of element stored in nvcuda::wmma::frament. </div><div class="ttdef"><b>Definition:</b> tile_iterator_wmma_tensor_op.h:78</div></div>
<div class="ttc" id="pitch__linear_8h_html"><div class="ttname"><a href="pitch__linear_8h.html">pitch_linear.h</a></div><div class="ttdoc">Defines layout functions used by TensorRef and derived classes for pitch-linear memory. </div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_a6bbcd0e512915565cabfeccdb1b6417d"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#a6bbcd0e512915565cabfeccdb1b6417d">cutlass::TensorRef::add_pointer_offset</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorRef &amp; add_pointer_offset(LongIndex offset_)</div><div class="ttdoc">Adds an offset to each pointer. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:319</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_html"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp.html">cutlass::epilogue::warp::TileIteratorWmmaTensorOp</a></div><div class="ttdoc">Template for reading and writing tiles of accumulators to shared memory. </div><div class="ttdef"><b>Definition:</b> tile_iterator_wmma_tensor_op.h:56</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4_html_af8b63e0d1f9a4547c8c7a464fabf1dd0"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorWmmaTensorOp_3_01WarpShape___00_01OperatorShape_fd6a91cd8bbd07ecd1344326b830e3a4.html#af8b63e0d1f9a4547c8c7a464fabf1dd0">cutlass::epilogue::warp::TileIteratorWmmaTensorOp&lt; WarpShape_, OperatorShape_, OperatorFragment_, layout::RowMajor &gt;::store</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void store(Fragment const &amp;frag)</div><div class="ttdoc">Store. </div><div class="ttdef"><b>Definition:</b> tile_iterator_wmma_tensor_op.h:177</div></div>
<div class="ttc" id="cutlass_8h_html"><div class="ttname"><a href="cutlass_8h.html">cutlass.h</a></div><div class="ttdoc">Basic include for CUTLASS. </div></div>
<div class="ttc" id="structcutlass_1_1MatrixCoord_html"><div class="ttname"><a href="structcutlass_1_1MatrixCoord.html">cutlass::MatrixCoord</a></div><div class="ttdef"><b>Definition:</b> matrix_coord.h:39</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_adeada5e33b231f125a4aaeaf963bd3a3"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#adeada5e33b231f125a4aaeaf963bd3a3">cutlass::TensorRef::LongIndex</a></div><div class="ttdeci">typename Layout::LongIndex LongIndex</div><div class="ttdoc">Long index used for pointer offsets. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:168</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
