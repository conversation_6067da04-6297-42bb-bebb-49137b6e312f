var searchData=
[
  ['randomfunc',['RandomFunc',['../structcutlass_1_1reference_1_1device_1_1detail_1_1TensorFillRandomGaussianFunc.html#a411755eb3303cc9681111ac2fabb2a48',1,'cutlass::reference::device::detail::TensorFillRandomGaussianFunc::RandomFunc()'],['../structcutlass_1_1reference_1_1device_1_1detail_1_1TensorFillRandomUniformFunc.html#afafde9750cffecbaf6d0dba8a55fc5d5',1,'cutlass::reference::device::detail::TensorFillRandomUniformFunc::RandomFunc()']]],
  ['real',['Real',['../structcutlass_1_1reference_1_1host_1_1detail_1_1RandomUniformFunc.html#a317b294d017b81224eb1aca2742e8a3c',1,'cutlass::reference::host::detail::RandomUniformFunc::Real()'],['../structcutlass_1_1reference_1_1host_1_1detail_1_1RandomUniformFunc_3_01complex_3_01Element_01_4_01_4.html#abd7e66b999df7719f6ac77f0a82a0d5d',1,'cutlass::reference::host::detail::RandomUniformFunc&lt; complex&lt; Element &gt; &gt;::Real()']]],
  ['reductionkernel',['ReductionKernel',['../classcutlass_1_1gemm_1_1device_1_1GemmSplitKParallel.html#aaf83264eb3effceee610d9547ddf32e9',1,'cutlass::gemm::device::GemmSplitKParallel::ReductionKernel()'],['../classcutlass_1_1gemm_1_1device_1_1GemmSplitKParallel_3_01ElementA___00_01LayoutA___00_01ElementBbe7c1f7154ad5b5bf9d4d28301e2b457.html#aa69c465611c07990cdc79605c16b04ff',1,'cutlass::gemm::device::GemmSplitKParallel&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ConvertScaledOp_, ReductionOp_, ThreadblockSwizzle_, Stages, kAlignmentA, kAlignmentB, Operator_ &gt;::ReductionKernel()']]],
  ['reductionop',['ReductionOp',['../classcutlass_1_1gemm_1_1device_1_1GemmSplitKParallel.html#ac82ba3da12b03bc91586a3947ce99fc5',1,'cutlass::gemm::device::GemmSplitKParallel::ReductionOp()'],['../classcutlass_1_1gemm_1_1device_1_1GemmSplitKParallel_3_01ElementA___00_01LayoutA___00_01ElementBbe7c1f7154ad5b5bf9d4d28301e2b457.html#a2d8d3a504dd8807ed09e25f37a658783',1,'cutlass::gemm::device::GemmSplitKParallel&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ConvertScaledOp_, ReductionOp_, ThreadblockSwizzle_, Stages, kAlignmentA, kAlignmentB, Operator_ &gt;::ReductionOp()'],['../classcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK.html#a9fed5689109358e708a27d487db15232',1,'cutlass::reduction::kernel::ReduceSplitK::ReductionOp()']]],
  ['reference',['reference',['../structcutlass_1_1AlignedBuffer.html#afa029189fb46528b5eb5f50060cbf28e',1,'cutlass::AlignedBuffer::reference()'],['../classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a5827968c9c3deca639f5981ad895fe67',1,'cutlass::Array&lt; T, N, true &gt;::reference()'],['../classcutlass_1_1TensorRef.html#a0aba29ebf8715d217817a49356f95d3f',1,'cutlass::TensorRef::Reference()'],['../classcutlass_1_1TensorView.html#a4d8af7fd842866a218722e7686d6bc3c',1,'cutlass::TensorView::Reference()'],['../classcutlass_1_1thread_1_1Matrix.html#a3c9535ffa08c91d0040b09667c81b201',1,'cutlass::thread::Matrix::Reference()'],['../classcutlass_1_1HostTensor.html#a33698c7aa33255e7a2e7abc298e28f39',1,'cutlass::HostTensor::Reference()']]],
  ['result_5ftype',['result_type',['../structcutlass_1_1NumericConverter.html#a46b1b5c0c96c50176578fcd6f915ee8c',1,'cutlass::NumericConverter::result_type()'],['../structcutlass_1_1NumericConverter_3_01int8__t_00_01float_00_01Round_01_4.html#acce8af4bfd5837006dc66803fee491ca',1,'cutlass::NumericConverter&lt; int8_t, float, Round &gt;::result_type()'],['../structcutlass_1_1NumericConverter_3_01T_00_01T_00_01Round_01_4.html#a10b599dbfd3f4da945dc3ddb93e48ded',1,'cutlass::NumericConverter&lt; T, T, Round &gt;::result_type()'],['../structcutlass_1_1NumericConverter_3_01float_00_01half__t_00_01Round_01_4.html#ac1adcd31b0db52e8682680c9927a05c8',1,'cutlass::NumericConverter&lt; float, half_t, Round &gt;::result_type()'],['../structcutlass_1_1NumericConverter_3_01half__t_00_01float_00_01FloatRoundStyle_1_1round__to__nearest_01_4.html#a5dc993f38c6eedd917008e6c839c6300',1,'cutlass::NumericConverter&lt; half_t, float, FloatRoundStyle::round_to_nearest &gt;::result_type()'],['../structcutlass_1_1NumericConverter_3_01half__t_00_01float_00_01FloatRoundStyle_1_1round__toward__zero_01_4.html#aa5bfe0288e538f1df94d74fa52aa1e17',1,'cutlass::NumericConverter&lt; half_t, float, FloatRoundStyle::round_toward_zero &gt;::result_type()'],['../structcutlass_1_1NumericConverterClamp.html#a8e13f626ae7a0ebb228bf1ef59d4ed09',1,'cutlass::NumericConverterClamp::result_type()'],['../structcutlass_1_1NumericArrayConverter.html#a5ed5dd75f4310d887fa527dd177c6f91',1,'cutlass::NumericArrayConverter::result_type()'],['../structcutlass_1_1NumericArrayConverter_3_01half__t_00_01float_00_012_00_01FloatRoundStyle_1_1round__to__nearest_01_4.html#ad50d5ce8c7047513745c1fab77c3988c',1,'cutlass::NumericArrayConverter&lt; half_t, float, 2, FloatRoundStyle::round_to_nearest &gt;::result_type()'],['../structcutlass_1_1NumericArrayConverter_3_01float_00_01half__t_00_012_00_01Round_01_4.html#af14ce6b66b30bd8942a6693e6a06c8f9',1,'cutlass::NumericArrayConverter&lt; float, half_t, 2, Round &gt;::result_type()'],['../structcutlass_1_1NumericArrayConverter_3_01half__t_00_01float_00_01N_00_01Round_01_4.html#a411c3cd15a3f03d360c96c05025cc3d3',1,'cutlass::NumericArrayConverter&lt; half_t, float, N, Round &gt;::result_type()'],['../structcutlass_1_1NumericArrayConverter_3_01float_00_01half__t_00_01N_00_01Round_01_4.html#a644b3f49bf10e99d6a53061b350ac693',1,'cutlass::NumericArrayConverter&lt; float, half_t, N, Round &gt;::result_type()']]],
  ['rowarrangement',['RowArrangement',['../structcutlass_1_1epilogue_1_1threadblock_1_1OutputTileOptimalThreadMap_1_1Detail.html#a028dd493fdfc53f5189aa9936e170941',1,'cutlass::epilogue::threadblock::OutputTileOptimalThreadMap::Detail']]],
  ['rowvector',['RowVector',['../namespacecutlass_1_1thread.html#a4a8bdc0c4e09b113284e07228704f98a',1,'cutlass::thread']]]
];
