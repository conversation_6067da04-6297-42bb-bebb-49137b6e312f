var searchData=
[
  ['gemm_5fbatched_2eh',['gemm_batched.h',['../kernel_2gemm__batched_8h.html',1,'']]],
  ['gemm_5fsplitk_5fparallel_2eh',['gemm_splitk_parallel.h',['../kernel_2gemm__splitk__parallel_8h.html',1,'']]],
  ['k',['k',['../structcutlass_1_1gemm_1_1GemmCoord.html#a18835ec84cbb6250143327e93697c7e9',1,'cutlass::gemm::GemmCoord::k() const '],['../structcutlass_1_1gemm_1_1GemmCoord.html#ae12eb84ec47ddf01b4d459c7aabc22ad',1,'cutlass::gemm::GemmCoord::k()'],['../structcutlass_1_1gemm_1_1BatchedGemmCoord.html#ab85de6cc625dd61523b37ce8d36a7e6e',1,'cutlass::gemm::BatchedGemmCoord::k() const '],['../structcutlass_1_1gemm_1_1BatchedGemmCoord.html#a7d9a8d73ad10cc2615207129ff228393',1,'cutlass::gemm::BatchedGemmCoord::k()']]],
  ['ka',['kA',['../namespacecutlass_1_1gemm.html#a34338284023da7403c9ecbd3f406b2a6a30f767aa191cd5d261e767fd78393607',1,'cutlass::gemm']]],
  ['kaccessesperinterleavedtile',['kAccessesPerInterleavedTile',['../structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_1d48185f49e4d066f8e9327bf0856b7f.html#a61170e3b111822d0f43c0b7b9d2b6d85',1,'cutlass::epilogue::warp::VoltaTensorOpPolicy&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, half_t, layout::RowMajor &gt;::kAccessesPerInterleavedTile()'],['../structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_136ce744d4c1c6e8707f5a9785196194.html#a5df324e55af9ac23dea4056f6e5b9bd5',1,'cutlass::epilogue::warp::VoltaTensorOpPolicy&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, float, layout::RowMajor &gt;::kAccessesPerInterleavedTile()']]],
  ['kaccessesperiteration',['kAccessesPerIteration',['../structcutlass_1_1epilogue_1_1warp_1_1SimtPolicy_3_01WarpShape___00_01Operator___00_01layout_1_1Rcef1c60e23e997017ae176c92931151d.html#aba347419d996527f17d0775a1fa8ab73',1,'cutlass::epilogue::warp::SimtPolicy&lt; WarpShape_, Operator_, layout::RowMajor, MmaSimtPolicy_ &gt;']]],
  ['kaccessesperquad',['kAccessesPerQuad',['../structcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gemmffcab2297c8de8d0013602a39c525b78.html#a1bf53fa314f37d678046132d398f5124',1,'cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, half_t, layout::RowMajor &gt;::Detail::kAccessesPerQuad()'],['../structcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gemm770cbca45441d295d5d7433e8222a700.html#a5ec1c03e8642655de409830ba9d28aac',1,'cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, float, layout::RowMajor &gt;::Detail::kAccessesPerQuad()']]],
  ['kaccessespervector',['kAccessesPerVector',['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator_3_01Shape___00_01Elemen784a0e9da3f55064c47e5613791f51f7.html#acc469195f8f34b8cbd37d348970c89d7',1,'cutlass::transform::threadblock::PredicatedTileAccessIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessType_ &gt;::kAccessesPerVector()'],['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator_3_01Shape___00_01Elemen89c687c583745a73cb485041911a4c4e.html#a4baae6986244b7a5aefb4b273d229a5a',1,'cutlass::transform::threadblock::PredicatedTileAccessIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, AccessType_ &gt;::kAccessesPerVector()'],['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator_3_01Shape___00_01Elemen9838736ad62fae54213fbaf722a989ab.html#ab27d8f0b0917b2cb6097a4931f420bb2',1,'cutlass::transform::threadblock::PredicatedTileAccessIterator&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, AccessType_ &gt;::kAccessesPerVector()'],['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator_3_01Shape___00_01Elemenab63a1e105bf37f6371516cb9e2c5a7a.html#aca8ffdaa9ae7878c646af388927cc619',1,'cutlass::transform::threadblock::PredicatedTileAccessIterator&lt; Shape_, Element_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessType_ &gt;::kAccessesPerVector()'],['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator_3_01Shape___00_01Elemen809793e785fb4211888c6b4e5dcfcb39.html#acc8bf566f87197a3ae7f53c7914c5dd4',1,'cutlass::transform::threadblock::PredicatedTileAccessIterator&lt; Shape_, Element_, layout::RowMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessType_ &gt;::kAccessesPerVector()'],['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#af99ae79be70bd5bf0c814759ca40c95d',1,'cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessSize &gt;::kAccessesPerVector()']]],
  ['kaccessquaddelta',['kAccessQuadDelta',['../structcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gemmffcab2297c8de8d0013602a39c525b78.html#a9f9d4ce2c61db3266a4c58876ed697ee',1,'cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, half_t, layout::RowMajor &gt;::Detail::kAccessQuadDelta()'],['../structcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gemm770cbca45441d295d5d7433e8222a700.html#a90cd832bb0f9bc8d7d77181c24a9d782',1,'cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, float, layout::RowMajor &gt;::Detail::kAccessQuadDelta()']]],
  ['kaccessrows',['kAccessRows',['../structcutlass_1_1epilogue_1_1threadblock_1_1detail_1_1RowArrangement_3_01Shape_00_01WarpsRemaini91159e6f7e123d881e3ec45101fa4f81.html#adcac777a84015bf6ba63cbd86907ac74',1,'cutlass::epilogue::threadblock::detail::RowArrangement&lt; Shape, WarpsRemaining, ElementsPerAccess, ElementSize, false &gt;::kAccessRows()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1detail_1_1RowArrangement_3_01Shape_00_01WarpsRemaini6d8790249bf12cac580da73bb37eb791.html#a6fc76c727fbc9d40139843173a971fd2',1,'cutlass::epilogue::threadblock::detail::RowArrangement&lt; Shape, WarpsRemaining, ElementsPerAccess, ElementSize, true &gt;::kAccessRows()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1OutputTileOptimalThreadMap_1_1Detail.html#a050ba21042390f3757539ac6a1b958ea',1,'cutlass::epilogue::threadblock::OutputTileOptimalThreadMap::Detail::kAccessRows()']]],
  ['kaccesssize',['kAccessSize',['../structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a1e3a11d053aed1428ffaf4893cdc4715',1,'cutlass::layout::VoltaTensorOpMultiplicandCongruous::kAccessSize()'],['../structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#a478922587dc4bcf510121bc2309180c7',1,'cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandCongruous::kAccessSize()'],['../structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#a469537089ba16873fde88b1d31255050',1,'cutlass::layout::RowMajorVoltaTensorOpMultiplicandCongruous::kAccessSize()'],['../structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#aa3fb793c8975ae81e031bac761a23857',1,'cutlass::layout::VoltaTensorOpMultiplicandBCongruous::kAccessSize()'],['../structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#ab1a5c2c723f8c39bf21bf54b1e6f1127',1,'cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandBCongruous::kAccessSize()'],['../structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#ad2355d0822557706181cdc0f69d120d5',1,'cutlass::layout::RowMajorVoltaTensorOpMultiplicandBCongruous::kAccessSize()'],['../structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#a5e7ab2fc429ba408a2fd537489bc87ae',1,'cutlass::layout::VoltaTensorOpMultiplicandCrosswise::kAccessSize()'],['../structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#a9fbbafdefa92eaecbe9b9d89d118a6f7',1,'cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandCrosswise::kAccessSize()'],['../structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#a88192ee545e21fa3096e082599e69459',1,'cutlass::layout::RowMajorVoltaTensorOpMultiplicandCrosswise::kAccessSize()'],['../structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a627189c6253996de45f23c2dc04df72e',1,'cutlass::layout::TensorOpMultiplicand::kAccessSize()'],['../structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a580ac36bfc4fbd587fcbe3f36e25fb43',1,'cutlass::layout::TensorOpMultiplicandCongruous::kAccessSize()'],['../structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#a2b38596d9f461583bbb6c8129acc24e3',1,'cutlass::layout::TensorOpMultiplicandCongruous&lt; 32, Crosswise &gt;::kAccessSize()'],['../structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#a4824540ce26044047aabec0be63ed0dc',1,'cutlass::layout::ColumnMajorTensorOpMultiplicandCongruous::kAccessSize()'],['../structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#aed5835ab4bafa391321f721b2a39f285',1,'cutlass::layout::RowMajorTensorOpMultiplicandCongruous::kAccessSize()'],['../structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a44e047e914c52edcfeb07bc3747c0b18',1,'cutlass::layout::TensorOpMultiplicandCrosswise::kAccessSize()'],['../structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#a31869d096511d5559f3ae1ff5671207f',1,'cutlass::layout::ColumnMajorTensorOpMultiplicandCrosswise::kAccessSize()'],['../structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#a9a93000ebe759669c1e54564b1266961',1,'cutlass::layout::RowMajorTensorOpMultiplicandCrosswise::kAccessSize()'],['../structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#af6c844d6ab958e5e32dfdfb452de1578',1,'cutlass::layout::TensorOpMultiplicandColumnMajorInterleaved::kAccessSize()'],['../structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#aaa4ff06d1ef03d1a0b9e07d9af1c9323',1,'cutlass::layout::TensorOpMultiplicandRowMajorInterleaved::kAccessSize()']]],
  ['kaccesssizeinbits',['kAccessSizeInBits',['../classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#aad7937d2ef96522d0afa41a587c8f790',1,'cutlass::epilogue::EpilogueWorkspace::kAccessSizeInBits()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmSha69bef08ea63dd930f99d9788105873dd.html#a32c6dec4033b897cf0dcd7be05c50e97',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 8, 8, 4 &gt;, ElementA_, layout::ColumnMajor, ElementB_, layout::RowMajor, ElementC_, LayoutC_, arch::OpClassTensorOp, 2, Operator_ &gt;::kAccessSizeInBits()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmSha3adf608332a8c9ee7014fced0da8a9ca.html#a21900d9dee0f0783e183a684be1268ae',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 8, 8, 4 &gt;, ElementA_, layout::RowMajor, ElementB_, layout::ColumnMajor, ElementC_, LayoutC_, arch::OpClassTensorOp, 2, Operator_ &gt;::kAccessSizeInBits()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmShab7edfba3cdf43a07e3c4d719d87565a4.html#ac57c0e8298405753c64fc4a491c1ba0b',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 8, 8, 4 &gt;, ElementA_, layout::RowMajor, ElementB_, layout::RowMajor, ElementC_, LayoutC_, arch::OpClassTensorOp, 2, Operator_ &gt;::kAccessSizeInBits()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmShaf03a122202ad10acdc96f280106d678b.html#a33290f43f6be8451ceca3c4edddb088f',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 8, 8, 4 &gt;, ElementA_, layout::ColumnMajor, ElementB_, layout::ColumnMajor, ElementC_, LayoutC_, arch::OpClassTensorOp, 2, Operator_ &gt;::kAccessSizeInBits()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01Instrucf60fe02fcdd80d28b7fd419133465dcc.html#a62bdfe989e5800b42ad510b621de5b48',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, InstructionShape_, ElementA_, layout::ColumnMajor, ElementB_, layout::RowMajor, ElementC_, LayoutC_, arch::OpClassTensorOp, 2, Operator_ &gt;::kAccessSizeInBits()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01Instruc24092ddc01fc83dabb7db4c14880fe60.html#a071fa7065c300f14f039a34f898fdb49',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, InstructionShape_, ElementA_, layout::RowMajor, ElementB_, layout::ColumnMajor, ElementC_, LayoutC_, arch::OpClassTensorOp, 2, Operator_ &gt;::kAccessSizeInBits()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01Instruc4fee9f2965b8468bfb42b94a74527d22.html#a7ae5dc2e9a6628be77a75cddc9d12011',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, InstructionShape_, ElementA_, layout::RowMajor, ElementB_, layout::RowMajor, ElementC_, LayoutC_, arch::OpClassTensorOp, 2, Operator_ &gt;::kAccessSizeInBits()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01Instruc803d38bc1e4618c07c47f54c87ae2678.html#a8baea997dbbf27fdb0df6b5853c243ec',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, InstructionShape_, ElementA_, layout::ColumnMajor, ElementB_, layout::ColumnMajor, ElementC_, LayoutC_, arch::OpClassTensorOp, 2, Operator_ &gt;::kAccessSizeInBits()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01Instruc2bf00737f4ad0a9da9a8be6d3e66c152.html#a6f3483d9a5feb71664ba0c0ddf3c4d7e',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, InstructionShape_, ElementA_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt;, ElementB_, layout::RowMajorInterleaved&lt; InterleavedK &gt;, ElementC_, LayoutC_, arch::OpClassTensorOp, 2, Operator_, AccumulatorsInRowMajor &gt;::kAccessSizeInBits()'],['../structcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element_0a9491607d11be8e1780e79ad711aa42.html#a4491955b1e900aa9cf45e610debc3ee8',1,'cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::TensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element_))&gt;, AdvanceRank, ThreadMap_, Alignment &gt;::Detail::kAccessSizeInBits()'],['../structcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element_3be8b96d170d886f39b6b30acab65e7a.html#a1ef8ca3c06d9ce974836317f67b5d752',1,'cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::TensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::Detail::kAccessSizeInBits()'],['../structcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_052caec9d5bceeb59b9a13cb3338ce64d.html#ab5ed9dfd3b28a68e629686369e7d2db2',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::TensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element_))&gt;, AdvanceRank, ThreadMap_, Alignment &gt;::Detail::kAccessSizeInBits()'],['../structcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_039093927f4b1ee61538c569bf1ae4efd.html#af010e2034edfaca50aaf1ada0fb0222d',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::TensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::Detail::kAccessSizeInBits()'],['../structcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_032f88d1be8b209e44a4815c707ba35bb.html#a646a2654342dc0134659cc3eece7f027',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::VoltaTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::Detail::kAccessSizeInBits()'],['../structcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_02d305cfb0b55c6fb236a52cf2240651e.html#ac40dd2b177f1129a02505a941ddccb08',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::VoltaTensorOpMultiplicandBCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::Detail::kAccessSizeInBits()']]],
  ['kaccesswidth',['kAccessWidth',['../structcutlass_1_1epilogue_1_1threadblock_1_1detail_1_1RowArrangement_3_01Shape_00_01WarpsRemaini91159e6f7e123d881e3ec45101fa4f81.html#a015f6bd69b9a05b2fbac3544789d7936',1,'cutlass::epilogue::threadblock::detail::RowArrangement&lt; Shape, WarpsRemaining, ElementsPerAccess, ElementSize, false &gt;::kAccessWidth()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1detail_1_1RowArrangement_3_01Shape_00_01WarpsRemaini6d8790249bf12cac580da73bb37eb791.html#ae32ce0b27c5ec552ab1154ecf5f0da1a',1,'cutlass::epilogue::threadblock::detail::RowArrangement&lt; Shape, WarpsRemaining, ElementsPerAccess, ElementSize, true &gt;::kAccessWidth()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1OutputTileOptimalThreadMap_1_1Detail.html#a42047dd6960d5c09fd5145de6cabc18a',1,'cutlass::epilogue::threadblock::OutputTileOptimalThreadMap::Detail::kAccessWidth()']]],
  ['kaccumulatorcolumnstride',['kAccumulatorColumnStride',['../structcutlass_1_1epilogue_1_1warp_1_1TensorOpPolicy_3_01WarpShape_00_01OperatorShape_00_01layout_1_1RowMajor_01_4.html#a20499f0e2316d3699c077d28f7808f9a',1,'cutlass::epilogue::warp::TensorOpPolicy&lt; WarpShape, OperatorShape, layout::RowMajor &gt;']]],
  ['kaccumulatorelementcount',['kAccumulatorElementCount',['../structcutlass_1_1epilogue_1_1warp_1_1SimtPolicy_3_01WarpShape___00_01Operator___00_01layout_1_1Rcef1c60e23e997017ae176c92931151d.html#a5dd1a5071c51e16afb984b14d96e9a0e',1,'cutlass::epilogue::warp::SimtPolicy&lt; WarpShape_, Operator_, layout::RowMajor, MmaSimtPolicy_ &gt;']]],
  ['kaccumulatorrowstride',['kAccumulatorRowStride',['../structcutlass_1_1epilogue_1_1warp_1_1TensorOpPolicy_3_01WarpShape_00_01OperatorShape_00_01layout_1_1RowMajor_01_4.html#a27021254f64e4b95d6af6465e0622b06',1,'cutlass::epilogue::warp::TensorOpPolicy&lt; WarpShape, OperatorShape, layout::RowMajor &gt;']]],
  ['kactiverank',['kActiveRank',['../structcutlass_1_1reference_1_1host_1_1detail_1_1TensorForEachHelper.html#a1161a761c596e714982fe30141211cca',1,'cutlass::reference::host::detail::TensorForEachHelper::kActiveRank()'],['../structcutlass_1_1reference_1_1host_1_1detail_1_1TensorForEachHelper_3_01Func_00_01Rank_00_010_01_4.html#ac558396615146aba17f12df92757a8ea',1,'cutlass::reference::host::detail::TensorForEachHelper&lt; Func, Rank, 0 &gt;::kActiveRank()']]],
  ['kadvancerank',['kAdvanceRank',['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator_3_01Shape___00_01Elemen784a0e9da3f55064c47e5613791f51f7.html#a4c178ffb6608541fce36413ff37c29e9',1,'cutlass::transform::threadblock::PredicatedTileAccessIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessType_ &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator_3_01Shape___00_01Elemen89c687c583745a73cb485041911a4c4e.html#a3990cb0df7585e28b45623aca89f3747',1,'cutlass::transform::threadblock::PredicatedTileAccessIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, AccessType_ &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator_3_01Shape___00_01Elemen9838736ad62fae54213fbaf722a989ab.html#a9ad0e204d1530aead9edba09f9af9b22',1,'cutlass::transform::threadblock::PredicatedTileAccessIterator&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, AccessType_ &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator_3_01Shape___00_01Elemenab63a1e105bf37f6371516cb9e2c5a7a.html#a3dde017efaaac4825fd2939d8017d0d2',1,'cutlass::transform::threadblock::PredicatedTileAccessIterator&lt; Shape_, Element_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessType_ &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator_3_01Shape___00_01Elemen809793e785fb4211888c6b4e5dcfcb39.html#a62f29856535ce4c2d78e6db48b7b10ab',1,'cutlass::transform::threadblock::PredicatedTileAccessIterator&lt; Shape_, Element_, layout::RowMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessType_ &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator2dThreadTile_3_01Shape__1790abaa54a01f277d75766d5882fec8.html#ac82540b3d5d33716df5e64c4a92fc5be',1,'cutlass::transform::threadblock::PredicatedTileAccessIterator2dThreadTile&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessType_ &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator2dThreadTile_3_01Shape__da632779aba661c0f4cfaaa78126b771.html#a7b133220bc1d0139e3c43e56d12c175e',1,'cutlass::transform::threadblock::PredicatedTileAccessIterator2dThreadTile&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, AccessType_ &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator2dThreadTile_3_01Shape__7327fa15996bcb8502cdfcc192350fe1.html#a7c4e6d66d164e7d8c3bd6735b3b0e338',1,'cutlass::transform::threadblock::PredicatedTileAccessIterator2dThreadTile&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, AccessType_ &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a5d4c4f757ba6db23fbe7227d1c41869a',1,'cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessSize &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a77b2969e0af0ec77437681a1cd1bfc82',1,'cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#ab0c8347bf8f121315d30f4429f3855b9',1,'cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a3dce16276f08ab3143ded18faeaed489',1,'cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#afbc38be9ceacc01a877073ec35219542',1,'cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator2dThreadTile_3_01Shape___00_017a517f3c73efd795ab05059cc9b111e1.html#a8ea7d8c5b2d1cb79ecec2a5b114c42c7',1,'cutlass::transform::threadblock::PredicatedTileIterator2dThreadTile&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, Transpose_ &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator2dThreadTile_3_01Shape___00_0165b39a630d10785a3558406f9adb99b9.html#a472dc9ee3f75f91699c794f1eb24dc0b',1,'cutlass::transform::threadblock::PredicatedTileIterator2dThreadTile&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, Transpose_ &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator2dThreadTile_3_01Shape___00_013671177d6219bfeb0e1b4dc4c1b5bf11.html#ab9b93715c82a268fd53eb1d55c281bab',1,'cutlass::transform::threadblock::PredicatedTileIterator2dThreadTile&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, Transpose_ &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__0855e9d9ab619202d2397180c1e4c4a5.html#ad55bd2bbfdbe0984b16859d636b409c6',1,'cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, Alignment &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__eb7d20f8b9d69e0ae5e7ef51dc480867.html#a43dc3a626dd5d4bb6d258c4d17550f45',1,'cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, Alignment &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__f04332958a49a47d6fb2b25201764630.html#a07e24a73152f9feb38cd07e90a1e9a3f',1,'cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, Alignment &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__ebf4714349612673e8b6609b763eeb6f.html#a9c2871152eb66979098cff0676b00bbd',1,'cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::TensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element_))&gt;, AdvanceRank, ThreadMap_, Alignment &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__2c1476eaf582bfe972793e17babfe985.html#ab346174acb993db138cfaed18c61d979',1,'cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::ColumnMajorTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element_))&gt;, AdvanceRank, ThreadMap_, Alignment &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__6baada077236f1a368c61c5e11b45b72.html#af076255d0a7dde0025c10f69af9b25b9',1,'cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::RowMajorTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element_))&gt;, AdvanceRank, ThreadMap_, Alignment &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__e9a9e0f4286f652f55eb9b863b21effe.html#a55f42293c103fdc0771abb0ba0b6fc6e',1,'cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::TensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__a3c11cf1f00ef7a1efb8389ac6e4c6e0.html#a67c0493b9d10db5185437f942bc4374a',1,'cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::ColumnMajorTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__0184b7188941788a96624510a4b2f876.html#abdb2a9eac279d6042387de59a2dd92d0',1,'cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::RowMajorTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_0184a89653916f5d51ab59d1b386989a17.html#a5cfe40a22b6405c12cf6f17206ea87da',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, Alignment &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_0149454d361ea5885cf5166a920b5145df.html#a1f7a2c177973a9c407df31ef4e7b900d',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, Alignment &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_011d3637dbd8bc58bcb020b51bf57fbfc0.html#a780965d9703eeaeb65aa2b2b47bdd93a',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, Alignment &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator2dThreadTile_3_01Shape___00_01Ele76ed82829532ae1c17f4c78158f036c7.html#a2e97562e0a8664a6d74e4a222e12dd9a',1,'cutlass::transform::threadblock::RegularTileIterator2dThreadTile&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, Alignment &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator2dThreadTile_3_01Shape___00_01Ele654c8f6161ae5340f040397a4e2e045c.html#a5e69c931ac6d6206dcfd1135b8ee4777',1,'cutlass::transform::threadblock::RegularTileIterator2dThreadTile&lt; Shape_, Element_, layout::RowMajorInterleaved&lt; 4 &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator2dThreadTile_3_01Shape___00_01Eleb60d066756d1c18f05fceee6a27bdb8a.html#af9b3d6132a5cfeea39194feeab5e9047',1,'cutlass::transform::threadblock::RegularTileIterator2dThreadTile&lt; Shape_, Element_, layout::ColumnMajorInterleaved&lt; 4 &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01efd5013a2503d6567e2bf6b40c97360c.html#a983df29486b4b738b26109a6f0fb1def',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::TensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element_))&gt;, AdvanceRank, ThreadMap_, Alignment &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_017982f81d4ef592e19c8427de2ea933a3.html#a4524d56b78c64e97a881a8911179f011',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::ColumnMajorTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element_))&gt;, AdvanceRank, ThreadMap_, Alignment &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01c20d35180520077a5a09b1e33543c1a5.html#af6ef404f2e7e8cfccba354d28cb36c97',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::RowMajorTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element_))&gt;, AdvanceRank, ThreadMap_, Alignment &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_0197fef2242a3454a7d1cebe61aee28b43.html#afb070e84ad84b06918cc1a2335b99711',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::TensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_010889a732373c350de9b9a9f6c13cd761.html#a73d92073145270d9b6924abef10911e6',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::ColumnMajorTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01a31b454d9c930525c1e9ca406a514f40.html#a902fc59ce3ceb86d99adc4924facaf5e',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::RowMajorTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01f96bbeb63e6d4ce4a2551279de3a9f0e.html#ad086804d9a3a327759c8badfb2938ed5',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::VoltaTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01793f74bfd8f116a827948ab01a37349a.html#a6920500c99e0ad2170ef207460656062',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::ColumnMajorVoltaTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01f6f6511b5033cad31083644ac69c54d8.html#af6fbc41258d57e4d7a4aac46edf811a3',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::RowMajorVoltaTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01a75d2cd74e722d6ad6a3b41aabfd432d.html#ae5fae99cc67b23b02aa463a033dedb8c',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::VoltaTensorOpMultiplicandBCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01187f8574e1fe9d7d5e8fbf09bd834bf0.html#a41b84283d541bef55f463856e6e52e7b',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::ColumnMajorVoltaTensorOpMultiplicandBCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_0104ad31bd559a88cc418ae1cab7492ed5.html#a7ccd102ac41d37c510a7818b3bbc5a40',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::RowMajorVoltaTensorOpMultiplicandBCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01dbd6b8468d5bd787308d2f615a24d123.html#a963a0e4c7935555b8016754c43c63591',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::VoltaTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Shape_::kContiguous &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01bd31b3810c1fedf2e7e5959ff92b5d3d.html#a1ddb408e14b2e354d26b900472bded68',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::ColumnMajorVoltaTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Shape_::kRow &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::kAdvanceRank()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01b3fa5720e807697de61b9f937b269cd0.html#a04d8fd36d9d850ff6c4d95cca46b110a',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::RowMajorVoltaTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Shape_::kColumn &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::kAdvanceRank()']]],
  ['kalign',['kAlign',['../structcutlass_1_1AlignedBuffer.html#a0bab3f7468fe898b8abddba83f0b581a',1,'cutlass::AlignedBuffer']]],
  ['kalignment',['kAlignment',['../classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#ae46b49371732c012f0b44399f39d8b0a',1,'cutlass::epilogue::threadblock::SharedLoadIterator::kAlignment()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__0855e9d9ab619202d2397180c1e4c4a5.html#a462a01d67fc7a91ade09cfa8933b1465',1,'cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, Alignment &gt;::kAlignment()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__eb7d20f8b9d69e0ae5e7ef51dc480867.html#a03463d3eeb2d83d476e8474be0b96367',1,'cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, Alignment &gt;::kAlignment()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__f04332958a49a47d6fb2b25201764630.html#a84fcf9f2ac22bcf97b4217fcaf359fcf',1,'cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, Alignment &gt;::kAlignment()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__ebf4714349612673e8b6609b763eeb6f.html#a87bfae407dbf0fc94e4a846ccebf7073',1,'cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::TensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element_))&gt;, AdvanceRank, ThreadMap_, Alignment &gt;::kAlignment()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__2c1476eaf582bfe972793e17babfe985.html#afcbfb246bfbfda546da90c5f908c2664',1,'cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::ColumnMajorTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element_))&gt;, AdvanceRank, ThreadMap_, Alignment &gt;::kAlignment()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__6baada077236f1a368c61c5e11b45b72.html#a13df81b103c33b25af2c1f6527ca8584',1,'cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::RowMajorTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element_))&gt;, AdvanceRank, ThreadMap_, Alignment &gt;::kAlignment()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__e9a9e0f4286f652f55eb9b863b21effe.html#a25cf1f0784f01682051fa28d15e28b4d',1,'cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::TensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::kAlignment()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__a3c11cf1f00ef7a1efb8389ac6e4c6e0.html#ad1194d03b3694a66b06fcd2c2b526921',1,'cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::ColumnMajorTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::kAlignment()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__0184b7188941788a96624510a4b2f876.html#ac83cf1ee4e2149c2553098b571f3a17c',1,'cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::RowMajorTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::kAlignment()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_0184a89653916f5d51ab59d1b386989a17.html#a9995cd0108feb5cc5d4858181fd761b3',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, Alignment &gt;::kAlignment()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_0149454d361ea5885cf5166a920b5145df.html#a79f4dbf9da4bf9ba8b3b1559bea9835f',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, Alignment &gt;::kAlignment()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_011d3637dbd8bc58bcb020b51bf57fbfc0.html#acf5b3c865149cac8d1ec6ff4e96e275c',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, Alignment &gt;::kAlignment()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator2dThreadTile_3_01Shape___00_01Ele76ed82829532ae1c17f4c78158f036c7.html#a166ac1e52288f5a0f9c25455ffa2cd33',1,'cutlass::transform::threadblock::RegularTileIterator2dThreadTile&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, Alignment &gt;::kAlignment()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator2dThreadTile_3_01Shape___00_01Ele654c8f6161ae5340f040397a4e2e045c.html#adbe706ff6763e8bb4be094a994cd71f1',1,'cutlass::transform::threadblock::RegularTileIterator2dThreadTile&lt; Shape_, Element_, layout::RowMajorInterleaved&lt; 4 &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::kAlignment()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator2dThreadTile_3_01Shape___00_01Eleb60d066756d1c18f05fceee6a27bdb8a.html#a8cb1ff6063babf139f7dfda286049531',1,'cutlass::transform::threadblock::RegularTileIterator2dThreadTile&lt; Shape_, Element_, layout::ColumnMajorInterleaved&lt; 4 &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::kAlignment()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01efd5013a2503d6567e2bf6b40c97360c.html#a08056bca3bc22da63b5406e780edd35f',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::TensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element_))&gt;, AdvanceRank, ThreadMap_, Alignment &gt;::kAlignment()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_017982f81d4ef592e19c8427de2ea933a3.html#a7e43846b92ea979f29ccc29806182c28',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::ColumnMajorTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element_))&gt;, AdvanceRank, ThreadMap_, Alignment &gt;::kAlignment()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01c20d35180520077a5a09b1e33543c1a5.html#a6ba4450e53e2a9b3499c068045863f5c',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::RowMajorTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element_))&gt;, AdvanceRank, ThreadMap_, Alignment &gt;::kAlignment()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_0197fef2242a3454a7d1cebe61aee28b43.html#aab74a608e4457924bf58da8a6a24c673',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::TensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::kAlignment()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_010889a732373c350de9b9a9f6c13cd761.html#ab476f8fecc7e906c89e2639baf23b1c8',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::ColumnMajorTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::kAlignment()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01a31b454d9c930525c1e9ca406a514f40.html#a8b8b1dd68dca629c60974bbb0bfeba99',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::RowMajorTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::kAlignment()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01b3fa5720e807697de61b9f937b269cd0.html#a8adbbe1353bdc33ed9274988881c0abd',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::RowMajorVoltaTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Shape_::kColumn &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::kAlignment()']]],
  ['kalignmenta',['kAlignmentA',['../structcutlass_1_1gemm_1_1device_1_1DefaultGemmConfiguration_3_01arch_1_1OpClassSimt_00_01ArchTag286687c5e6abe22d241f789fe344a465.html#a465fd23467dcb62bd6a92f03dffc628d',1,'cutlass::gemm::device::DefaultGemmConfiguration&lt; arch::OpClassSimt, ArchTag, ElementA, ElementB, ElementC, ElementAccumulator &gt;::kAlignmentA()'],['../structcutlass_1_1gemm_1_1device_1_1DefaultGemmConfiguration_3_01arch_1_1OpClassSimt_00_01ArchTag3026e48abb8c905d1cc6d13d669700e4.html#a7da3037680d736b1043c9d5d1579d02f',1,'cutlass::gemm::device::DefaultGemmConfiguration&lt; arch::OpClassSimt, ArchTag, int8_t, int8_t, ElementC, int32_t &gt;::kAlignmentA()'],['../structcutlass_1_1gemm_1_1device_1_1DefaultGemmConfiguration_3_01arch_1_1OpClassWmmaTensorOp_00_0884059ecad03bea3e86c4cf722226097.html#a3583ebf26b8d3548d8c4d235db9f7f31',1,'cutlass::gemm::device::DefaultGemmConfiguration&lt; arch::OpClassWmmaTensorOp, ArchTag, ElementA, ElementB, ElementC, ElementAccumulator &gt;::kAlignmentA()'],['../structcutlass_1_1gemm_1_1device_1_1DefaultGemmConfiguration_3_01arch_1_1OpClassTensorOp_00_01arc567cad318a31d04b70ea615d6321decd.html#ac593595deb8c18006c9b34e513225cbf',1,'cutlass::gemm::device::DefaultGemmConfiguration&lt; arch::OpClassTensorOp, arch::Sm70, ElementA, ElementB, ElementC, ElementAccumulator &gt;::kAlignmentA()'],['../structcutlass_1_1gemm_1_1device_1_1DefaultGemmConfiguration_3_01arch_1_1OpClassTensorOp_00_01arcde61af9be1337dac1fdb210e7e7a6e01.html#a7e108d841f08231e008f15fb5085955e',1,'cutlass::gemm::device::DefaultGemmConfiguration&lt; arch::OpClassTensorOp, arch::Sm75, ElementA, ElementB, ElementC, ElementAccumulator &gt;::kAlignmentA()'],['../structcutlass_1_1gemm_1_1device_1_1DefaultGemmConfiguration_3_01arch_1_1OpClassTensorOp_00_01arc4fada4957d463c80a2831e47f28157c4.html#a1a1072d8312664fca4ab9a38731a558c',1,'cutlass::gemm::device::DefaultGemmConfiguration&lt; arch::OpClassTensorOp, arch::Sm75, int8_t, int8_t, ElementC, int32_t &gt;::kAlignmentA()'],['../structcutlass_1_1gemm_1_1device_1_1DefaultGemmConfiguration_3_01arch_1_1OpClassTensorOp_00_01arc8ab5fd2693c6a6ec43e447acb07f784c.html#a2bece68a1b3557a62d92b0b4b110dda0',1,'cutlass::gemm::device::DefaultGemmConfiguration&lt; arch::OpClassTensorOp, arch::Sm75, int8_t, uint8_t, ElementC, int32_t &gt;::kAlignmentA()'],['../structcutlass_1_1gemm_1_1device_1_1DefaultGemmConfiguration_3_01arch_1_1OpClassTensorOp_00_01arcb27bf218007928652d5b803193eab473.html#ad82bb5bfc0fa1bb10cb50fd239e7c57a',1,'cutlass::gemm::device::DefaultGemmConfiguration&lt; arch::OpClassTensorOp, arch::Sm75, uint8_t, int8_t, ElementC, int32_t &gt;::kAlignmentA()'],['../structcutlass_1_1gemm_1_1device_1_1DefaultGemmConfiguration_3_01arch_1_1OpClassTensorOp_00_01arcfea0f3503156e8e3fba6456f0cedafdd.html#a49c72e0f5375d327a82468d7ae87261d',1,'cutlass::gemm::device::DefaultGemmConfiguration&lt; arch::OpClassTensorOp, arch::Sm75, uint8_t, uint8_t, ElementC, int32_t &gt;::kAlignmentA()'],['../structcutlass_1_1gemm_1_1device_1_1DefaultGemmConfiguration_3_01arch_1_1OpClassTensorOp_00_01arc485a4f0b5a7d2d4ab2c1a24da6328048.html#a4df286a461db79479b9870cbfd0a672e',1,'cutlass::gemm::device::DefaultGemmConfiguration&lt; arch::OpClassTensorOp, arch::Sm75, int4b_t, int4b_t, ElementC, int32_t &gt;::kAlignmentA()'],['../structcutlass_1_1gemm_1_1device_1_1DefaultGemmConfiguration_3_01arch_1_1OpClassTensorOp_00_01arc8e2604a56dff3a7595da9ee0604ae55e.html#ad3c7885d5d5d68f86791ae020c2eeb34',1,'cutlass::gemm::device::DefaultGemmConfiguration&lt; arch::OpClassTensorOp, arch::Sm75, int4b_t, uint4b_t, ElementC, int32_t &gt;::kAlignmentA()'],['../structcutlass_1_1gemm_1_1device_1_1DefaultGemmConfiguration_3_01arch_1_1OpClassTensorOp_00_01arcffcf31256aed23d4d8d0eab627bc0cad.html#aa3d217f6d271b59cc9028a566798789b',1,'cutlass::gemm::device::DefaultGemmConfiguration&lt; arch::OpClassTensorOp, arch::Sm75, uint4b_t, int4b_t, ElementC, int32_t &gt;::kAlignmentA()'],['../structcutlass_1_1gemm_1_1device_1_1DefaultGemmConfiguration_3_01arch_1_1OpClassTensorOp_00_01arcb2e258b7bd321c633dd65d3ebcf6414a.html#a60c5fa49aa627f8ced16e6b121015eb9',1,'cutlass::gemm::device::DefaultGemmConfiguration&lt; arch::OpClassTensorOp, arch::Sm75, uint4b_t, uint4b_t, ElementC, int32_t &gt;::kAlignmentA()'],['../classcutlass_1_1gemm_1_1device_1_1Gemm.html#ad0f1a2669ecc62f24922613e9bad857f',1,'cutlass::gemm::device::Gemm::kAlignmentA()'],['../classcutlass_1_1gemm_1_1device_1_1Gemm_3_01ElementA___00_01LayoutA___00_01ElementB___00_01Layout4d0960ae6b1d1bf19e6239dbd002249c.html#a78660ed036162b8455546ff5718968d0',1,'cutlass::gemm::device::Gemm&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, AlignmentA, AlignmentB, SplitKSerial, Operator_, IsBetaZero &gt;::kAlignmentA()'],['../classcutlass_1_1gemm_1_1device_1_1GemmBatched.html#a8976ed5c5e404ee87deaea4455d0d960',1,'cutlass::gemm::device::GemmBatched::kAlignmentA()'],['../classcutlass_1_1gemm_1_1device_1_1GemmBatched_3_01ElementA___00_01LayoutA___00_01ElementB___00_0c9bb6f4463ab6085e6008b5d5ad6abfd.html#a4b924723475dcef72e0130ce1bb43956',1,'cutlass::gemm::device::GemmBatched&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, AlignmentA, AlignmentB, Operator_ &gt;::kAlignmentA()']]],
  ['kalignmentb',['kAlignmentB',['../structcutlass_1_1gemm_1_1device_1_1DefaultGemmConfiguration_3_01arch_1_1OpClassSimt_00_01ArchTag286687c5e6abe22d241f789fe344a465.html#a95b32b6ea4a7deb93090ba1a4c38c97b',1,'cutlass::gemm::device::DefaultGemmConfiguration&lt; arch::OpClassSimt, ArchTag, ElementA, ElementB, ElementC, ElementAccumulator &gt;::kAlignmentB()'],['../structcutlass_1_1gemm_1_1device_1_1DefaultGemmConfiguration_3_01arch_1_1OpClassSimt_00_01ArchTag3026e48abb8c905d1cc6d13d669700e4.html#aeaa91a16b3ec53bb2e340e8f414b7a9d',1,'cutlass::gemm::device::DefaultGemmConfiguration&lt; arch::OpClassSimt, ArchTag, int8_t, int8_t, ElementC, int32_t &gt;::kAlignmentB()'],['../structcutlass_1_1gemm_1_1device_1_1DefaultGemmConfiguration_3_01arch_1_1OpClassWmmaTensorOp_00_0884059ecad03bea3e86c4cf722226097.html#a42606234be9d3de071846e8fa5077dfa',1,'cutlass::gemm::device::DefaultGemmConfiguration&lt; arch::OpClassWmmaTensorOp, ArchTag, ElementA, ElementB, ElementC, ElementAccumulator &gt;::kAlignmentB()'],['../structcutlass_1_1gemm_1_1device_1_1DefaultGemmConfiguration_3_01arch_1_1OpClassTensorOp_00_01arc567cad318a31d04b70ea615d6321decd.html#a94cf589ae3c1b5c6d2fe72ce91a0d306',1,'cutlass::gemm::device::DefaultGemmConfiguration&lt; arch::OpClassTensorOp, arch::Sm70, ElementA, ElementB, ElementC, ElementAccumulator &gt;::kAlignmentB()'],['../structcutlass_1_1gemm_1_1device_1_1DefaultGemmConfiguration_3_01arch_1_1OpClassTensorOp_00_01arcde61af9be1337dac1fdb210e7e7a6e01.html#a9fb17df1fb763bcdb829c5089e8d7fa7',1,'cutlass::gemm::device::DefaultGemmConfiguration&lt; arch::OpClassTensorOp, arch::Sm75, ElementA, ElementB, ElementC, ElementAccumulator &gt;::kAlignmentB()'],['../structcutlass_1_1gemm_1_1device_1_1DefaultGemmConfiguration_3_01arch_1_1OpClassTensorOp_00_01arc4fada4957d463c80a2831e47f28157c4.html#a7070824db4088c6f0601e715b735bc83',1,'cutlass::gemm::device::DefaultGemmConfiguration&lt; arch::OpClassTensorOp, arch::Sm75, int8_t, int8_t, ElementC, int32_t &gt;::kAlignmentB()'],['../structcutlass_1_1gemm_1_1device_1_1DefaultGemmConfiguration_3_01arch_1_1OpClassTensorOp_00_01arc8ab5fd2693c6a6ec43e447acb07f784c.html#ab4b9afca5439329325359bc515db9091',1,'cutlass::gemm::device::DefaultGemmConfiguration&lt; arch::OpClassTensorOp, arch::Sm75, int8_t, uint8_t, ElementC, int32_t &gt;::kAlignmentB()'],['../structcutlass_1_1gemm_1_1device_1_1DefaultGemmConfiguration_3_01arch_1_1OpClassTensorOp_00_01arcb27bf218007928652d5b803193eab473.html#a42d01af76fa72099d1664ef0144b82a3',1,'cutlass::gemm::device::DefaultGemmConfiguration&lt; arch::OpClassTensorOp, arch::Sm75, uint8_t, int8_t, ElementC, int32_t &gt;::kAlignmentB()'],['../structcutlass_1_1gemm_1_1device_1_1DefaultGemmConfiguration_3_01arch_1_1OpClassTensorOp_00_01arcfea0f3503156e8e3fba6456f0cedafdd.html#aea3e387961d7f34320aadb919d640d15',1,'cutlass::gemm::device::DefaultGemmConfiguration&lt; arch::OpClassTensorOp, arch::Sm75, uint8_t, uint8_t, ElementC, int32_t &gt;::kAlignmentB()'],['../structcutlass_1_1gemm_1_1device_1_1DefaultGemmConfiguration_3_01arch_1_1OpClassTensorOp_00_01arc485a4f0b5a7d2d4ab2c1a24da6328048.html#a5a2f7aa1f18265c7d3bc9ebdf1f4624a',1,'cutlass::gemm::device::DefaultGemmConfiguration&lt; arch::OpClassTensorOp, arch::Sm75, int4b_t, int4b_t, ElementC, int32_t &gt;::kAlignmentB()'],['../structcutlass_1_1gemm_1_1device_1_1DefaultGemmConfiguration_3_01arch_1_1OpClassTensorOp_00_01arc8e2604a56dff3a7595da9ee0604ae55e.html#a0142242cdec713b072b43a5b0e20a033',1,'cutlass::gemm::device::DefaultGemmConfiguration&lt; arch::OpClassTensorOp, arch::Sm75, int4b_t, uint4b_t, ElementC, int32_t &gt;::kAlignmentB()'],['../structcutlass_1_1gemm_1_1device_1_1DefaultGemmConfiguration_3_01arch_1_1OpClassTensorOp_00_01arcffcf31256aed23d4d8d0eab627bc0cad.html#a177974f04f4da4945cc06293aad28a64',1,'cutlass::gemm::device::DefaultGemmConfiguration&lt; arch::OpClassTensorOp, arch::Sm75, uint4b_t, int4b_t, ElementC, int32_t &gt;::kAlignmentB()'],['../structcutlass_1_1gemm_1_1device_1_1DefaultGemmConfiguration_3_01arch_1_1OpClassTensorOp_00_01arcb2e258b7bd321c633dd65d3ebcf6414a.html#a1549f26fa82c6bf473372fd37c3c9bf9',1,'cutlass::gemm::device::DefaultGemmConfiguration&lt; arch::OpClassTensorOp, arch::Sm75, uint4b_t, uint4b_t, ElementC, int32_t &gt;::kAlignmentB()'],['../classcutlass_1_1gemm_1_1device_1_1Gemm.html#a30809c4843226401b9d2da5ecb8d838b',1,'cutlass::gemm::device::Gemm::kAlignmentB()'],['../classcutlass_1_1gemm_1_1device_1_1Gemm_3_01ElementA___00_01LayoutA___00_01ElementB___00_01Layout4d0960ae6b1d1bf19e6239dbd002249c.html#aa39221ab9fa4248c613b7222f764072e',1,'cutlass::gemm::device::Gemm&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, AlignmentA, AlignmentB, SplitKSerial, Operator_, IsBetaZero &gt;::kAlignmentB()'],['../classcutlass_1_1gemm_1_1device_1_1GemmBatched.html#a17d34b2884711522fafcfd7c7500955c',1,'cutlass::gemm::device::GemmBatched::kAlignmentB()'],['../classcutlass_1_1gemm_1_1device_1_1GemmBatched_3_01ElementA___00_01LayoutA___00_01ElementB___00_0c9bb6f4463ab6085e6008b5d5ad6abfd.html#a8f5d41976058b08562aa1819687d79a2',1,'cutlass::gemm::device::GemmBatched&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, AlignmentA, AlignmentB, Operator_ &gt;::kAlignmentB()']]],
  ['kalignmentc',['kAlignmentC',['../classcutlass_1_1gemm_1_1device_1_1Gemm.html#ac85244d4a91ea8e8c1c3187fad1004db',1,'cutlass::gemm::device::Gemm::kAlignmentC()'],['../classcutlass_1_1gemm_1_1device_1_1Gemm_3_01ElementA___00_01LayoutA___00_01ElementB___00_01Layout4d0960ae6b1d1bf19e6239dbd002249c.html#ad74a049e26f4b9224362b4d1c93ca14b',1,'cutlass::gemm::device::Gemm&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, AlignmentA, AlignmentB, SplitKSerial, Operator_, IsBetaZero &gt;::kAlignmentC()'],['../classcutlass_1_1gemm_1_1device_1_1GemmBatched.html#ac1f190321a811fa91eec0096829b07ff',1,'cutlass::gemm::device::GemmBatched::kAlignmentC()'],['../classcutlass_1_1gemm_1_1device_1_1GemmBatched_3_01ElementA___00_01LayoutA___00_01ElementB___00_0c9bb6f4463ab6085e6008b5d5ad6abfd.html#a79d27ed8dc23cc975f287ec0f041ddf9',1,'cutlass::gemm::device::GemmBatched&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, AlignmentA, AlignmentB, Operator_ &gt;::kAlignmentC()']]],
  ['kalignmentk',['kAlignmentK',['../structcutlass_1_1gemm_1_1kernel_1_1GemmSplitKParallel.html#a9681ea194ff33c1b57ba3a79f98999fd',1,'cutlass::gemm::kernel::GemmSplitKParallel']]],
  ['karray',['kArray',['../namespacecutlass_1_1library.html#a8a2c782ab9bf9e19f99fdfcaf7f1c182aafc9b058c0fa7b9d2556bbcdcc49a272',1,'cutlass::library']]],
  ['kb',['kB',['../namespacecutlass_1_1gemm.html#a34338284023da7403c9ecbd3f406b2a6a3e56c011b37f0bc78fb9eb175c1181c6',1,'cutlass::gemm']]],
  ['kb1',['kB1',['../namespacecutlass_1_1library.html#a366ecc865ac5b24cfdfd392199ba8e9ea25069789ea555310ce562cd8d893adb3',1,'cutlass::library']]],
  ['kbatch',['kBatch',['../structcutlass_1_1gemm_1_1BatchedGemmCoord.html#afbaa31e84987b27ec3e4fe1b5701ebe2',1,'cutlass::gemm::BatchedGemmCoord']]],
  ['kbatched',['kBatched',['../namespacecutlass_1_1library.html#a8a2c782ab9bf9e19f99fdfcaf7f1c182abd41d9174652d6e7bbbcb457bb1b5582',1,'cutlass::library']]],
  ['kbits',['kBits',['../structcutlass_1_1integer__subbyte.html#aa5eecb9043e2e2183f88ccf0ddbb04ea',1,'cutlass::integer_subbyte']]],
  ['kblockcolumns',['kBlockColumns',['../structcutlass_1_1layout_1_1ColumnMajorBlockLinear.html#a67c82c42797bb2b0f03c2bc4ee6b5e04',1,'cutlass::layout::ColumnMajorBlockLinear::kBlockColumns()'],['../structcutlass_1_1layout_1_1RowMajorBlockLinear.html#a5a883183edfd68dd8dc3406c7b7164d5',1,'cutlass::layout::RowMajorBlockLinear::kBlockColumns()']]],
  ['kblockrows',['kBlockRows',['../structcutlass_1_1layout_1_1ColumnMajorBlockLinear.html#a91fc032744a467348f1b89f31df7bbd6',1,'cutlass::layout::ColumnMajorBlockLinear::kBlockRows()'],['../structcutlass_1_1layout_1_1RowMajorBlockLinear.html#a09702f09686e247483f1767e8aa9ca62',1,'cutlass::layout::RowMajorBlockLinear::kBlockRows()']]],
  ['kbytes',['kBytes',['../structcutlass_1_1AlignedBuffer.html#ae7742a9814b15dafe3e05f98771a32e3',1,'cutlass::AlignedBuffer::kBytes()'],['../structcutlass_1_1PredicateVector.html#ab870e074b33c598f69fe11e104615c5a',1,'cutlass::PredicateVector::kBytes()']]],
  ['kc',['kC',['../structcutlass_1_1Tensor4DCoord.html#a538809c6f5ee032adf4558cd004d988d',1,'cutlass::Tensor4DCoord::kC()'],['../namespacecutlass_1_1gemm.html#a34338284023da7403c9ecbd3f406b2a6af73b7a14626c25aa554e2437ea3fc1df',1,'cutlass::gemm::kC()']]],
  ['kcf16',['kCF16',['../namespacecutlass_1_1library.html#a366ecc865ac5b24cfdfd392199ba8e9ea733d7597a35d5a625f63abc4c22c1963',1,'cutlass::library']]],
  ['kcf32',['kCF32',['../namespacecutlass_1_1library.html#a366ecc865ac5b24cfdfd392199ba8e9ea049a4a1b2e2226de16f7afb8b462d6b1',1,'cutlass::library']]],
  ['kcf64',['kCF64',['../namespacecutlass_1_1library.html#a366ecc865ac5b24cfdfd392199ba8e9ea77c8ce656c23d1eef73e87aa1298395c',1,'cutlass::library']]],
  ['kcluster',['kCluster',['../structcutlass_1_1epilogue_1_1threadblock_1_1OutputTileShape.html#a52d517268cc8554f0d6e36098334f2bf',1,'cutlass::epilogue::threadblock::OutputTileShape']]],
  ['kcolumn',['kColumn',['../structcutlass_1_1epilogue_1_1threadblock_1_1OutputTileShape.html#a7b35ef292b8b23cb49f1df4ac6c40912',1,'cutlass::epilogue::threadblock::OutputTileShape::kColumn()'],['../structcutlass_1_1MatrixShape.html#a160927c2ddf7239f5be8c7cf6374a82e',1,'cutlass::MatrixShape::kColumn()']]],
  ['kcolumnmajor',['kColumnMajor',['../namespacecutlass_1_1layout.html#af6b33640063b02d26c261efd25053e6cabbcb6d9641fe76477dcbe3b209efa7ca',1,'cutlass::layout::kColumnMajor()'],['../namespacecutlass.html#af99b012f0e1795ca7dc167b7b109dd19abbcb6d9641fe76477dcbe3b209efa7ca',1,'cutlass::kColumnMajor()'],['../namespacecutlass_1_1library.html#aa863c416529c1fe76555be9760619a30abbcb6d9641fe76477dcbe3b209efa7ca',1,'cutlass::library::kColumnMajor()']]],
  ['kcolumnmajorinterleavedk16',['kColumnMajorInterleavedK16',['../namespacecutlass_1_1library.html#aa863c416529c1fe76555be9760619a30a6e2a33a60ac6f96ae7c9d21e018e1d0f',1,'cutlass::library']]],
  ['kcolumnmajorinterleavedk4',['kColumnMajorInterleavedK4',['../namespacecutlass_1_1library.html#aa863c416529c1fe76555be9760619a30aa79cf5644f084b0e4ee32b52f0cc3a47',1,'cutlass::library']]],
  ['kcolumns',['kColumns',['../classcutlass_1_1thread_1_1Matrix.html#ad13ab25b0e147387c7868fbde3dd0436',1,'cutlass::thread::Matrix']]],
  ['kcolumnsperquad',['kColumnsPerQuad',['../structcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gemmffcab2297c8de8d0013602a39c525b78.html#ab055e705c6aa732223e21b9498dfa3f8',1,'cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, half_t, layout::RowMajor &gt;::Detail::kColumnsPerQuad()'],['../structcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gemm770cbca45441d295d5d7433e8222a700.html#ae38ff98b5bc1c9ad217fa5d4a45f55d5',1,'cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, float, layout::RowMajor &gt;::Detail::kColumnsPerQuad()']]],
  ['kcompacteddeltacluster',['kCompactedDeltaCluster',['../structcutlass_1_1epilogue_1_1threadblock_1_1OutputTileOptimalThreadMap_1_1Detail.html#a1f768d9ce5f03e5c82b916de6df5c6d5',1,'cutlass::epilogue::threadblock::OutputTileOptimalThreadMap::Detail']]],
  ['kcompacteddeltagroup',['kCompactedDeltaGroup',['../structcutlass_1_1epilogue_1_1threadblock_1_1OutputTileOptimalThreadMap_1_1Detail.html#a1a695bd9a621339ab99be78ffbbafe18',1,'cutlass::epilogue::threadblock::OutputTileOptimalThreadMap::Detail']]],
  ['kconjugate',['kConjugate',['../namespacecutlass.html#a59f08b1b99c4d52257b962d35ba55cdea8be1522542c86e700b5e787b1aabc70b',1,'cutlass::kConjugate()'],['../namespacecutlass.html#ab7e605b25da48d89f98764c12d50b467a8be1522542c86e700b5e787b1aabc70b',1,'cutlass::kConjugate()'],['../namespacecutlass_1_1library.html#aa2b27589531eec608a86cf43a36c4175a8be1522542c86e700b5e787b1aabc70b',1,'cutlass::library::kConjugate()']]],
  ['kcontiguous',['kContiguous',['../structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7',1,'cutlass::layout::PitchLinearShape']]],
  ['kcontiguouselementsperline',['kContiguousElementsPerLine',['../structcutlass_1_1gemm_1_1warp_1_1MmaVoltaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operafa294175b280756dd8388f9ffe7b72c4.html#a4dfe26c4948b1c0a930127359f8fbf30',1,'cutlass::gemm::warp::MmaVoltaTensorOpMultiplicandTileIterator&lt; Shape_, Operand_, Element_, cutlass::layout::VoltaTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, KBlock &gt;, InstructionShape_, OpDelta_, 32 &gt;::Policy::kContiguousElementsPerLine()'],['../structcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_0390833403016f5d817416e20828845df.html#a72e2ec7effe4120a9faf2a19e2be8891',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::VoltaTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Shape_::kContiguous &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::Detail::kContiguousElementsPerLine()']]],
  ['kcount',['kCount',['../structcutlass_1_1AlignedBuffer.html#a16e2e6aa35c03e4a65b062123d9490ba',1,'cutlass::AlignedBuffer::kCount()'],['../classcutlass_1_1epilogue_1_1thread_1_1Convert.html#a00960402e155b59316966c64c4d46907',1,'cutlass::epilogue::thread::Convert::kCount()'],['../classcutlass_1_1epilogue_1_1thread_1_1LinearCombination.html#a6cf5c112befa1b3b00415033a8ffa837',1,'cutlass::epilogue::thread::LinearCombination::kCount()'],['../classcutlass_1_1epilogue_1_1thread_1_1LinearCombinationClamp.html#a27c80ef3fe591a6903974084dfd59897',1,'cutlass::epilogue::thread::LinearCombinationClamp::kCount()'],['../classcutlass_1_1epilogue_1_1thread_1_1LinearCombinationRelu.html#a70f9d8e1eef1ab9f67168af75442dfd9',1,'cutlass::epilogue::thread::LinearCombinationRelu::kCount()'],['../classcutlass_1_1epilogue_1_1thread_1_1LinearCombinationRelu_3_01ElementOutput___00_01Count_00_01int_00_01float_00_01Round_01_4.html#a1d809f513cad623975efcefc6bc496d2',1,'cutlass::epilogue::thread::LinearCombinationRelu&lt; ElementOutput_, Count, int, float, Round &gt;::kCount()'],['../classcutlass_1_1epilogue_1_1thread_1_1ReductionOpPlus.html#a7d5bd10e6f1b8db85718fb76f638d8d1',1,'cutlass::epilogue::thread::ReductionOpPlus::kCount()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1OutputTileShape.html#a245132551c8d4dea831c40b2dc8bcdd1',1,'cutlass::epilogue::threadblock::OutputTileShape::kCount()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Mask.html#a87069316c04bf6da67a5e7b5259def57',1,'cutlass::epilogue::threadblock::PredicatedTileIterator::Mask::kCount()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Mask.html#ad15acdea319b1a73f3e2b0c9f4cda448',1,'cutlass::epilogue::threadblock::InterleavedPredicatedTileIterator::Mask::kCount()'],['../structcutlass_1_1gemm_1_1GemmShape.html#a3768273d392c8e133812f2b3313f45e8',1,'cutlass::gemm::GemmShape::kCount()'],['../structcutlass_1_1layout_1_1PitchLinearShape.html#a02589c527ef668d1ff0b201c00bbee58',1,'cutlass::layout::PitchLinearShape::kCount()'],['../structcutlass_1_1MatrixShape.html#a8eba996819c3f542780766f3ef61957d',1,'cutlass::MatrixShape::kCount()'],['../structcutlass_1_1reduction_1_1thread_1_1ReduceAdd.html#ad0d42ebeb2b4bbee843e5ee31a8c62c9',1,'cutlass::reduction::thread::ReduceAdd::kCount()']]],
  ['kcrosswise',['kCrosswise',['../classcutlass_1_1gemm_1_1warp_1_1MmaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operand___0c7d419c589d601ce4eb603be566fea21.html#acc6388e433c480e8316fb1e628f13e0c',1,'cutlass::gemm::warp::MmaTensorOpMultiplicandTileIterator&lt; Shape_, Operand_, Element_, cutlass::layout::TensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, InstructionShape_, OpDelta_, 32, PartitionsK_ &gt;::kCrosswise()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operand___0e52ad425e1ee3e68544873f66733237b.html#ab727d22dc3746013f7a9e998b3311656',1,'cutlass::gemm::warp::MmaTensorOpMultiplicandTileIterator&lt; Shape_, Operand_, Element_, cutlass::layout::ColumnMajorTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, InstructionShape_, OpDelta_, 32, PartitionsK_ &gt;::kCrosswise()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operand___0352e0dcab42bc8360606874e00173556.html#a4272bab4dedfb6cb8a54ef5e6a087c56',1,'cutlass::gemm::warp::MmaTensorOpMultiplicandTileIterator&lt; Shape_, Operand_, Element_, cutlass::layout::RowMajorTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, InstructionShape_, OpDelta_, 32, PartitionsK_ &gt;::kCrosswise()'],['../structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a58b9d5809fed8924636d1caa32f661ef',1,'cutlass::layout::TensorOpMultiplicand::kCrosswise()'],['../structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a7b0e3438e68b0c121f40ff9c79ae8d51',1,'cutlass::layout::TensorOpMultiplicandCrosswise::kCrosswise()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__e9a9e0f4286f652f55eb9b863b21effe.html#a6fcdcb5f60d4afb2d2ac8db323ca4061',1,'cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::TensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::kCrosswise()']]],
  ['kcs16',['kCS16',['../namespacecutlass_1_1library.html#a366ecc865ac5b24cfdfd392199ba8e9ea5858a55c4aa43e6e37e7cb3bfe4ca07c',1,'cutlass::library']]],
  ['kcs32',['kCS32',['../namespacecutlass_1_1library.html#a366ecc865ac5b24cfdfd392199ba8e9eabfddfc16d664dfb08a56c922edd88f78',1,'cutlass::library']]],
  ['kcs4',['kCS4',['../namespacecutlass_1_1library.html#a366ecc865ac5b24cfdfd392199ba8e9eaa5934f63754a6ad442d40051bddcd68a',1,'cutlass::library']]],
  ['kcs64',['kCS64',['../namespacecutlass_1_1library.html#a366ecc865ac5b24cfdfd392199ba8e9eabd2f3d659ac172c463db4ab85a36ee7e',1,'cutlass::library']]],
  ['kcs8',['kCS8',['../namespacecutlass_1_1library.html#a366ecc865ac5b24cfdfd392199ba8e9ea83f535bd01efd735f598d7879217e7c1',1,'cutlass::library']]],
  ['kcu16',['kCU16',['../namespacecutlass_1_1library.html#a366ecc865ac5b24cfdfd392199ba8e9eacb496889062efad8506521307e5c5755',1,'cutlass::library']]],
  ['kcu32',['kCU32',['../namespacecutlass_1_1library.html#a366ecc865ac5b24cfdfd392199ba8e9ea9147149d838743a40fbef0fbf12a68e3',1,'cutlass::library']]],
  ['kcu4',['kCU4',['../namespacecutlass_1_1library.html#a366ecc865ac5b24cfdfd392199ba8e9eac040b78d1c3efc67f367c87f4c03c95f',1,'cutlass::library']]],
  ['kcu64',['kCU64',['../namespacecutlass_1_1library.html#a366ecc865ac5b24cfdfd392199ba8e9ea50769a687d9fa6f71dcb71328e2de995',1,'cutlass::library']]],
  ['kcu8',['kCU8',['../namespacecutlass_1_1library.html#a366ecc865ac5b24cfdfd392199ba8e9eaf49f96620bd9a8b0776da07dc7265a3e',1,'cutlass::library']]],
  ['kd',['kD',['../namespacecutlass_1_1gemm.html#a34338284023da7403c9ecbd3f406b2a6a64fa0b7138d2992f2e7daa5080eb5ed0',1,'cutlass::gemm']]],
  ['kdeltacluster',['kDeltaCluster',['../structcutlass_1_1epilogue_1_1threadblock_1_1OutputTileOptimalThreadMap_1_1Detail.html#a923045513e145cfd6cec97d14695332f',1,'cutlass::epilogue::threadblock::OutputTileOptimalThreadMap::Detail']]],
  ['kdeltacolumn',['kDeltaColumn',['../structcutlass_1_1epilogue_1_1threadblock_1_1detail_1_1RowArrangement_3_01Shape_00_01WarpsRemaini91159e6f7e123d881e3ec45101fa4f81.html#a531cc8d50e9d4cca5957795b0a6959a8',1,'cutlass::epilogue::threadblock::detail::RowArrangement&lt; Shape, WarpsRemaining, ElementsPerAccess, ElementSize, false &gt;::kDeltaColumn()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1detail_1_1RowArrangement_3_01Shape_00_01WarpsRemaini6d8790249bf12cac580da73bb37eb791.html#a67a897f36cb80fcb57cbd12b3cd31447',1,'cutlass::epilogue::threadblock::detail::RowArrangement&lt; Shape, WarpsRemaining, ElementsPerAccess, ElementSize, true &gt;::kDeltaColumn()']]],
  ['kdeltagroup',['kDeltaGroup',['../structcutlass_1_1epilogue_1_1threadblock_1_1OutputTileOptimalThreadMap_1_1Detail.html#a5e63cdc756d771c867a719f2bcf588fd',1,'cutlass::epilogue::threadblock::OutputTileOptimalThreadMap::Detail']]],
  ['kdeltarow',['kDeltaRow',['../structcutlass_1_1epilogue_1_1threadblock_1_1detail_1_1RowArrangement_3_01Shape_00_01WarpsRemaini91159e6f7e123d881e3ec45101fa4f81.html#a39f29272f1d0640f3ce29467ab053339',1,'cutlass::epilogue::threadblock::detail::RowArrangement&lt; Shape, WarpsRemaining, ElementsPerAccess, ElementSize, false &gt;::kDeltaRow()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1detail_1_1RowArrangement_3_01Shape_00_01WarpsRemaini6d8790249bf12cac580da73bb37eb791.html#a138328b13678f568a89c840fd081fb77',1,'cutlass::epilogue::threadblock::detail::RowArrangement&lt; Shape, WarpsRemaining, ElementsPerAccess, ElementSize, true &gt;::kDeltaRow()']]],
  ['kdevice',['kDevice',['../namespacecutlass_1_1library.html#af4d69c13cb62d2ef63e1e5491a32cabaa040394711ad65e20323fe0114e507103',1,'cutlass::library']]],
  ['kelementcount',['kElementCount',['../structcutlass_1_1transform_1_1thread_1_1Transpose_3_01ElementCount___00_01layout_1_1PitchLinearS99f8e05faf0bb5ed48a0154afe740d81.html#a5f267ce8d5c33de3d5b9eded4b4ee995',1,'cutlass::transform::thread::Transpose&lt; ElementCount_, layout::PitchLinearShape&lt; 4, 4 &gt;, int8_t &gt;']]],
  ['kelements',['kElements',['../classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a59927c40660b5f39218f5867d4158e5e',1,'cutlass::Array&lt; T, N, true &gt;::kElements()'],['../classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a56c28da772c3cf49799eeef4ee1eb981',1,'cutlass::Array&lt; T, N, false &gt;::kElements()'],['../structcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator2dThreadTile_3_01Shape___00_0b878062cc0cd214bf7e17d74ff17e246.html#a7e9c43dcd79254f5b71bec6a67c5d08b',1,'cutlass::transform::threadblock::PredicatedTileIterator2dThreadTile&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, Transpose_ &gt;::AccessType::kElements()']]],
  ['kelementsize',['kElementSize',['../structcutlass_1_1epilogue_1_1threadblock_1_1detail_1_1RowArrangement_3_01Shape_00_01WarpsRemaini91159e6f7e123d881e3ec45101fa4f81.html#aeebbd6691025dfc55b8daa8dbcec17b7',1,'cutlass::epilogue::threadblock::detail::RowArrangement&lt; Shape, WarpsRemaining, ElementsPerAccess, ElementSize, false &gt;::kElementSize()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1detail_1_1RowArrangement_3_01Shape_00_01WarpsRemaini6d8790249bf12cac580da73bb37eb791.html#a0cb5021aac5eb618e29c4717e1aece29',1,'cutlass::epilogue::threadblock::detail::RowArrangement&lt; Shape, WarpsRemaining, ElementsPerAccess, ElementSize, true &gt;::kElementSize()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1OutputTileOptimalThreadMap.html#a3c8e3aea6b632d5cecd34f64ce78c99e',1,'cutlass::epilogue::threadblock::OutputTileOptimalThreadMap::kElementSize()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedOutputTileThreadMap.html#a01a082e6664ea202d28119a270beee6d',1,'cutlass::epilogue::threadblock::InterleavedOutputTileThreadMap::kElementSize()'],['../structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#ad404b4e10152a83243aaf95f7e2ab26a',1,'cutlass::layout::VoltaTensorOpMultiplicandCongruous::kElementSize()'],['../structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#afa7b88c7e7c79f8a2ae71ea85f675943',1,'cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandCongruous::kElementSize()'],['../structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#a176656177616042b3ecf2b8d04519a73',1,'cutlass::layout::RowMajorVoltaTensorOpMultiplicandCongruous::kElementSize()'],['../structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#aa1ac4434032b09b6bbec2761288d6854',1,'cutlass::layout::VoltaTensorOpMultiplicandBCongruous::kElementSize()'],['../structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#a643e8cbff2d6339692cbec3ed34ad341',1,'cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandBCongruous::kElementSize()'],['../structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#a43c426d2a016db41441769263525254b',1,'cutlass::layout::RowMajorVoltaTensorOpMultiplicandBCongruous::kElementSize()'],['../structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#a5e0c3537c079116e936e0f7365f37642',1,'cutlass::layout::VoltaTensorOpMultiplicandCrosswise::kElementSize()'],['../structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#a44dba8d44799aa180e2e0d7dc4410eea',1,'cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandCrosswise::kElementSize()'],['../structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#abdcf1ff5105cf7a26817a62bc5e7adcc',1,'cutlass::layout::RowMajorVoltaTensorOpMultiplicandCrosswise::kElementSize()'],['../structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a1f35f08a131d76521a98c391acedb4e6',1,'cutlass::layout::TensorOpMultiplicand::kElementSize()'],['../structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a0486b60fe43ff7f8b10f0ab6cde0ead4',1,'cutlass::layout::TensorOpMultiplicandCongruous::kElementSize()'],['../structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#a075ffc6df8fc96d549ef16cd61ab11e4',1,'cutlass::layout::TensorOpMultiplicandCongruous&lt; 32, Crosswise &gt;::kElementSize()'],['../structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#aa683ea614d333101f9fb6c221337b4a2',1,'cutlass::layout::ColumnMajorTensorOpMultiplicandCongruous::kElementSize()'],['../structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#a852a5e6f718ab0e91b0cb6a0e6148648',1,'cutlass::layout::RowMajorTensorOpMultiplicandCongruous::kElementSize()'],['../structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#af07f2e40a9ba698ed0e98ac449b3f61f',1,'cutlass::layout::TensorOpMultiplicandCrosswise::kElementSize()'],['../structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#a6bd031a9b730ba2a609eef0457994524',1,'cutlass::layout::ColumnMajorTensorOpMultiplicandCrosswise::kElementSize()'],['../structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#a07b5e619df35284decc0941c6f860e63',1,'cutlass::layout::RowMajorTensorOpMultiplicandCrosswise::kElementSize()'],['../structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#a105bcb898baf9d42df209f1a6f60ecf2',1,'cutlass::layout::TensorOpMultiplicandColumnMajorInterleaved::kElementSize()'],['../structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#aba9287009e012b428198acab4ad053f9',1,'cutlass::layout::TensorOpMultiplicandRowMajorInterleaved::kElementSize()']]],
  ['kelementsperaccess',['kElementsPerAccess',['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultEpilogueComplexTensorOp.html#afc3c42f94722a38fbf23436fc16320e5',1,'cutlass::epilogue::threadblock::DefaultEpilogueComplexTensorOp::kElementsPerAccess()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultEpilogueSimt.html#aa1838e16d322ee1130b16b625f64fd57',1,'cutlass::epilogue::threadblock::DefaultEpilogueSimt::kElementsPerAccess()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultEpilogueTensorOp.html#ad3cb41c7fa82b4fe104c5e736308c739',1,'cutlass::epilogue::threadblock::DefaultEpilogueTensorOp::kElementsPerAccess()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultInterleavedEpilogueTensorOp.html#a70531ca686ab28318d0ab9a0a3fb4e56',1,'cutlass::epilogue::threadblock::DefaultInterleavedEpilogueTensorOp::kElementsPerAccess()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultEpilogueVoltaTensorOp.html#a8a7902784612c55f9a362d5396064647',1,'cutlass::epilogue::threadblock::DefaultEpilogueVoltaTensorOp::kElementsPerAccess()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultEpilogueWmmaTensorOp.html#a3a463148ab913accd64ad83757df869d',1,'cutlass::epilogue::threadblock::DefaultEpilogueWmmaTensorOp::kElementsPerAccess()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultThreadMapSimt.html#a5cad2530f66c02df18228866da95011e',1,'cutlass::epilogue::threadblock::DefaultThreadMapSimt::kElementsPerAccess()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultThreadMapTensorOp.html#a37861517987ce0e21ce8945e97a9d986',1,'cutlass::epilogue::threadblock::DefaultThreadMapTensorOp::kElementsPerAccess()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultInterleavedThreadMapTensorOp.html#a149e5db2c8243a9eaad88a3e7ff6ed61',1,'cutlass::epilogue::threadblock::DefaultInterleavedThreadMapTensorOp::kElementsPerAccess()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultThreadMapVoltaTensorOp_3_01ThreadblockShape__d58c94abc36b7c5c109b55202c6992e7.html#a1594f8bd01920460fe88e3a568b9ef3a',1,'cutlass::epilogue::threadblock::DefaultThreadMapVoltaTensorOp&lt; ThreadblockShape_, WarpShape_, PartitionsK, ElementOutput_, ElementsPerAccess, half_t &gt;::kElementsPerAccess()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultThreadMapVoltaTensorOp_3_01ThreadblockShape__95db04b7b72e34283958bd7fbf851d16.html#a370bb2559e8d10b20a5a424359efc1fb',1,'cutlass::epilogue::threadblock::DefaultThreadMapVoltaTensorOp&lt; ThreadblockShape_, WarpShape_, PartitionsK, ElementOutput_, ElementsPerAccess, float &gt;::kElementsPerAccess()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultThreadMapWmmaTensorOp.html#ab40912a1f7f30dc1b716d42d3f56d0b7',1,'cutlass::epilogue::threadblock::DefaultThreadMapWmmaTensorOp::kElementsPerAccess()'],['../classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#af8ecba80487465f25daac670e661627e',1,'cutlass::epilogue::threadblock::Epilogue::kElementsPerAccess()'],['../classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#ab5196507dba2b3252b53314596a0a770',1,'cutlass::epilogue::EpilogueWorkspace::kElementsPerAccess()'],['../classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedEpilogue.html#ac12e6d08fe3c1acb2e16fda1769ce570',1,'cutlass::epilogue::threadblock::InterleavedEpilogue::kElementsPerAccess()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1OutputTileThreadMap.html#ab160d4885797ba7efe5e939148457372',1,'cutlass::epilogue::threadblock::OutputTileThreadMap::kElementsPerAccess()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1detail_1_1RowArrangement_3_01Shape_00_01WarpsRemaini91159e6f7e123d881e3ec45101fa4f81.html#a06fa764531f37833164d36513884a255',1,'cutlass::epilogue::threadblock::detail::RowArrangement&lt; Shape, WarpsRemaining, ElementsPerAccess, ElementSize, false &gt;::kElementsPerAccess()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1detail_1_1RowArrangement_3_01Shape_00_01WarpsRemaini6d8790249bf12cac580da73bb37eb791.html#a90a8fb721b24a22b5d5cfc66e1316c85',1,'cutlass::epilogue::threadblock::detail::RowArrangement&lt; Shape, WarpsRemaining, ElementsPerAccess, ElementSize, true &gt;::kElementsPerAccess()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1OutputTileOptimalThreadMap.html#a17d8246401e42ef7fbf0adbfa478501b',1,'cutlass::epilogue::threadblock::OutputTileOptimalThreadMap::kElementsPerAccess()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1OutputTileOptimalThreadMap_1_1CompactedThreadMap.html#a47e2a4734e9456189ade9ab46dd706a3',1,'cutlass::epilogue::threadblock::OutputTileOptimalThreadMap::CompactedThreadMap::kElementsPerAccess()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedOutputTileThreadMap.html#a1f5300928e6936d73d41b1fbf90cadb8',1,'cutlass::epilogue::threadblock::InterleavedOutputTileThreadMap::kElementsPerAccess()'],['../classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#ab3ad40c4debee260a23d76194b114657',1,'cutlass::epilogue::threadblock::PredicatedTileIterator::kElementsPerAccess()'],['../classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#a5787986f693785bc41cbb07ff1190ebc',1,'cutlass::epilogue::threadblock::InterleavedPredicatedTileIterator::kElementsPerAccess()'],['../classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#ae7cc22c50e7e00742c7430080d673dc8',1,'cutlass::epilogue::threadblock::SharedLoadIterator::kElementsPerAccess()'],['../structcutlass_1_1epilogue_1_1warp_1_1SimtPolicy_3_01WarpShape___00_01Operator___00_01layout_1_1Rcef1c60e23e997017ae176c92931151d.html#abd3b551e211168087a609f2867154bd9',1,'cutlass::epilogue::warp::SimtPolicy&lt; WarpShape_, Operator_, layout::RowMajor, MmaSimtPolicy_ &gt;::kElementsPerAccess()'],['../structcutlass_1_1epilogue_1_1warp_1_1TensorOpPolicy_3_01WarpShape_00_01OperatorShape_00_01layout_1_1RowMajor_01_4.html#a118c88417d9e5be6e93cff0425080b7b',1,'cutlass::epilogue::warp::TensorOpPolicy&lt; WarpShape, OperatorShape, layout::RowMajor &gt;::kElementsPerAccess()'],['../structcutlass_1_1epilogue_1_1warp_1_1TensorOpPolicy_3_01WarpShape_00_01OperatorShape_00_01layout69549d10c3610d943987eb90e827bc05.html#a972aa0a9ed0e6c93cefe10f759d66846',1,'cutlass::epilogue::warp::TensorOpPolicy&lt; WarpShape, OperatorShape, layout::ColumnMajorInterleaved&lt; InterleavedK &gt; &gt;::kElementsPerAccess()'],['../classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a0345cdb103d294f0df1a34fd343b92d7',1,'cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, half_t, layout::RowMajor &gt;::kElementsPerAccess()'],['../classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a70793652078daffd36bf3ef14f1f15d4',1,'cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, float, layout::RowMajor &gt;::kElementsPerAccess()'],['../structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_1d48185f49e4d066f8e9327bf0856b7f.html#a3616da22cf9fac3bea048413778e5694',1,'cutlass::epilogue::warp::VoltaTensorOpPolicy&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, half_t, layout::RowMajor &gt;::kElementsPerAccess()'],['../structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_136ce744d4c1c6e8707f5a9785196194.html#afcf8488c1b19e0cc44709fe5b8cd0407',1,'cutlass::epilogue::warp::VoltaTensorOpPolicy&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, float, layout::RowMajor &gt;::kElementsPerAccess()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmShafafd5c61db86cbfe90863578ddd11092.html#a1fedb6678cf6a2ba73ad0baa66af319f',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 1, 1, 1 &gt;, ElementA_, layout::ColumnMajor, ElementB_, layout::RowMajor, ElementC_, LayoutC_, arch::OpClassSimt, 2, Operator_ &gt;::kElementsPerAccess()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmSha8da7a0cfbbe859b701fdd9f2b8566aa7.html#ac6cd2c817d78838775074fae95642e5c',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 1, 1, 1 &gt;, ElementA_, layout::RowMajor, ElementB_, layout::ColumnMajor, ElementC_, LayoutC_, arch::OpClassSimt, 2, Operator_ &gt;::kElementsPerAccess()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmSha84e9f8afb6a4ca9f5dcd219b182d16e7.html#a36805c4cedb5ba75337ec6454baddb25',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 1, 1, 1 &gt;, ElementA_, layout::RowMajor, ElementB_, layout::RowMajor, ElementC_, LayoutC_, arch::OpClassSimt, 2, Operator_ &gt;::kElementsPerAccess()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmShab94a11a77dd0565102710907089acee0.html#a601876c23a6a30441744fff49e35219b',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 1, 1, 1 &gt;, ElementA_, layout::ColumnMajor, ElementB_, layout::ColumnMajor, ElementC_, LayoutC_, arch::OpClassSimt, 2, Operator_ &gt;::kElementsPerAccess()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01Instruc2bf00737f4ad0a9da9a8be6d3e66c152.html#a3efb100b305719721211fdbdc85cbe94',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, InstructionShape_, ElementA_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt;, ElementB_, layout::RowMajorInterleaved&lt; InterleavedK &gt;, ElementC_, LayoutC_, arch::OpClassTensorOp, 2, Operator_, AccumulatorsInRowMajor &gt;::kElementsPerAccess()'],['../structcutlass_1_1gemm_1_1warp_1_1MmaVoltaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operafa294175b280756dd8388f9ffe7b72c4.html#a9f16b3a97439a83a010077a02a19d31b',1,'cutlass::gemm::warp::MmaVoltaTensorOpMultiplicandTileIterator&lt; Shape_, Operand_, Element_, cutlass::layout::VoltaTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, KBlock &gt;, InstructionShape_, OpDelta_, 32 &gt;::Policy::kElementsPerAccess()'],['../structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a21bc56227398fa3a011e635a88d5d595',1,'cutlass::layout::VoltaTensorOpMultiplicandCongruous::kElementsPerAccess()'],['../structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#a97621de01ef7a3c31f46e97e38dab9b2',1,'cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandCongruous::kElementsPerAccess()'],['../structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#ae2e6dc5d33fe0acbd5e15440963d8ec9',1,'cutlass::layout::RowMajorVoltaTensorOpMultiplicandCongruous::kElementsPerAccess()'],['../structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#a4f63aebef89812b3a2424a818f5919f4',1,'cutlass::layout::VoltaTensorOpMultiplicandBCongruous::kElementsPerAccess()'],['../structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#af7fb6b600c555fc0c5fb649289528854',1,'cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandBCongruous::kElementsPerAccess()'],['../structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#a57a4589fb0bc4b5675577326f21250b9',1,'cutlass::layout::RowMajorVoltaTensorOpMultiplicandBCongruous::kElementsPerAccess()'],['../structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#a150d62daad6c89add3c0cc3bf6402b39',1,'cutlass::layout::VoltaTensorOpMultiplicandCrosswise::kElementsPerAccess()'],['../structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#a9fee65225e8075cf8d2ecc812c30f35e',1,'cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandCrosswise::kElementsPerAccess()'],['../structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#aa9cd0b0069123cc028acd65f5f395f19',1,'cutlass::layout::RowMajorVoltaTensorOpMultiplicandCrosswise::kElementsPerAccess()'],['../structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a653fa6fd827643bcb835f6a715ac8d25',1,'cutlass::layout::TensorOpMultiplicand::kElementsPerAccess()'],['../structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a1937f2fe9816df95d76ef9036345a5c9',1,'cutlass::layout::TensorOpMultiplicandCongruous::kElementsPerAccess()'],['../structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#a4349d6c8b646e4537f7bd43982d07ffd',1,'cutlass::layout::TensorOpMultiplicandCongruous&lt; 32, Crosswise &gt;::kElementsPerAccess()'],['../structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#a1e1b169e7b1108d6c6e975d259938431',1,'cutlass::layout::ColumnMajorTensorOpMultiplicandCongruous::kElementsPerAccess()'],['../structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#a5322b178b2699ec72ac38ed4e6ba3bb7',1,'cutlass::layout::RowMajorTensorOpMultiplicandCongruous::kElementsPerAccess()'],['../structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a11572d8b23d45062f1844286c9c63db6',1,'cutlass::layout::TensorOpMultiplicandCrosswise::kElementsPerAccess()'],['../structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#a31427122376ca36d9313e06664b0ee8a',1,'cutlass::layout::ColumnMajorTensorOpMultiplicandCrosswise::kElementsPerAccess()'],['../structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#ab58ab635041661f1a77c5373ca9ee48d',1,'cutlass::layout::RowMajorTensorOpMultiplicandCrosswise::kElementsPerAccess()'],['../structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#a6c0474f980cd125cbcd8b7f65bb004f5',1,'cutlass::layout::TensorOpMultiplicandColumnMajorInterleaved::kElementsPerAccess()'],['../structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#a098afbb5810784378b875b41966687ee',1,'cutlass::layout::TensorOpMultiplicandRowMajorInterleaved::kElementsPerAccess()'],['../classcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK.html#a014e2940dbfce4b0d4f77bb3b03e0ab0',1,'cutlass::reduction::kernel::ReduceSplitK::kElementsPerAccess()'],['../structcutlass_1_1transform_1_1PitchLinearStripminedThreadMap.html#a79dd4ad5e5e96c8c992d683825f32c48',1,'cutlass::transform::PitchLinearStripminedThreadMap::kElementsPerAccess()'],['../structcutlass_1_1transform_1_1PitchLinearTilePolicyStripminedThreadContiguous.html#ad659c8fed0e319137c2bfa9a1a7c161f',1,'cutlass::transform::PitchLinearTilePolicyStripminedThreadContiguous::kElementsPerAccess()'],['../structcutlass_1_1transform_1_1PitchLinearTilePolicyStripminedThreadStrided.html#a88287ed2be2db89355c9a2a54ac89a20',1,'cutlass::transform::PitchLinearTilePolicyStripminedThreadStrided::kElementsPerAccess()'],['../structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html#a5d58eb41348e3acb88f815c706b3e750',1,'cutlass::transform::PitchLinearWarpRakedThreadMap::kElementsPerAccess()'],['../structcutlass_1_1transform_1_1TransposePitchLinearThreadMap.html#a9c5c6785a5d4bb21d93b3a3e18692235',1,'cutlass::transform::TransposePitchLinearThreadMap::kElementsPerAccess()'],['../structcutlass_1_1transform_1_1TransposePitchLinearThreadMapSimt.html#a0b699122a1c8057231ae14d18aa9ed49',1,'cutlass::transform::TransposePitchLinearThreadMapSimt::kElementsPerAccess()'],['../structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap.html#a8d3b364145189790a0e5fa033d63f858',1,'cutlass::transform::PitchLinearWarpStripedThreadMap::kElementsPerAccess()'],['../structcutlass_1_1transform_1_1PitchLinear2DThreadTileStripminedThreadMap_3_01Shape___00_01Thread0082c3467229b12cc9dd996283ee7160.html#a047e42641c9f61371d2b631e471f20e5',1,'cutlass::transform::PitchLinear2DThreadTileStripminedThreadMap&lt; Shape_, Threads, cutlass::layout::PitchLinearShape&lt; 4, 4 &gt; &gt;::kElementsPerAccess()'],['../structcutlass_1_1transform_1_1TransposePitchLinearThreadMap2DThreadTile.html#a90624d1f686da60922f9bd7ddc6df5b7',1,'cutlass::transform::TransposePitchLinearThreadMap2DThreadTile::kElementsPerAccess()']]],
  ['kelementsperiteration',['kElementsPerIteration',['../structcutlass_1_1epilogue_1_1warp_1_1SimtPolicy_3_01WarpShape___00_01Operator___00_01layout_1_1Rcef1c60e23e997017ae176c92931151d.html#adbc8b3fe881c629baa6b45d44f838ca2',1,'cutlass::epilogue::warp::SimtPolicy&lt; WarpShape_, Operator_, layout::RowMajor, MmaSimtPolicy_ &gt;']]],
  ['kelementspermma',['kElementsPerMma',['../structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_1d48185f49e4d066f8e9327bf0856b7f.html#a0c87fbb52f690deb4316a416660e9ca6',1,'cutlass::epilogue::warp::VoltaTensorOpPolicy&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, half_t, layout::RowMajor &gt;::kElementsPerMma()'],['../structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_136ce744d4c1c6e8707f5a9785196194.html#a6d6baf51c49ea98de2db365806f13326',1,'cutlass::epilogue::warp::VoltaTensorOpPolicy&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, float, layout::RowMajor &gt;::kElementsPerMma()']]],
  ['kelementsperstoreditem',['kElementsPerStoredItem',['../classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a4a6f489743eb03c5c97fe6bb3ed2fa22',1,'cutlass::Array&lt; T, N, false &gt;::kElementsPerStoredItem()'],['../classcutlass_1_1HostTensor.html#ad8d8df8fbb877de1d29978288405bf5f',1,'cutlass::HostTensor::kElementsPerStoredItem()']]],
  ['kepilogueelementsperaccess',['kEpilogueElementsPerAccess',['../structcutlass_1_1gemm_1_1kernel_1_1DefaultGemm_3_01ElementA_00_01LayoutA_00_01kAlignmentA_00_01Edd80343e6570718ed237122e4ebf7fb5.html#a168e0b333a9a3d867e994db59a6d8d12',1,'cutlass::gemm::kernel::DefaultGemm&lt; ElementA, LayoutA, kAlignmentA, ElementB, LayoutB, kAlignmentB, ElementC, layout::RowMajor, ElementAccumulator, arch::OpClassSimt, ArchTag, ThreadblockShape, WarpShape, GemmShape&lt; 1, 1, 1 &gt;, EpilogueOutputOp, ThreadblockSwizzle, 2, SplitKSerial, Operator &gt;::kEpilogueElementsPerAccess()'],['../structcutlass_1_1gemm_1_1kernel_1_1DefaultGemm_3_01int8__t_00_01LayoutA_00_01kAlignmentA_00_01inf48440732c1c5f42ddbfaba179861815.html#ad2025c61a9ca42ea22fbf62e8a38e632',1,'cutlass::gemm::kernel::DefaultGemm&lt; int8_t, LayoutA, kAlignmentA, int8_t, LayoutB, kAlignmentB, ElementC, LayoutC, ElementAccumulator, arch::OpClassSimt, ArchTag, ThreadblockShape, WarpShape, GemmShape&lt; 1, 1, 4 &gt;, EpilogueOutputOp, ThreadblockSwizzle, 2, SplitKSerial, Operator, false &gt;::kEpilogueElementsPerAccess()']]],
  ['kernel',['Kernel',['../namespacecutlass.html#acec34e54a2a5fb612a077280b9734606',1,'cutlass']]],
  ['kernel_5flaunch_2eh',['kernel_launch.h',['../kernel__launch_8h.html',1,'']]],
  ['kernelclass',['KernelClass',['../structcutlass_1_1reduction_1_1BatchedReductionTraits.html#a085c72d54426f5eb60f5bffa9c383229',1,'cutlass::reduction::BatchedReductionTraits']]],
  ['kernellaunchconfiguration',['KernelLaunchConfiguration',['../structcutlass_1_1KernelLaunchConfiguration.html',1,'cutlass']]],
  ['kernellaunchconfiguration',['KernelLaunchConfiguration',['../structcutlass_1_1KernelLaunchConfiguration.html#a726db328ccc8f5e186ff8e7cef568eaa',1,'cutlass::KernelLaunchConfiguration']]],
  ['kerrorinternal',['kErrorInternal',['../namespacecutlass.html#ac5a88c5840a28a9e0206b9cc7812a18da4d3b5847a0b17037c6b69faf8b1d4d71',1,'cutlass']]],
  ['kerrorinvalidlayout',['kErrorInvalidLayout',['../namespacecutlass.html#ac5a88c5840a28a9e0206b9cc7812a18da7fa8ed49e2d3f7e9af8ad025cdca548c',1,'cutlass']]],
  ['kerrorinvalidproblem',['kErrorInvalidProblem',['../namespacecutlass.html#ac5a88c5840a28a9e0206b9cc7812a18da58c16acfe39125423ff22c907487ba2c',1,'cutlass']]],
  ['kerrormisalignedoperand',['kErrorMisalignedOperand',['../namespacecutlass.html#ac5a88c5840a28a9e0206b9cc7812a18daa4867e1466f5d067dbec566abfe5a67a',1,'cutlass']]],
  ['kerrornotsupported',['kErrorNotSupported',['../namespacecutlass.html#ac5a88c5840a28a9e0206b9cc7812a18da3af1f6b1c6965aa925910b89e4152ab8',1,'cutlass']]],
  ['kerrorworkspacenull',['kErrorWorkspaceNull',['../namespacecutlass.html#ac5a88c5840a28a9e0206b9cc7812a18da6467125354474c5cff2f3c920abb7d3f',1,'cutlass']]],
  ['keys',['keys',['../structcutlass_1_1CommandLine.html#a1603f1c65c6d8d3d4262443b40e5c290',1,'cutlass::CommandLine']]],
  ['kf16',['kF16',['../namespacecutlass_1_1library.html#a366ecc865ac5b24cfdfd392199ba8e9ea13a852aa20c7c1030c7d2588b5d799b6',1,'cutlass::library']]],
  ['kf32',['kF32',['../namespacecutlass_1_1library.html#a366ecc865ac5b24cfdfd392199ba8e9ea941c992a0e3d2eb28203202730996ba2',1,'cutlass::library']]],
  ['kf64',['kF64',['../namespacecutlass_1_1library.html#a366ecc865ac5b24cfdfd392199ba8e9ea5234930e162acb53b17e178221057319',1,'cutlass::library']]],
  ['kfactor',['kFactor',['../structcutlass_1_1layout_1_1TensorOpMultiplicand.html#abb0b4f936c0e251e9b3560fc9b7820f6',1,'cutlass::layout::TensorOpMultiplicand::kFactor()'],['../structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a6f1f52234a9fb636ff84f2139ffa432b',1,'cutlass::layout::TensorOpMultiplicandCrosswise::kFactor()']]],
  ['kgemm',['kGemm',['../namespacecutlass_1_1library.html#ae609b16f8fa78f39136fc0a9802e4459a35c087a4c81dc032460aeba0b062add7',1,'cutlass::library::kGemm()'],['../namespacecutlass_1_1library.html#a8a2c782ab9bf9e19f99fdfcaf7f1c182a35c087a4c81dc032460aeba0b062add7',1,'cutlass::library::kGemm()']]],
  ['kgroup',['kGroup',['../structcutlass_1_1epilogue_1_1threadblock_1_1OutputTileShape.html#a20e91af94bad3e155647563375f205b2',1,'cutlass::epilogue::threadblock::OutputTileShape']]],
  ['kgroupcount',['kGroupCount',['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultThreadMapSimt_1_1Detail.html#a29cdfb7311f00b37c18a8e1e83984323',1,'cutlass::epilogue::threadblock::DefaultThreadMapSimt::Detail']]],
  ['kgrouppertile',['kGroupPerTile',['../classcutlass_1_1gemm_1_1warp_1_1MmaSimtTileIterator_3_01Shape___00_01Operand_1_1kA_00_01Element_f0ce904a9294556f15e1cc9cf7c99a93.html#abcaaf57ef8d27c9b2c1644cf2d8ff627',1,'cutlass::gemm::warp::MmaSimtTileIterator&lt; Shape_, Operand::kA, Element_, layout::ColumnMajorInterleaved&lt; 4 &gt;, Policy_, PartitionsK, PartitionGroupSize &gt;::kGroupPerTile()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaSimtTileIterator_3_01Shape___00_01Operand_1_1kB_00_01Element_ada156b62fcbdce47009c5bf1321c92c.html#aed1185c5f83fa070387352de6619c467',1,'cutlass::gemm::warp::MmaSimtTileIterator&lt; Shape_, Operand::kB, Element_, layout::RowMajorInterleaved&lt; 4 &gt;, Policy_, PartitionsK, PartitionGroupSize &gt;::kGroupPerTile()']]],
  ['kgroupspertile',['kGroupsPerTile',['../structcutlass_1_1gemm_1_1warp_1_1MmaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operand___07638f8b7761f6e2e2e6918e2c05e739.html#ad8f1780a4422082360b148bd8d7ab279',1,'cutlass::gemm::warp::MmaTensorOpMultiplicandTileIterator&lt; Shape_, Operand_, Element_, cutlass::layout::TensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, 64 &gt;, InstructionShape_, OpDelta_, 32, PartitionsK_ &gt;::Policy::kGroupsPerTile()'],['../structcutlass_1_1gemm_1_1warp_1_1MmaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operand___0784c74bd670999ec23ad8ef9dc55777.html#ac4e856d9fa19691cd13950f8bde12233',1,'cutlass::gemm::warp::MmaTensorOpMultiplicandTileIterator&lt; Shape_, Operand_, Element_, cutlass::layout::TensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, InstructionShape_, OpDelta_, 32, PartitionsK_ &gt;::Policy::kGroupsPerTile()']]],
  ['kh',['kH',['../structcutlass_1_1Tensor4DCoord.html#aa4014fb6f869b2b5c16796f4435eb110',1,'cutlass::Tensor4DCoord']]],
  ['khermitian',['kHermitian',['../namespacecutlass.html#ab7e605b25da48d89f98764c12d50b467a01311591f8ae2ee4658df72786ed1050',1,'cutlass']]],
  ['khost',['kHost',['../namespacecutlass_1_1library.html#af4d69c13cb62d2ef63e1e5491a32cabaaab2568040d6d4e966d109b5adf7f5175',1,'cutlass::library']]],
  ['kimaginaryindex',['kImaginaryIndex',['../classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8.html#acbb82d0594bc94dc0fe4c6bb4c9a22e4',1,'cutlass::epilogue::warp::FragmentIteratorComplexTensorOp&lt; WarpShape_, OperatorShape_, OperatorElementC_, OperatorFragmentC_, layout::RowMajor &gt;']]],
  ['kind',['kind',['../structcutlass_1_1Distribution.html#a07cb089b346ef06e198f6043128264fb',1,'cutlass::Distribution::kind()'],['../structcutlass_1_1library_1_1OperationDescription.html#a50b52d8a4e961279bd32269aa1d40ef9',1,'cutlass::library::OperationDescription::kind()'],['../structcutlass_1_1Distribution.html#a499f4023e0d42356ce71d38cc32bf92a',1,'cutlass::Distribution::Kind()']]],
  ['kinterleave',['kInterleave',['../classcutlass_1_1gemm_1_1warp_1_1MmaSimtTileIterator_3_01Shape___00_01Operand_1_1kA_00_01Element_f0ce904a9294556f15e1cc9cf7c99a93.html#aa59040a13580682019401fb436f43c69',1,'cutlass::gemm::warp::MmaSimtTileIterator&lt; Shape_, Operand::kA, Element_, layout::ColumnMajorInterleaved&lt; 4 &gt;, Policy_, PartitionsK, PartitionGroupSize &gt;::kInterleave()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaSimtTileIterator_3_01Shape___00_01Operand_1_1kB_00_01Element_ada156b62fcbdce47009c5bf1321c92c.html#a43b7ae0d44deda8db968e1f3eec83d0f',1,'cutlass::gemm::warp::MmaSimtTileIterator&lt; Shape_, Operand::kB, Element_, layout::RowMajorInterleaved&lt; 4 &gt;, Policy_, PartitionsK, PartitionGroupSize &gt;::kInterleave()'],['../structcutlass_1_1layout_1_1RowMajorInterleaved.html#ac8c62a27c3c64309c24f9f0a02e9edfb',1,'cutlass::layout::RowMajorInterleaved::kInterleave()'],['../structcutlass_1_1layout_1_1ColumnMajorInterleaved.html#afa55ccfb47e6460c7babcd30b43d0d63',1,'cutlass::layout::ColumnMajorInterleaved::kInterleave()'],['../classcutlass_1_1layout_1_1TensorNCxHWx.html#aa9d3b74f0ae63b54b4e3a55cdbb9edef',1,'cutlass::layout::TensorNCxHWx::kInterleave()'],['../classcutlass_1_1layout_1_1TensorCxRSKx.html#a325038d558d62bd3a30e14f5938b5ace',1,'cutlass::layout::TensorCxRSKx::kInterleave()']]],
  ['kinterleavedk',['kInterleavedK',['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultInterleavedThreadMapTensorOp.html#af33e0517fef98a7ebed04c7d8203601c',1,'cutlass::epilogue::threadblock::DefaultInterleavedThreadMapTensorOp::kInterleavedK()'],['../classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_e459aab140a2ce78336e584f95886726.html#afdf4bf88b1edd663c25a7a8b1f4465b6',1,'cutlass::epilogue::warp::FragmentIteratorTensorOp&lt; WarpShape_, OperatorShape_, OperatorElementC_, OperatorFragmentC_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt; &gt;::kInterleavedK()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01Instruc2bf00737f4ad0a9da9a8be6d3e66c152.html#a2aa6a2072172c17cf381d2fac83c19f1',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, InstructionShape_, ElementA_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt;, ElementB_, layout::RowMajorInterleaved&lt; InterleavedK &gt;, ElementC_, LayoutC_, arch::OpClassTensorOp, 2, Operator_, AccumulatorsInRowMajor &gt;::kInterleavedK()'],['../structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#aec2c91247bbff7c4abb974a6d30ae202',1,'cutlass::layout::TensorOpMultiplicandColumnMajorInterleaved::kInterleavedK()'],['../structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#a25870e5c3148680a6a0c1b2a04b220d4',1,'cutlass::layout::TensorOpMultiplicandRowMajorInterleaved::kInterleavedK()'],['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator_3_01Shape___00_01Elemenab63a1e105bf37f6371516cb9e2c5a7a.html#aca2497716eba6617c5ad2a4d00084926',1,'cutlass::transform::threadblock::PredicatedTileAccessIterator&lt; Shape_, Element_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessType_ &gt;::kInterleavedK()'],['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator_3_01Shape___00_01Elemen809793e785fb4211888c6b4e5dcfcb39.html#afa688804fa59229af07b1d22e9722e6d',1,'cutlass::transform::threadblock::PredicatedTileAccessIterator&lt; Shape_, Element_, layout::RowMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessType_ &gt;::kInterleavedK()'],['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#ab71694768c19d697bf73481ca0d7a3d5',1,'cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::kInterleavedK()'],['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a001d75d0f08f26b38ceefa6d63426f09',1,'cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::kInterleavedK()']]],
  ['kinterleavedtilesm',['kInterleavedTilesM',['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultThreadMapVoltaTensorOp_3_01ThreadblockShape__4433cc988100e98097a748d2670fb0fc.html#a2f6ac9f5289c96c724bbf22598c5bb48',1,'cutlass::epilogue::threadblock::DefaultThreadMapVoltaTensorOp&lt; ThreadblockShape_, WarpShape_, PartitionsK, ElementOutput_, ElementsPerAccess, half_t &gt;::Detail::kInterleavedTilesM()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultThreadMapVoltaTensorOp_3_01ThreadblockShape__52116c60c62f0fd520071558e42b814f.html#a995d3fd248d01a16c449046aaf4fd00a',1,'cutlass::epilogue::threadblock::DefaultThreadMapVoltaTensorOp&lt; ThreadblockShape_, WarpShape_, PartitionsK, ElementOutput_, ElementsPerAccess, float &gt;::Detail::kInterleavedTilesM()']]],
  ['kinvalid',['kInvalid',['../namespacecutlass.html#ac5a88c5840a28a9e0206b9cc7812a18dab10913c938482a8aa4ba85b7a1116cb4',1,'cutlass::kInvalid()'],['../namespacecutlass_1_1library.html#aa863c416529c1fe76555be9760619a30ab10913c938482a8aa4ba85b7a1116cb4',1,'cutlass::library::kInvalid()'],['../namespacecutlass_1_1library.html#a366ecc865ac5b24cfdfd392199ba8e9eab10913c938482a8aa4ba85b7a1116cb4',1,'cutlass::library::kInvalid()'],['../namespacecutlass_1_1library.html#ae609b16f8fa78f39136fc0a9802e4459ab10913c938482a8aa4ba85b7a1116cb4',1,'cutlass::library::kInvalid()'],['../namespacecutlass_1_1library.html#af4d69c13cb62d2ef63e1e5491a32cabaab10913c938482a8aa4ba85b7a1116cb4',1,'cutlass::library::kInvalid()'],['../namespacecutlass_1_1library.html#a5ccf134b261aafdde24f4185cf1ddda6ab10913c938482a8aa4ba85b7a1116cb4',1,'cutlass::library::kInvalid()'],['../namespacecutlass_1_1library.html#a6e7f08a7db0273b3da7cc7ec6188b95eab10913c938482a8aa4ba85b7a1116cb4',1,'cutlass::library::kInvalid()'],['../namespacecutlass_1_1library.html#a8a2c782ab9bf9e19f99fdfcaf7f1c182ab10913c938482a8aa4ba85b7a1116cb4',1,'cutlass::library::kInvalid()']]],
  ['kisbetazero',['kIsBetaZero',['../classcutlass_1_1gemm_1_1device_1_1Gemm.html#a0303e85432228170eba7dc4b418c86b4',1,'cutlass::gemm::device::Gemm::kIsBetaZero()'],['../classcutlass_1_1gemm_1_1device_1_1Gemm_3_01ElementA___00_01LayoutA___00_01ElementB___00_01Layout4d0960ae6b1d1bf19e6239dbd002249c.html#a98d1d07f32f29b29e883775fcd276833',1,'cutlass::gemm::device::Gemm&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, AlignmentA, AlignmentB, SplitKSerial, Operator_, IsBetaZero &gt;::kIsBetaZero()']]],
  ['kisconventionallayout',['kIsConventionalLayout',['../structcutlass_1_1gemm_1_1thread_1_1detail_1_1EnableMma__Crow__SM60.html#a8ec734b2126bd5147abafee8a3b7be70',1,'cutlass::gemm::thread::detail::EnableMma_Crow_SM60']]],
  ['kiterarionsperaccess',['kIterarionsPerAccess',['../structcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_0390833403016f5d817416e20828845df.html#a39d8f3a62387cdc01df2a1fdd899fa51',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::VoltaTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Shape_::kContiguous &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::Detail']]],
  ['kiterations',['kIterations',['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultThreadMapSimt_1_1Detail.html#a3129fc3da8ee72066cc96de03318afae',1,'cutlass::epilogue::threadblock::DefaultThreadMapSimt::Detail::kIterations()'],['../classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#a230e054d544fb2499fe0062a6c87eaae',1,'cutlass::epilogue::EpilogueWorkspace::kIterations()'],['../classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#a7848f893107ec20d56abb46bc05e0e43',1,'cutlass::epilogue::threadblock::PredicatedTileIterator::kIterations()'],['../classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#a4e2a4d391df9a9fc709173cebde22501',1,'cutlass::epilogue::threadblock::InterleavedPredicatedTileIterator::kIterations()'],['../classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8.html#ad7012975aa28a6cdc90a1f28688693ec',1,'cutlass::epilogue::warp::FragmentIteratorComplexTensorOp&lt; WarpShape_, OperatorShape_, OperatorElementC_, OperatorFragmentC_, layout::RowMajor &gt;::kIterations()'],['../classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorSimt_3_01WarpShape___00_01Operator___00_01la3f2abc523201c1b0228df99119ab88e1.html#ad586a2c18bfc47524b2fac72f42c1976',1,'cutlass::epilogue::warp::FragmentIteratorSimt&lt; WarpShape_, Operator_, layout::RowMajor, MmaSimtPolicy_ &gt;::kIterations()'],['../classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_5e78dabe303f20d76b00c600aab61eda.html#ae452dfefe60053bdd46a19caaf3ec3f3',1,'cutlass::epilogue::warp::FragmentIteratorTensorOp&lt; WarpShape_, OperatorShape_, OperatorElementC_, OperatorFragmentC_, layout::RowMajor &gt;::kIterations()'],['../classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_e459aab140a2ce78336e584f95886726.html#a9326d02f0d226f190c50a621c3fb826e',1,'cutlass::epilogue::warp::FragmentIteratorTensorOp&lt; WarpShape_, OperatorShape_, OperatorElementC_, OperatorFragmentC_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt; &gt;::kIterations()'],['../classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b.html#a7bde70907d0ef820ef36d771b9b9c247',1,'cutlass::epilogue::warp::FragmentIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, half_t, layout::RowMajor &gt;::kIterations()'],['../classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2.html#a78d4cbe9dd5a59d6a220c5c761b522ce',1,'cutlass::epilogue::warp::FragmentIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, float, layout::RowMajor &gt;::kIterations()'],['../structcutlass_1_1epilogue_1_1warp_1_1SimtPolicy_3_01WarpShape___00_01Operator___00_01layout_1_1Rcef1c60e23e997017ae176c92931151d.html#ab00111b3e6511568121e457f10085f85',1,'cutlass::epilogue::warp::SimtPolicy&lt; WarpShape_, Operator_, layout::RowMajor, MmaSimtPolicy_ &gt;::kIterations()'],['../structcutlass_1_1epilogue_1_1warp_1_1TensorOpPolicy_3_01WarpShape_00_01OperatorShape_00_01layout_1_1RowMajor_01_4.html#afc40fb3730419f5be0fa88c3bc399a72',1,'cutlass::epilogue::warp::TensorOpPolicy&lt; WarpShape, OperatorShape, layout::RowMajor &gt;::kIterations()'],['../structcutlass_1_1epilogue_1_1warp_1_1TensorOpPolicy_3_01WarpShape_00_01OperatorShape_00_01layout69549d10c3610d943987eb90e827bc05.html#a7f4b6a177dced958f52e8c2d05a7dbcd',1,'cutlass::epilogue::warp::TensorOpPolicy&lt; WarpShape, OperatorShape, layout::ColumnMajorInterleaved&lt; InterleavedK &gt; &gt;::kIterations()'],['../classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#ad22571b7a352a6c9834341ff15614759',1,'cutlass::epilogue::warp::TileIteratorSimt&lt; WarpShape_, Operator_, Element_, layout::RowMajor, MmaSimtPolicy_ &gt;::kIterations()'],['../classcutlass_1_1epilogue_1_1warp_1_1TileIteratorTensorOp_3_01WarpShape___00_01OperatorShape___003cbb32beb84b4984cb7853662096d289.html#a5f4cbb21e1c17bc6e6f7938415b53ee8',1,'cutlass::epilogue::warp::TileIteratorTensorOp&lt; WarpShape_, OperatorShape_, Element_, layout::RowMajor &gt;::kIterations()'],['../classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a1be924f14d935345299d2c6bbddbf913',1,'cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, half_t, layout::RowMajor &gt;::kIterations()'],['../classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a54036a133462ef407b56ef9ab7e27033',1,'cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, float, layout::RowMajor &gt;::kIterations()'],['../structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_1d48185f49e4d066f8e9327bf0856b7f.html#a5adc29eec21790473e9fa5c1129b7324',1,'cutlass::epilogue::warp::VoltaTensorOpPolicy&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, half_t, layout::RowMajor &gt;::kIterations()'],['../structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_136ce744d4c1c6e8707f5a9785196194.html#aea54d5805f9b49122c1bf4147912b4ff',1,'cutlass::epilogue::warp::VoltaTensorOpPolicy&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, float, layout::RowMajor &gt;::kIterations()']]],
  ['kiterationscluster',['kIterationsCluster',['../structcutlass_1_1epilogue_1_1threadblock_1_1OutputTileOptimalThreadMap_1_1Detail.html#a08e51e7063db4385566380fe7f660867',1,'cutlass::epilogue::threadblock::OutputTileOptimalThreadMap::Detail']]],
  ['kiterationscolumn',['kIterationsColumn',['../structcutlass_1_1epilogue_1_1threadblock_1_1detail_1_1RowArrangement_3_01Shape_00_01WarpsRemaini91159e6f7e123d881e3ec45101fa4f81.html#aa60e9c20f478f032e2451a252bca21d1',1,'cutlass::epilogue::threadblock::detail::RowArrangement&lt; Shape, WarpsRemaining, ElementsPerAccess, ElementSize, false &gt;::kIterationsColumn()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1detail_1_1RowArrangement_3_01Shape_00_01WarpsRemaini6d8790249bf12cac580da73bb37eb791.html#a06b05d4b8916974eae38d00b1ab529fa',1,'cutlass::epilogue::threadblock::detail::RowArrangement&lt; Shape, WarpsRemaining, ElementsPerAccess, ElementSize, true &gt;::kIterationsColumn()']]],
  ['kiterationsgroup',['kIterationsGroup',['../structcutlass_1_1epilogue_1_1threadblock_1_1OutputTileOptimalThreadMap_1_1Detail.html#a3b5134a66ff28668e56dfce264e81670',1,'cutlass::epilogue::threadblock::OutputTileOptimalThreadMap::Detail']]],
  ['kiterationsperinstruction',['kIterationsPerInstruction',['../structcutlass_1_1epilogue_1_1warp_1_1TensorOpPolicy_3_01WarpShape_00_01OperatorShape_00_01layout_1_1RowMajor_01_4.html#a1525dbcf12c66f8b5413ebf7fc6fe5b7',1,'cutlass::epilogue::warp::TensorOpPolicy&lt; WarpShape, OperatorShape, layout::RowMajor &gt;::kIterationsPerInstruction()'],['../structcutlass_1_1epilogue_1_1warp_1_1TensorOpPolicy_3_01WarpShape_00_01OperatorShape_00_01layout69549d10c3610d943987eb90e827bc05.html#a3f83f7ea1229ddee9eb73e3b2b23c4d4',1,'cutlass::epilogue::warp::TensorOpPolicy&lt; WarpShape, OperatorShape, layout::ColumnMajorInterleaved&lt; InterleavedK &gt; &gt;::kIterationsPerInstruction()']]],
  ['kiterationsrow',['kIterationsRow',['../structcutlass_1_1epilogue_1_1threadblock_1_1detail_1_1RowArrangement_3_01Shape_00_01WarpsRemaini91159e6f7e123d881e3ec45101fa4f81.html#ab8caf1d08a3d7fa615c4e135926260e4',1,'cutlass::epilogue::threadblock::detail::RowArrangement&lt; Shape, WarpsRemaining, ElementsPerAccess, ElementSize, false &gt;::kIterationsRow()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1detail_1_1RowArrangement_3_01Shape_00_01WarpsRemaini6d8790249bf12cac580da73bb37eb791.html#a5d4957ce6dddc21bb761a9d51259c48b',1,'cutlass::epilogue::threadblock::detail::RowArrangement&lt; Shape, WarpsRemaining, ElementsPerAccess, ElementSize, true &gt;::kIterationsRow()']]],
  ['kk',['kK',['../structcutlass_1_1gemm_1_1GemmShape.html#a8b7ab79699355caf179323b7ad4c71fc',1,'cutlass::gemm::GemmShape::kK()'],['../structcutlass_1_1gemm_1_1GemmCoord.html#a521d4b8e720d2261c825e05397c92a5e',1,'cutlass::gemm::GemmCoord::kK()'],['../structcutlass_1_1gemm_1_1BatchedGemmCoord.html#a96bfdb9d11de1babab85b357ec101928',1,'cutlass::gemm::BatchedGemmCoord::kK()']]],
  ['kkblock',['kKBlock',['../classcutlass_1_1gemm_1_1warp_1_1MmaVoltaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operana2f40b28f0d2286b84d86f7238d67b52.html#a62c2d35000a63bca53391a0eab7e2e1c',1,'cutlass::gemm::warp::MmaVoltaTensorOpMultiplicandTileIterator&lt; Shape_, Operand_, Element_, cutlass::layout::VoltaTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, KBlock &gt;, InstructionShape_, OpDelta_, 32 &gt;::kKBlock()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaVoltaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operan5a221944f4a0e16ccab77ba684856942.html#a533fcb05c82297c360ce2b025530c287',1,'cutlass::gemm::warp::MmaVoltaTensorOpMultiplicandTileIterator&lt; Shape_, Operand_, Element_, cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, KBlock &gt;, InstructionShape_, OpDelta_, 32 &gt;::kKBlock()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaVoltaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operandcc9821c435540895138bc9af495f321.html#a2244908970938c5af5d2e55df8e8820e',1,'cutlass::gemm::warp::MmaVoltaTensorOpMultiplicandTileIterator&lt; Shape_, Operand_, Element_, cutlass::layout::RowMajorVoltaTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, KBlock &gt;, InstructionShape_, OpDelta_, 32 &gt;::kKBlock()'],['../structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#a8ecfcee5a9743848a57ec78e4a73b8ba',1,'cutlass::layout::VoltaTensorOpMultiplicandCrosswise::kKBlock()']]],
  ['kkn',['kKN',['../structcutlass_1_1gemm_1_1GemmShape.html#a8058a871bbf06767fde00007b1e3f33b',1,'cutlass::gemm::GemmShape']]],
  ['klanesinquad',['kLanesInQuad',['../structcutlass_1_1epilogue_1_1warp_1_1TileIteratorTensorOp_3_01WarpShape___00_01OperatorShape___05f11e023c9e6ee5f7a888fa4c5bbf6d1.html#a7848a076b84109151f08303706503063',1,'cutlass::epilogue::warp::TileIteratorTensorOp&lt; WarpShape_, OperatorShape_, Element_, layout::RowMajor &gt;::Detail::kLanesInQuad()'],['../structcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gemmffcab2297c8de8d0013602a39c525b78.html#a48cbbc34bbcea59e18a0456456d95647',1,'cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, half_t, layout::RowMajor &gt;::Detail::kLanesInQuad()'],['../structcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gemm770cbca45441d295d5d7433e8222a700.html#ad96cb0ceb99280d669956d3bfb1bfb2e',1,'cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, float, layout::RowMajor &gt;::Detail::kLanesInQuad()']]],
  ['kldsmopinner',['kLdsmOpInner',['../structcutlass_1_1gemm_1_1warp_1_1MmaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operand___07638f8b7761f6e2e2e6918e2c05e739.html#aecbe752f0ab6bd5d40885865e1c8136f',1,'cutlass::gemm::warp::MmaTensorOpMultiplicandTileIterator&lt; Shape_, Operand_, Element_, cutlass::layout::TensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, 64 &gt;, InstructionShape_, OpDelta_, 32, PartitionsK_ &gt;::Policy::kLdsmOpInner()'],['../structcutlass_1_1gemm_1_1warp_1_1MmaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operand___0784c74bd670999ec23ad8ef9dc55777.html#a1590e539d3eb1072f78c653cae1d73d0',1,'cutlass::gemm::warp::MmaTensorOpMultiplicandTileIterator&lt; Shape_, Operand_, Element_, cutlass::layout::TensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, InstructionShape_, OpDelta_, 32, PartitionsK_ &gt;::Policy::kLdsmOpInner()']]],
  ['kldsmopouter',['kLdsmOpOuter',['../structcutlass_1_1gemm_1_1warp_1_1MmaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operand___07638f8b7761f6e2e2e6918e2c05e739.html#ab3627254bfb25506ae9f50d5793ddf08',1,'cutlass::gemm::warp::MmaTensorOpMultiplicandTileIterator&lt; Shape_, Operand_, Element_, cutlass::layout::TensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, 64 &gt;, InstructionShape_, OpDelta_, 32, PartitionsK_ &gt;::Policy::kLdsmOpOuter()'],['../structcutlass_1_1gemm_1_1warp_1_1MmaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operand___0784c74bd670999ec23ad8ef9dc55777.html#a069db210d212589ef01cc0ad1d69ee65',1,'cutlass::gemm::warp::MmaTensorOpMultiplicandTileIterator&lt; Shape_, Operand_, Element_, cutlass::layout::TensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, InstructionShape_, OpDelta_, 32, PartitionsK_ &gt;::Policy::kLdsmOpOuter()']]],
  ['km',['km',['../structcutlass_1_1gemm_1_1GemmCoord.html#ad60d91a8e6acafca1f62ab3b3088b733',1,'cutlass::gemm::GemmCoord::km()'],['../structcutlass_1_1gemm_1_1GemmShape.html#a7a47fe0c44571a0a68a43c5a47cf676a',1,'cutlass::gemm::GemmShape::kM()'],['../structcutlass_1_1gemm_1_1GemmCoord.html#af1f5c03c35eaa406c6a63082da26bec3',1,'cutlass::gemm::GemmCoord::kM()'],['../structcutlass_1_1gemm_1_1BatchedGemmCoord.html#a52f4a43dbe52fd75208e4b46f3b5dc52',1,'cutlass::gemm::BatchedGemmCoord::kM()']]],
  ['kmask',['kMask',['../classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a6981c3aa259d3a1cc4818e29fa1d1423',1,'cutlass::Array&lt; T, N, false &gt;::kMask()'],['../structcutlass_1_1integer__subbyte.html#a17652e0d2e6e9397c7adf2eb7dbe1203',1,'cutlass::integer_subbyte::kMask()']]],
  ['kmemoryaccesssize',['kMemoryAccessSize',['../structcutlass_1_1epilogue_1_1threadblock_1_1detail_1_1RowArrangement_3_01Shape_00_01WarpsRemaini6d8790249bf12cac580da73bb37eb791.html#addf9830fdf962d6cff6dccd4fdaeb365',1,'cutlass::epilogue::threadblock::detail::RowArrangement&lt; Shape, WarpsRemaining, ElementsPerAccess, ElementSize, true &gt;']]],
  ['kminalignment',['kMinAlignment',['../classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#abd70e40965a6296adeeeb7ddcca7da90',1,'cutlass::epilogue::threadblock::SharedLoadIterator']]],
  ['kmincomputecapability',['kMinComputeCapability',['../structcutlass_1_1arch_1_1Sm50.html#ae14b658c3b0caa882d22ab3dac49f1f4',1,'cutlass::arch::Sm50::kMinComputeCapability()'],['../structcutlass_1_1arch_1_1Sm60.html#a35d6ac621f22c1936ccd65c961c2398e',1,'cutlass::arch::Sm60::kMinComputeCapability()'],['../structcutlass_1_1arch_1_1Sm61.html#aa81fbe4d676a8d23be330af8cb2cbecf',1,'cutlass::arch::Sm61::kMinComputeCapability()'],['../structcutlass_1_1arch_1_1Sm70.html#a6db30019eb2008480b217f035661b1bc',1,'cutlass::arch::Sm70::kMinComputeCapability()'],['../structcutlass_1_1arch_1_1Sm72.html#a109bf335d29769ed08570cff7e709242',1,'cutlass::arch::Sm72::kMinComputeCapability()'],['../structcutlass_1_1arch_1_1Sm75.html#a1e2a32a34aeb5b8dced1106b67456daa',1,'cutlass::arch::Sm75::kMinComputeCapability()']]],
  ['kmk',['kMK',['../structcutlass_1_1gemm_1_1GemmShape.html#af83abad945e08855b9e8ebff227dca1f',1,'cutlass::gemm::GemmShape']]],
  ['kmn',['kMN',['../structcutlass_1_1gemm_1_1GemmShape.html#ae09748d75be7d6b66151d49188d5d1fc',1,'cutlass::gemm::GemmShape']]],
  ['kmnk',['kMNK',['../structcutlass_1_1gemm_1_1GemmShape.html#a7597ae51cabc6efd8929e67233803086',1,'cutlass::gemm::GemmShape']]],
  ['kn',['kn',['../structcutlass_1_1gemm_1_1GemmCoord.html#af8f0abf128e5e0fb74bc81b3560e8110',1,'cutlass::gemm::GemmCoord::kn()'],['../structcutlass_1_1gemm_1_1GemmShape.html#a9fcbaa4b47b83d0c8a09979ad5c98a1e',1,'cutlass::gemm::GemmShape::kN()'],['../structcutlass_1_1gemm_1_1GemmCoord.html#a67f08a03dabee497fa5547cff0f1faea',1,'cutlass::gemm::GemmCoord::kN()'],['../structcutlass_1_1gemm_1_1BatchedGemmCoord.html#acb2978c49363fd0f44438408e3258036',1,'cutlass::gemm::BatchedGemmCoord::kN()'],['../structcutlass_1_1Tensor4DCoord.html#acb0b48b015b75e2d7a226a69f5a2f3b8',1,'cutlass::Tensor4DCoord::kN()']]],
  ['knm',['knm',['../structcutlass_1_1gemm_1_1GemmCoord.html#ae4d6a46e4036d5f3d574ab6ae305e619',1,'cutlass::gemm::GemmCoord']]],
  ['knone',['kNone',['../namespacecutlass.html#a59f08b1b99c4d52257b962d35ba55cdea35c3ace1970663a16e5c65baa5941b13',1,'cutlass::kNone()'],['../namespacecutlass.html#ab7e605b25da48d89f98764c12d50b467a35c3ace1970663a16e5c65baa5941b13',1,'cutlass::kNone()'],['../namespacecutlass_1_1library.html#aa2b27589531eec608a86cf43a36c4175a35c3ace1970663a16e5c65baa5941b13',1,'cutlass::library::kNone()'],['../namespacecutlass_1_1library.html#a5ccf134b261aafdde24f4185cf1ddda6a35c3ace1970663a16e5c65baa5941b13',1,'cutlass::library::kNone()']]],
  ['kopdelta',['kOpDelta',['../classcutlass_1_1gemm_1_1warp_1_1MmaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operand___0ed7daaeba1c095e77f68533d4d2c475c.html#a00bb713fb2ac605d69dc57014b058eb4',1,'cutlass::gemm::warp::MmaTensorOpMultiplicandTileIterator&lt; Shape_, Operand_, Element_, cutlass::layout::TensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, 64 &gt;, InstructionShape_, OpDelta_, 32, PartitionsK_ &gt;::kOpDelta()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operand___0b84f53cd44b339eccc12067c9f86e11c.html#a4214c67c61ac0a06916ddde36fe67e10',1,'cutlass::gemm::warp::MmaTensorOpMultiplicandTileIterator&lt; Shape_, Operand_, Element_, cutlass::layout::ColumnMajorTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element_))&gt;, InstructionShape_, OpDelta_, 32, PartitionsK_ &gt;::kOpDelta()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operand___039819fb3ccd43786d556c2c9669508ef.html#a9debc539c23e32863ce5ba1ca39226bb',1,'cutlass::gemm::warp::MmaTensorOpMultiplicandTileIterator&lt; Shape_, Operand_, Element_, cutlass::layout::RowMajorTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element_))&gt;, InstructionShape_, OpDelta_, 32, PartitionsK_ &gt;::kOpDelta()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operand___0c7d419c589d601ce4eb603be566fea21.html#a702ec66954f8e450236d4db5b59f425a',1,'cutlass::gemm::warp::MmaTensorOpMultiplicandTileIterator&lt; Shape_, Operand_, Element_, cutlass::layout::TensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, InstructionShape_, OpDelta_, 32, PartitionsK_ &gt;::kOpDelta()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operand___0e52ad425e1ee3e68544873f66733237b.html#a4776e877963b81e7a1da91a7b5956034',1,'cutlass::gemm::warp::MmaTensorOpMultiplicandTileIterator&lt; Shape_, Operand_, Element_, cutlass::layout::ColumnMajorTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, InstructionShape_, OpDelta_, 32, PartitionsK_ &gt;::kOpDelta()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operand___0352e0dcab42bc8360606874e00173556.html#a3744dbf27ac3b792738eba665ae381e7',1,'cutlass::gemm::warp::MmaTensorOpMultiplicandTileIterator&lt; Shape_, Operand_, Element_, cutlass::layout::RowMajorTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, InstructionShape_, OpDelta_, 32, PartitionsK_ &gt;::kOpDelta()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaVoltaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operan34be8e21a40af3ebd2dc3dff460dca72.html#a76eaaf0d567aed0ddf1de9eaf7ab059d',1,'cutlass::gemm::warp::MmaVoltaTensorOpMultiplicandTileIterator&lt; Shape_, Operand::kA, Element_, cutlass::layout::VoltaTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;, InstructionShape_, OpDelta_, 32 &gt;::kOpDelta()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaVoltaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operan16c56cdc2dda5eeb996af8ec0242d501.html#a591c22882c64531853e04e9949a6978e',1,'cutlass::gemm::warp::MmaVoltaTensorOpMultiplicandTileIterator&lt; Shape_, Operand::kB, Element_, cutlass::layout::VoltaTensorOpMultiplicandBCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;, InstructionShape_, OpDelta_, 32 &gt;::kOpDelta()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaVoltaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operan0d3248553e52cd61ed8a2b3b12a20343.html#a89960b51dc335744099705e743d3d477',1,'cutlass::gemm::warp::MmaVoltaTensorOpMultiplicandTileIterator&lt; Shape_, Operand::kA, Element_, cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;, InstructionShape_, OpDelta_, 32 &gt;::kOpDelta()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaVoltaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operand734577b7e54a074d143aba59828c2f2.html#a9f5b8306d5a55373e78cd150fa5df31b',1,'cutlass::gemm::warp::MmaVoltaTensorOpMultiplicandTileIterator&lt; Shape_, Operand::kB, Element_, cutlass::layout::RowMajorVoltaTensorOpMultiplicandBCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;, InstructionShape_, OpDelta_, 32 &gt;::kOpDelta()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaVoltaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operana2f40b28f0d2286b84d86f7238d67b52.html#af6dcd64c14f4fa5b849535f9e6732752',1,'cutlass::gemm::warp::MmaVoltaTensorOpMultiplicandTileIterator&lt; Shape_, Operand_, Element_, cutlass::layout::VoltaTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, KBlock &gt;, InstructionShape_, OpDelta_, 32 &gt;::kOpDelta()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaVoltaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operan5a221944f4a0e16ccab77ba684856942.html#a3d48d66b98780f92c75ff1f7eff8a937',1,'cutlass::gemm::warp::MmaVoltaTensorOpMultiplicandTileIterator&lt; Shape_, Operand_, Element_, cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, KBlock &gt;, InstructionShape_, OpDelta_, 32 &gt;::kOpDelta()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaVoltaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operandcc9821c435540895138bc9af495f321.html#a4ac25976530fae30688e647efbeb44b1',1,'cutlass::gemm::warp::MmaVoltaTensorOpMultiplicandTileIterator&lt; Shape_, Operand_, Element_, cutlass::layout::RowMajorVoltaTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, KBlock &gt;, InstructionShape_, OpDelta_, 32 &gt;::kOpDelta()']]],
  ['koperand',['kOperand',['../classcutlass_1_1gemm_1_1warp_1_1MmaSimtTileIterator_3_01Shape___00_01Operand_1_1kA_00_01Element_67ca7e11a38e38f2c51b84767654a90f.html#ad14839aa08de3d25ca170fa6629124d8',1,'cutlass::gemm::warp::MmaSimtTileIterator&lt; Shape_, Operand::kA, Element_, layout::ColumnMajor, Policy_, PartitionsK, PartitionGroupSize &gt;::kOperand()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaSimtTileIterator_3_01Shape___00_01Operand_1_1kB_00_01Element_ea0a4e7ce3cd5d25cabf79383efdf4d9.html#a1e0e7f329b3d12cc2615e5adf376d7dc',1,'cutlass::gemm::warp::MmaSimtTileIterator&lt; Shape_, Operand::kB, Element_, layout::RowMajor, Policy_, PartitionsK, PartitionGroupSize &gt;::kOperand()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaSimtTileIterator_3_01Shape___00_01Operand_1_1kC_00_01Element_4ccafbc821b3a55cd532602442a74031.html#a191cf509b994b3a3441913fe815fcc97',1,'cutlass::gemm::warp::MmaSimtTileIterator&lt; Shape_, Operand::kC, Element_, layout::ColumnMajor, Policy_ &gt;::kOperand()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaSimtTileIterator_3_01Shape___00_01Operand_1_1kC_00_01Element_8f92ea79e85febb67169c4b2d94b1b20.html#a2b8aea664f29ec2545f511d947798bc5',1,'cutlass::gemm::warp::MmaSimtTileIterator&lt; Shape_, Operand::kC, Element_, layout::RowMajor, Policy_ &gt;::kOperand()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaSimtTileIterator_3_01Shape___00_01Operand_1_1kA_00_01Element_f0ce904a9294556f15e1cc9cf7c99a93.html#a3563744b1c3c0abe7d74393e0d4a0a65',1,'cutlass::gemm::warp::MmaSimtTileIterator&lt; Shape_, Operand::kA, Element_, layout::ColumnMajorInterleaved&lt; 4 &gt;, Policy_, PartitionsK, PartitionGroupSize &gt;::kOperand()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaSimtTileIterator_3_01Shape___00_01Operand_1_1kB_00_01Element_ada156b62fcbdce47009c5bf1321c92c.html#a0cd255bee4798a9e079adfa14996650d',1,'cutlass::gemm::warp::MmaSimtTileIterator&lt; Shape_, Operand::kB, Element_, layout::RowMajorInterleaved&lt; 4 &gt;, Policy_, PartitionsK, PartitionGroupSize &gt;::kOperand()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operand___0ed7daaeba1c095e77f68533d4d2c475c.html#a530b17594e536bc6ddba532cf17dbc88',1,'cutlass::gemm::warp::MmaTensorOpMultiplicandTileIterator&lt; Shape_, Operand_, Element_, cutlass::layout::TensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, 64 &gt;, InstructionShape_, OpDelta_, 32, PartitionsK_ &gt;::kOperand()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operand___0b84f53cd44b339eccc12067c9f86e11c.html#a30e53857ae2e6c76b22780441871505c',1,'cutlass::gemm::warp::MmaTensorOpMultiplicandTileIterator&lt; Shape_, Operand_, Element_, cutlass::layout::ColumnMajorTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element_))&gt;, InstructionShape_, OpDelta_, 32, PartitionsK_ &gt;::kOperand()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operand___039819fb3ccd43786d556c2c9669508ef.html#aae1f7b3b5fe4c031fbd323536af102ac',1,'cutlass::gemm::warp::MmaTensorOpMultiplicandTileIterator&lt; Shape_, Operand_, Element_, cutlass::layout::RowMajorTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element_))&gt;, InstructionShape_, OpDelta_, 32, PartitionsK_ &gt;::kOperand()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operand___0c7d419c589d601ce4eb603be566fea21.html#a4a21ce52fa53c0e49926df3bc2ceb68b',1,'cutlass::gemm::warp::MmaTensorOpMultiplicandTileIterator&lt; Shape_, Operand_, Element_, cutlass::layout::TensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, InstructionShape_, OpDelta_, 32, PartitionsK_ &gt;::kOperand()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operand___0e52ad425e1ee3e68544873f66733237b.html#ad50d5b711b65113c24fb443f9a47d090',1,'cutlass::gemm::warp::MmaTensorOpMultiplicandTileIterator&lt; Shape_, Operand_, Element_, cutlass::layout::ColumnMajorTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, InstructionShape_, OpDelta_, 32, PartitionsK_ &gt;::kOperand()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operand___0352e0dcab42bc8360606874e00173556.html#aea3b4653fc086476a28f8b9c7efc8c55',1,'cutlass::gemm::warp::MmaTensorOpMultiplicandTileIterator&lt; Shape_, Operand_, Element_, cutlass::layout::RowMajorTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, InstructionShape_, OpDelta_, 32, PartitionsK_ &gt;::kOperand()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaTensorOpAccumulatorTileIterator_3_01Shape___00_01Element___006c39f57875e0aa9d0ad82c8043ed8b98.html#a431c4d5802a17ee889e35e42ece2e46f',1,'cutlass::gemm::warp::MmaTensorOpAccumulatorTileIterator&lt; Shape_, Element_, cutlass::layout::RowMajor, InstructionShape_, OpDelta_ &gt;::kOperand()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaTensorOpAccumulatorTileIterator_3_01Shape___00_01Element___008f607b871a2b3d854eb4def64712c042.html#ab1d2010f80a463b38f41e0f7317b4976',1,'cutlass::gemm::warp::MmaTensorOpAccumulatorTileIterator&lt; Shape_, Element_, cutlass::layout::ColumnMajor, InstructionShape_, OpDelta_ &gt;::kOperand()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaTensorOpAccumulatorTileIterator_3_01Shape___00_01Element___00027dabdc144edd6276f664ca74088510.html#abe358f8cbcc78d00b121d80ffadff905',1,'cutlass::gemm::warp::MmaTensorOpAccumulatorTileIterator&lt; Shape_, Element_, cutlass::layout::ColumnMajorInterleaved&lt; InterleavedN &gt;, InstructionShape_, OpDelta_ &gt;::kOperand()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaVoltaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operan34be8e21a40af3ebd2dc3dff460dca72.html#a869e330893310d65941af384e1dabdf2',1,'cutlass::gemm::warp::MmaVoltaTensorOpMultiplicandTileIterator&lt; Shape_, Operand::kA, Element_, cutlass::layout::VoltaTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;, InstructionShape_, OpDelta_, 32 &gt;::kOperand()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaVoltaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operan16c56cdc2dda5eeb996af8ec0242d501.html#aa1e8da62cbb533f6ea848faf0ce95efa',1,'cutlass::gemm::warp::MmaVoltaTensorOpMultiplicandTileIterator&lt; Shape_, Operand::kB, Element_, cutlass::layout::VoltaTensorOpMultiplicandBCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;, InstructionShape_, OpDelta_, 32 &gt;::kOperand()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaVoltaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operan0d3248553e52cd61ed8a2b3b12a20343.html#af20092c53d77c819404fbadf12e25cf7',1,'cutlass::gemm::warp::MmaVoltaTensorOpMultiplicandTileIterator&lt; Shape_, Operand::kA, Element_, cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;, InstructionShape_, OpDelta_, 32 &gt;::kOperand()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaVoltaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operand734577b7e54a074d143aba59828c2f2.html#a33ed89a72f7be6c7f382e38e27946e01',1,'cutlass::gemm::warp::MmaVoltaTensorOpMultiplicandTileIterator&lt; Shape_, Operand::kB, Element_, cutlass::layout::RowMajorVoltaTensorOpMultiplicandBCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;, InstructionShape_, OpDelta_, 32 &gt;::kOperand()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaVoltaTensorOpAccumulatorTileIterator.html#a99c386e8d33f3e69a07ff75107a20205',1,'cutlass::gemm::warp::MmaVoltaTensorOpAccumulatorTileIterator::kOperand()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaVoltaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operana2f40b28f0d2286b84d86f7238d67b52.html#a51237d9841c0cb7172ad51743a327bb5',1,'cutlass::gemm::warp::MmaVoltaTensorOpMultiplicandTileIterator&lt; Shape_, Operand_, Element_, cutlass::layout::VoltaTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, KBlock &gt;, InstructionShape_, OpDelta_, 32 &gt;::kOperand()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaVoltaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operan5a221944f4a0e16ccab77ba684856942.html#a25d138b231130ecd7f3c518826730066',1,'cutlass::gemm::warp::MmaVoltaTensorOpMultiplicandTileIterator&lt; Shape_, Operand_, Element_, cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, KBlock &gt;, InstructionShape_, OpDelta_, 32 &gt;::kOperand()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaVoltaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operandcc9821c435540895138bc9af495f321.html#aacd4eb73cc5258154460d46a4a80c1b0',1,'cutlass::gemm::warp::MmaVoltaTensorOpMultiplicandTileIterator&lt; Shape_, Operand_, Element_, cutlass::layout::RowMajorVoltaTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, KBlock &gt;, InstructionShape_, OpDelta_, 32 &gt;::kOperand()']]],
  ['kpaddingm',['kPaddingM',['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmSha8da7a0cfbbe859b701fdd9f2b8566aa7.html#aeeab30cf98a08762b8f2d25db8e3dee2',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 1, 1, 1 &gt;, ElementA_, layout::RowMajor, ElementB_, layout::ColumnMajor, ElementC_, LayoutC_, arch::OpClassSimt, 2, Operator_ &gt;::kPaddingM()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmSha84e9f8afb6a4ca9f5dcd219b182d16e7.html#abfbcaec3b5165493373ee0e72b6cf975',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 1, 1, 1 &gt;, ElementA_, layout::RowMajor, ElementB_, layout::RowMajor, ElementC_, LayoutC_, arch::OpClassSimt, 2, Operator_ &gt;::kPaddingM()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmShaaf312aafe9da92ea9d417bcc12a8e7dc.html#aad05aa4bf8d369467d71a40dbddd9f30',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 1, 1, 4 &gt;, int8_t, layout::RowMajor, int8_t, layout::ColumnMajor, ElementC_, LayoutC_, arch::OpClassSimt, 2, Operator_ &gt;::kPaddingM()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmSha863d4139ccaa713bc4bde32c425f4067.html#ae227984ba1993b8b2def4156128907ea',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 1, 1, 4 &gt;, int8_t, layout::RowMajor, int8_t, layout::RowMajor, ElementC_, LayoutC_, arch::OpClassSimt, 2, Operator_ &gt;::kPaddingM()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmSha2c0d0b7cdb5c4bcb11e83c058eb65345.html#a5ce49840c5cc218cafac8469f7f88332',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 1, 1, 4 &gt;, int8_t, layout::ColumnMajor, int8_t, layout::ColumnMajor, ElementC_, LayoutC_, arch::OpClassSimt, 2, Operator_ &gt;::kPaddingM()']]],
  ['kpaddingn',['kPaddingN',['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmSha8da7a0cfbbe859b701fdd9f2b8566aa7.html#a08b6dcc32e0b9f2390f2663573bdf73b',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 1, 1, 1 &gt;, ElementA_, layout::RowMajor, ElementB_, layout::ColumnMajor, ElementC_, LayoutC_, arch::OpClassSimt, 2, Operator_ &gt;::kPaddingN()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmShab94a11a77dd0565102710907089acee0.html#a196c99c24aa9b108105d0488aaf7e058',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 1, 1, 1 &gt;, ElementA_, layout::ColumnMajor, ElementB_, layout::ColumnMajor, ElementC_, LayoutC_, arch::OpClassSimt, 2, Operator_ &gt;::kPaddingN()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmShaaf312aafe9da92ea9d417bcc12a8e7dc.html#a03e627a13f5a22475df6dbdd6c68d151',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 1, 1, 4 &gt;, int8_t, layout::RowMajor, int8_t, layout::ColumnMajor, ElementC_, LayoutC_, arch::OpClassSimt, 2, Operator_ &gt;::kPaddingN()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmSha863d4139ccaa713bc4bde32c425f4067.html#aa737480b656ac28df15de8ae82a631fa',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 1, 1, 4 &gt;, int8_t, layout::RowMajor, int8_t, layout::RowMajor, ElementC_, LayoutC_, arch::OpClassSimt, 2, Operator_ &gt;::kPaddingN()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmSha2c0d0b7cdb5c4bcb11e83c058eb65345.html#a97f871d9a198b28a18c49ce039039c7e',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 1, 1, 4 &gt;, int8_t, layout::ColumnMajor, int8_t, layout::ColumnMajor, ElementC_, LayoutC_, arch::OpClassSimt, 2, Operator_ &gt;::kPaddingN()']]],
  ['kparallel',['kParallel',['../namespacecutlass_1_1library.html#a5ccf134b261aafdde24f4185cf1ddda6a6fb3551e3657204372d76d2d9b83a3b9',1,'cutlass::library']]],
  ['kparallelserial',['kParallelSerial',['../namespacecutlass_1_1library.html#a5ccf134b261aafdde24f4185cf1ddda6ae7eba952d5752a5287bfa4b6831d44c0',1,'cutlass::library']]],
  ['kpartitionsk',['kPartitionsK',['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultEpilogueComplexTensorOp.html#a6a7770417159c242d8252998aebfa96e',1,'cutlass::epilogue::threadblock::DefaultEpilogueComplexTensorOp::kPartitionsK()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultEpilogueSimt.html#ad7fe696a7ce06d3f9dd61b5dffc826d2',1,'cutlass::epilogue::threadblock::DefaultEpilogueSimt::kPartitionsK()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultEpilogueTensorOp.html#a01fe2b88a4a6146d60030c0f86a97055',1,'cutlass::epilogue::threadblock::DefaultEpilogueTensorOp::kPartitionsK()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultInterleavedEpilogueTensorOp.html#acab53b796ce64e6e39299da15e65e4fb',1,'cutlass::epilogue::threadblock::DefaultInterleavedEpilogueTensorOp::kPartitionsK()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultEpilogueVoltaTensorOp.html#aa2feeeaad92ac23ba841306eb8e5c2ab',1,'cutlass::epilogue::threadblock::DefaultEpilogueVoltaTensorOp::kPartitionsK()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultEpilogueWmmaTensorOp.html#a4a1949f471e74465b1a92d88162747b0',1,'cutlass::epilogue::threadblock::DefaultEpilogueWmmaTensorOp::kPartitionsK()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultThreadMapSimt.html#a24ca41463fa323d6a92f9456d4adbca9',1,'cutlass::epilogue::threadblock::DefaultThreadMapSimt::kPartitionsK()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultThreadMapTensorOp.html#a3aa324b44f2a7d0fce139a301a6a9ccc',1,'cutlass::epilogue::threadblock::DefaultThreadMapTensorOp::kPartitionsK()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultInterleavedThreadMapTensorOp.html#af2f30872992b4836195d5b1ef03b53ae',1,'cutlass::epilogue::threadblock::DefaultInterleavedThreadMapTensorOp::kPartitionsK()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultThreadMapVoltaTensorOp_3_01ThreadblockShape__d58c94abc36b7c5c109b55202c6992e7.html#af5f1c910c0ade6b79dbbe110e9ac2b96',1,'cutlass::epilogue::threadblock::DefaultThreadMapVoltaTensorOp&lt; ThreadblockShape_, WarpShape_, PartitionsK, ElementOutput_, ElementsPerAccess, half_t &gt;::kPartitionsK()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultThreadMapVoltaTensorOp_3_01ThreadblockShape__95db04b7b72e34283958bd7fbf851d16.html#a8cbd8dd46c0afabd3d15756e3feba876',1,'cutlass::epilogue::threadblock::DefaultThreadMapVoltaTensorOp&lt; ThreadblockShape_, WarpShape_, PartitionsK, ElementOutput_, ElementsPerAccess, float &gt;::kPartitionsK()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultThreadMapWmmaTensorOp.html#a4c1f7afac546fd4c50f96cc24ab545cc',1,'cutlass::epilogue::threadblock::DefaultThreadMapWmmaTensorOp::kPartitionsK()'],['../classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#ab08e1b54d71da091b702f28b5741b086',1,'cutlass::epilogue::threadblock::Epilogue::kPartitionsK()'],['../classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html#a980ad531ed3e93a988ccfc9ac341dc4d',1,'cutlass::epilogue::threadblock::EpilogueBase::kPartitionsK()'],['../classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedEpilogue.html#a00e33e8715882b75b2a3cc867833e04c',1,'cutlass::epilogue::threadblock::InterleavedEpilogue::kPartitionsK()'],['../structcutlass_1_1gemm_1_1kernel_1_1DefaultGemm_3_01ElementA_00_01LayoutA_00_01kAlignmentA_00_01E5d78d37a9ae2ec08d7d477d571df036e.html#a2a1ca042219b933bab25404b2753bbe3',1,'cutlass::gemm::kernel::DefaultGemm&lt; ElementA, LayoutA, kAlignmentA, ElementB, LayoutB, kAlignmentB, ElementC, layout::RowMajor, ElementAccumulator, arch::OpClassTensorOp, arch::Sm75, ThreadblockShape, WarpShape, InstructionShape, EpilogueOutputOp, ThreadblockSwizzle, 2, SplitKSerial, Operator &gt;::kPartitionsK()'],['../structcutlass_1_1gemm_1_1kernel_1_1DefaultGemm_3_01ElementA_00_01layout_1_1ColumnMajorInterleave661fe54d13cc2c9153dcdf31e4beaa30.html#af94be2526f82454dbd71d94c9b46b57b',1,'cutlass::gemm::kernel::DefaultGemm&lt; ElementA, layout::ColumnMajorInterleaved&lt; InterleavedK &gt;, kAlignmentA, ElementB, layout::RowMajorInterleaved&lt; InterleavedK &gt;, kAlignmentB, ElementC, layout::ColumnMajorInterleaved&lt; InterleavedK &gt;, int32_t, arch::OpClassTensorOp, arch::Sm75, ThreadblockShape, WarpShape, InstructionShape, EpilogueOutputOp, ThreadblockSwizzle, 2, SplitKSerial, Operator, IsBetaZero &gt;::kPartitionsK()'],['../structcutlass_1_1gemm_1_1kernel_1_1DefaultGemm_3_01ElementA_00_01LayoutA_00_01kAlignmentA_00_01E044b039b2fe402f29b04a9f5feee5342.html#a2ba377a0d4fedf2373b43407c7d67507',1,'cutlass::gemm::kernel::DefaultGemm&lt; ElementA, LayoutA, kAlignmentA, ElementB, LayoutB, kAlignmentB, ElementC, layout::RowMajor, ElementAccumulator, arch::OpClassTensorOp, arch::Sm70, ThreadblockShape, WarpShape, GemmShape&lt; 8, 8, 4 &gt;, EpilogueOutputOp, ThreadblockSwizzle, 2, SplitKSerial, Operator &gt;::kPartitionsK()'],['../structcutlass_1_1gemm_1_1threadblock_1_1MmaPolicy.html#a18c06ff10b52bf2ca67a00e59a6ae842',1,'cutlass::gemm::threadblock::MmaPolicy::kPartitionsK()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaSimtTileIterator_3_01Shape___00_01Operand_1_1kA_00_01Element_f0ce904a9294556f15e1cc9cf7c99a93.html#aecd5b03b172004eb13c07f6f5c72fbce',1,'cutlass::gemm::warp::MmaSimtTileIterator&lt; Shape_, Operand::kA, Element_, layout::ColumnMajorInterleaved&lt; 4 &gt;, Policy_, PartitionsK, PartitionGroupSize &gt;::kPartitionsK()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaSimtTileIterator_3_01Shape___00_01Operand_1_1kB_00_01Element_ada156b62fcbdce47009c5bf1321c92c.html#a9b62bd489b052e7870234c2467f9577f',1,'cutlass::gemm::warp::MmaSimtTileIterator&lt; Shape_, Operand::kB, Element_, layout::RowMajorInterleaved&lt; 4 &gt;, Policy_, PartitionsK, PartitionGroupSize &gt;::kPartitionsK()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaTensorOp.html#a9e73c525ef6835bd6500f714166a7c43',1,'cutlass::gemm::warp::MmaTensorOp::kPartitionsK()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operand___0ed7daaeba1c095e77f68533d4d2c475c.html#a40fe36af3b514a30200609a2e3b31dcd',1,'cutlass::gemm::warp::MmaTensorOpMultiplicandTileIterator&lt; Shape_, Operand_, Element_, cutlass::layout::TensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, 64 &gt;, InstructionShape_, OpDelta_, 32, PartitionsK_ &gt;::kPartitionsK()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operand___0c7d419c589d601ce4eb603be566fea21.html#afb13a3b770bb8bb8ddb3db23a83489ed',1,'cutlass::gemm::warp::MmaTensorOpMultiplicandTileIterator&lt; Shape_, Operand_, Element_, cutlass::layout::TensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, InstructionShape_, OpDelta_, 32, PartitionsK_ &gt;::kPartitionsK()']]],
  ['kpartitionsn',['kPartitionsN',['../classcutlass_1_1gemm_1_1warp_1_1MmaTensorOp.html#ac47c26a237de7990a53df77a4535141b',1,'cutlass::gemm::warp::MmaTensorOp']]],
  ['kpartitionsperstage',['kPartitionsPerStage',['../classcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK.html#a23071cc4a87b6a2f0c3de29a2368e852',1,'cutlass::reduction::kernel::ReduceSplitK']]],
  ['kplanarcomplex',['kPlanarComplex',['../namespacecutlass_1_1library.html#a8a2c782ab9bf9e19f99fdfcaf7f1c182a13087f12cd5017f04f51a50fb7510ef0',1,'cutlass::library']]],
  ['kplanarcomplexbatched',['kPlanarComplexBatched',['../namespacecutlass_1_1library.html#a8a2c782ab9bf9e19f99fdfcaf7f1c182ae4505abd0106c859c2faf73bd8216162',1,'cutlass::library']]],
  ['kpointercount',['kPointerCount',['../structcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element_0a9491607d11be8e1780e79ad711aa42.html#a5e3a4764a2603fb56a92b24d23b251fa',1,'cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::TensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element_))&gt;, AdvanceRank, ThreadMap_, Alignment &gt;::Detail::kPointerCount()'],['../structcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element_3be8b96d170d886f39b6b30acab65e7a.html#af6f2e4a74ec2bd4c258208d53e06f4d0',1,'cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::TensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::Detail::kPointerCount()'],['../structcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_032f88d1be8b209e44a4815c707ba35bb.html#aebfe1bfa9754f0b067617277e710fd2d',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::VoltaTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::Detail::kPointerCount()'],['../structcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_02d305cfb0b55c6fb236a52cf2240651e.html#a04b1949d3cfae2a458bbe2d32e41e645',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::VoltaTensorOpMultiplicandBCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::Detail::kPointerCount()'],['../structcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_0390833403016f5d817416e20828845df.html#a9626831f7a71e2e3e859611fce83c9ab',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::VoltaTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Shape_::kContiguous &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::Detail::kPointerCount()']]],
  ['kpredicatebytecount',['kPredicateByteCount',['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator_3_01Shape___00_01Elemen784a0e9da3f55064c47e5613791f51f7.html#a2c9c4fdac435f606c7217c67cdab2967',1,'cutlass::transform::threadblock::PredicatedTileAccessIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessType_ &gt;::kPredicateByteCount()'],['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator2dThreadTile_3_01Shape__1790abaa54a01f277d75766d5882fec8.html#a8686c3d1ac8bda13f9d80a8278fb772e',1,'cutlass::transform::threadblock::PredicatedTileAccessIterator2dThreadTile&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessType_ &gt;::kPredicateByteCount()']]],
  ['kpredicatecount',['kPredicateCount',['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator_3_01Shape___00_01Elemen784a0e9da3f55064c47e5613791f51f7.html#a8d173557e32ba0a02f117a2f7b7dfdab',1,'cutlass::transform::threadblock::PredicatedTileAccessIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessType_ &gt;']]],
  ['kpredicatemask',['kPredicateMask',['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator_3_01Shape___00_01Elemen784a0e9da3f55064c47e5613791f51f7.html#a62eb694072abe9c9ea6031b22b7a23f6',1,'cutlass::transform::threadblock::PredicatedTileAccessIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessType_ &gt;::kPredicateMask()'],['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator2dThreadTile_3_01Shape__1790abaa54a01f277d75766d5882fec8.html#a31ff3bf4a805faa25b2a59542aaa7ec8',1,'cutlass::transform::threadblock::PredicatedTileAccessIterator2dThreadTile&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessType_ &gt;::kPredicateMask()']]],
  ['kpredicates',['kPredicates',['../structcutlass_1_1PredicateVector.html#afff3a2142d9853606d6ad7c3a459f492',1,'cutlass::PredicateVector']]],
  ['kpredicatesperbyte',['kPredicatesPerByte',['../structcutlass_1_1PredicateVector.html#a1387c4a964f971ed4611d750a09ec0b5',1,'cutlass::PredicateVector::kPredicatesPerByte()'],['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator_3_01Shape___00_01Elemen784a0e9da3f55064c47e5613791f51f7.html#a01095293801fc7e5b64753add7764f99',1,'cutlass::transform::threadblock::PredicatedTileAccessIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessType_ &gt;::kPredicatesPerByte()'],['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator2dThreadTile_3_01Shape__1790abaa54a01f277d75766d5882fec8.html#acfb881f49b718a01d3744abec6c40f73',1,'cutlass::transform::threadblock::PredicatedTileAccessIterator2dThreadTile&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessType_ &gt;::kPredicatesPerByte()']]],
  ['kpredicatesperword',['kPredicatesPerWord',['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator_3_01Shape___00_01Elemen784a0e9da3f55064c47e5613791f51f7.html#a9890399fb03e6a70b7959fe7aa66bdad',1,'cutlass::transform::threadblock::PredicatedTileAccessIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessType_ &gt;::kPredicatesPerWord()'],['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator2dThreadTile_3_01Shape__1790abaa54a01f277d75766d5882fec8.html#af6c9cefdc32af7dfaaeafc02ada88935',1,'cutlass::transform::threadblock::PredicatedTileAccessIterator2dThreadTile&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessType_ &gt;::kPredicatesPerWord()']]],
  ['kpredicatestart',['kPredicateStart',['../structcutlass_1_1PredicateVector.html#acf848dce84c01453ab8a2d00c8d4f86e',1,'cutlass::PredicateVector']]],
  ['kpredicatewordcount',['kPredicateWordCount',['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator_3_01Shape___00_01Elemen784a0e9da3f55064c47e5613791f51f7.html#a3866c5b6fbdd8173c669e6f483885e72',1,'cutlass::transform::threadblock::PredicatedTileAccessIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessType_ &gt;::kPredicateWordCount()'],['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator2dThreadTile_3_01Shape__1790abaa54a01f277d75766d5882fec8.html#a1eb7edb0c0ee1077e0792c4e6549e05a',1,'cutlass::transform::threadblock::PredicatedTileAccessIterator2dThreadTile&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessType_ &gt;::kPredicateWordCount()']]],
  ['krank',['kRank',['../structcutlass_1_1Coord.html#a2b07d7291d175920274c5e3346e5b68b',1,'cutlass::Coord::kRank()'],['../classcutlass_1_1layout_1_1RowMajor.html#a1f8883b913b10d3c3512118c4817c71c',1,'cutlass::layout::RowMajor::kRank()'],['../classcutlass_1_1layout_1_1ColumnMajor.html#a63ce5b9d779f46cebd9b40c39c6d06d4',1,'cutlass::layout::ColumnMajor::kRank()'],['../structcutlass_1_1layout_1_1RowMajorInterleaved.html#af47a55a327b9561f8792413dcf89ac96',1,'cutlass::layout::RowMajorInterleaved::kRank()'],['../structcutlass_1_1layout_1_1ColumnMajorInterleaved.html#aad9b92ee9840d1566cfdbc0a1c62e447',1,'cutlass::layout::ColumnMajorInterleaved::kRank()'],['../structcutlass_1_1layout_1_1ContiguousMatrix.html#ab0539a0f69aa086431c00371536ff89b',1,'cutlass::layout::ContiguousMatrix::kRank()'],['../structcutlass_1_1layout_1_1ColumnMajorBlockLinear.html#a721cb27c7abd4ccdd476cf5c64cfe82d',1,'cutlass::layout::ColumnMajorBlockLinear::kRank()'],['../structcutlass_1_1layout_1_1RowMajorBlockLinear.html#ad52fddc5f2efe0d1bb2468b3f212779b',1,'cutlass::layout::RowMajorBlockLinear::kRank()'],['../structcutlass_1_1layout_1_1GeneralMatrix.html#a7f9c6fefefa5e476ae8ba479c9f31ea4',1,'cutlass::layout::GeneralMatrix::kRank()'],['../classcutlass_1_1layout_1_1PitchLinear.html#ac254460aa0a2e5d64be594cef97a28bc',1,'cutlass::layout::PitchLinear::kRank()'],['../classcutlass_1_1layout_1_1TensorNHWC.html#afa85abf66e9f96d675a038307f8896bc',1,'cutlass::layout::TensorNHWC::kRank()'],['../classcutlass_1_1layout_1_1TensorNCHW.html#a1cf0ae642ae746413398d4996abfba1f',1,'cutlass::layout::TensorNCHW::kRank()'],['../classcutlass_1_1layout_1_1TensorNCxHWx.html#a1c774534c9c250bcf81f3e32e7b00edb',1,'cutlass::layout::TensorNCxHWx::kRank()'],['../classcutlass_1_1layout_1_1TensorCxRSKx.html#aafb84a4ad18832ea95e514bab7e1b884',1,'cutlass::layout::TensorCxRSKx::kRank()'],['../structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#af738c34a6c8abbec8f88f5235014c188',1,'cutlass::layout::VoltaTensorOpMultiplicandCongruous::kRank()'],['../structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#a354c90c7af5bf5f2119ee92378fe0f53',1,'cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandCongruous::kRank()'],['../structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#a177d3d065bfab1153b7782f194f4717c',1,'cutlass::layout::RowMajorVoltaTensorOpMultiplicandCongruous::kRank()'],['../structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#a2539ea13c07ef646d54d7e6f1f33286f',1,'cutlass::layout::VoltaTensorOpMultiplicandBCongruous::kRank()'],['../structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#aff2f8aaa5256aa9a4ca91575c7857766',1,'cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandBCongruous::kRank()'],['../structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#a921af4231b727ed09cbd6f8f03443b6c',1,'cutlass::layout::RowMajorVoltaTensorOpMultiplicandBCongruous::kRank()'],['../structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#aa8d1036369fd26bad635446582a6fd84',1,'cutlass::layout::VoltaTensorOpMultiplicandCrosswise::kRank()'],['../structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#a1ae07ad4be4b0dee528f5f60f69f2c5e',1,'cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandCrosswise::kRank()'],['../structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#aa6f600940d38ea49079c6e411f34d2c1',1,'cutlass::layout::RowMajorVoltaTensorOpMultiplicandCrosswise::kRank()'],['../structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a92637a459d6d39f55aada64522e77dd6',1,'cutlass::layout::TensorOpMultiplicand::kRank()'],['../structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a11d2f9f8444139c934ea1af67fb358ed',1,'cutlass::layout::TensorOpMultiplicandCongruous::kRank()'],['../structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#a037912b3f87ab25000802e091470c348',1,'cutlass::layout::TensorOpMultiplicandCongruous&lt; 32, Crosswise &gt;::kRank()'],['../structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#a9b8184bd2ad7692e14945ab0673331e5',1,'cutlass::layout::ColumnMajorTensorOpMultiplicandCongruous::kRank()'],['../structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#a27065a5c3ac2ad4e798e149c421a33bd',1,'cutlass::layout::RowMajorTensorOpMultiplicandCongruous::kRank()'],['../structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#a6a0c039823054c3ce6b083634e38eb85',1,'cutlass::layout::TensorOpMultiplicandCrosswise::kRank()'],['../structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#a4bc83e4719dfb686928aa85d603ff621',1,'cutlass::layout::ColumnMajorTensorOpMultiplicandCrosswise::kRank()'],['../structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#a5d70f2d7475c0f34dc0d7f2b7ccc3a5c',1,'cutlass::layout::RowMajorTensorOpMultiplicandCrosswise::kRank()'],['../structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#a6006c012512fa5b46e37fef97d9f23cb',1,'cutlass::layout::TensorOpMultiplicandColumnMajorInterleaved::kRank()'],['../structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#aed810950826eeca7eb63ef7bf8c45568',1,'cutlass::layout::TensorOpMultiplicandRowMajorInterleaved::kRank()'],['../classcutlass_1_1layout_1_1PackedVectorLayout.html#a527d1f3f293a962aa814ca69a5194b24',1,'cutlass::layout::PackedVectorLayout::kRank()'],['../classcutlass_1_1IdentityTensorLayout.html#a813d116ff0e45679a2a7960a7c10fd1b',1,'cutlass::IdentityTensorLayout::kRank()'],['../classcutlass_1_1TensorRef.html#a87c5c1c23b67b7182a177ef8d9437edd',1,'cutlass::TensorRef::kRank()'],['../classcutlass_1_1TensorView.html#ab50def50420ed64afefe864d108f3c58',1,'cutlass::TensorView::kRank()'],['../classcutlass_1_1thread_1_1Matrix.html#a20cc1aac0aa311bb952cf0b491d3b185',1,'cutlass::thread::Matrix::kRank()'],['../classcutlass_1_1HostTensor.html#aa3458af41bf057e8bcfd895388a52659',1,'cutlass::HostTensor::kRank()']]],
  ['krealindex',['kRealIndex',['../classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8.html#a33779b0965d1ce6810dc73638b80f19a',1,'cutlass::epilogue::warp::FragmentIteratorComplexTensorOp&lt; WarpShape_, OperatorShape_, OperatorElementC_, OperatorFragmentC_, layout::RowMajor &gt;']]],
  ['kround',['kRound',['../classcutlass_1_1epilogue_1_1thread_1_1Convert.html#a843cb16e03bd7b40a99d8262b87534eb',1,'cutlass::epilogue::thread::Convert::kRound()'],['../classcutlass_1_1epilogue_1_1thread_1_1LinearCombination.html#af9dcae5494355aae29c7800883db7c51',1,'cutlass::epilogue::thread::LinearCombination::kRound()'],['../classcutlass_1_1epilogue_1_1thread_1_1LinearCombinationClamp.html#aa794968396e172aa76dcdcc34c8e6c98',1,'cutlass::epilogue::thread::LinearCombinationClamp::kRound()'],['../classcutlass_1_1epilogue_1_1thread_1_1LinearCombinationRelu.html#a80f6b354f863f10380365994ba0a08ee',1,'cutlass::epilogue::thread::LinearCombinationRelu::kRound()'],['../classcutlass_1_1epilogue_1_1thread_1_1LinearCombinationRelu_3_01ElementOutput___00_01Count_00_01int_00_01float_00_01Round_01_4.html#a367b140f62e78891d5d39c2211ff4905',1,'cutlass::epilogue::thread::LinearCombinationRelu&lt; ElementOutput_, Count, int, float, Round &gt;::kRound()']]],
  ['krow',['kRow',['../structcutlass_1_1epilogue_1_1threadblock_1_1OutputTileShape.html#ae142ab20e32fe2ca5093012591bf6818',1,'cutlass::epilogue::threadblock::OutputTileShape::kRow()'],['../structcutlass_1_1MatrixShape.html#a6e376c7fd5954ab6040fea695ae8a889',1,'cutlass::MatrixShape::kRow()']]],
  ['krowmajor',['kRowMajor',['../namespacecutlass_1_1layout.html#af6b33640063b02d26c261efd25053e6ca1ebc644af759b214a70279505401a0b9',1,'cutlass::layout::kRowMajor()'],['../namespacecutlass.html#af99b012f0e1795ca7dc167b7b109dd19a1ebc644af759b214a70279505401a0b9',1,'cutlass::kRowMajor()'],['../namespacecutlass_1_1library.html#aa863c416529c1fe76555be9760619a30a1ebc644af759b214a70279505401a0b9',1,'cutlass::library::kRowMajor()']]],
  ['krowmajorinterleavedk16',['kRowMajorInterleavedK16',['../namespacecutlass_1_1library.html#aa863c416529c1fe76555be9760619a30a6e6ad573b4b1f3dc3cb13a77c7bc76dd',1,'cutlass::library']]],
  ['krowmajorinterleavedk4',['kRowMajorInterleavedK4',['../namespacecutlass_1_1library.html#aa863c416529c1fe76555be9760619a30a73a0304c98adfd29bc908335637ce82e',1,'cutlass::library']]],
  ['krows',['kRows',['../classcutlass_1_1thread_1_1Matrix.html#a808e73d767cc4e248cfba54a42b0a41d',1,'cutlass::thread::Matrix']]],
  ['krowsperiteration',['kRowsPerIteration',['../structcutlass_1_1epilogue_1_1warp_1_1SimtPolicy_3_01WarpShape___00_01Operator___00_01layout_1_1Rcef1c60e23e997017ae176c92931151d.html#aae132aaa130374b137c885bb6bdac147',1,'cutlass::epilogue::warp::SimtPolicy&lt; WarpShape_, Operator_, layout::RowMajor, MmaSimtPolicy_ &gt;::kRowsPerIteration()'],['../structcutlass_1_1epilogue_1_1warp_1_1TensorOpPolicy_3_01WarpShape_00_01OperatorShape_00_01layout_1_1RowMajor_01_4.html#aa79c89ede52ba69379b93212cae62aee',1,'cutlass::epilogue::warp::TensorOpPolicy&lt; WarpShape, OperatorShape, layout::RowMajor &gt;::kRowsPerIteration()'],['../structcutlass_1_1epilogue_1_1warp_1_1TensorOpPolicy_3_01WarpShape_00_01OperatorShape_00_01layout69549d10c3610d943987eb90e827bc05.html#a688fd87a9fd5f4760eeff8f54de08b0a',1,'cutlass::epilogue::warp::TensorOpPolicy&lt; WarpShape, OperatorShape, layout::ColumnMajorInterleaved&lt; InterleavedK &gt; &gt;::kRowsPerIteration()'],['../structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_1d48185f49e4d066f8e9327bf0856b7f.html#a1fa606c44c994a088ae186bd700de2a8',1,'cutlass::epilogue::warp::VoltaTensorOpPolicy&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, half_t, layout::RowMajor &gt;::kRowsPerIteration()'],['../structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_136ce744d4c1c6e8707f5a9785196194.html#a16272abda78ae3fee0cc136b519e1f75',1,'cutlass::epilogue::warp::VoltaTensorOpPolicy&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, float, layout::RowMajor &gt;::kRowsPerIteration()']]],
  ['krowspermmatile',['kRowsPerMmaTile',['../structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_136ce744d4c1c6e8707f5a9785196194.html#a58d2cf6dfbc615b0e71d9d47a2835265',1,'cutlass::epilogue::warp::VoltaTensorOpPolicy&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, float, layout::RowMajor &gt;']]],
  ['krowsperquad',['kRowsPerQuad',['../structcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gemmffcab2297c8de8d0013602a39c525b78.html#a119c2b2e771aed34ad6b8ac05730e352',1,'cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, half_t, layout::RowMajor &gt;::Detail::kRowsPerQuad()'],['../structcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gemm770cbca45441d295d5d7433e8222a700.html#a3d20f92ee0eefe511c9d43e67677cd13',1,'cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, float, layout::RowMajor &gt;::Detail::kRowsPerQuad()']]],
  ['ks16',['kS16',['../namespacecutlass_1_1library.html#a366ecc865ac5b24cfdfd392199ba8e9eaec6699c93da7f6a97f2e373a000a8c95',1,'cutlass::library']]],
  ['ks32',['kS32',['../namespacecutlass_1_1library.html#a366ecc865ac5b24cfdfd392199ba8e9ea1a5add06fe973533afb668d19b754f7f',1,'cutlass::library']]],
  ['ks4',['kS4',['../namespacecutlass_1_1library.html#a366ecc865ac5b24cfdfd392199ba8e9eac8c65cd2ebe8cb959b1076fa84ca427f',1,'cutlass::library']]],
  ['ks64',['kS64',['../namespacecutlass_1_1library.html#a366ecc865ac5b24cfdfd392199ba8e9ea89ea7ef93f423e322d8ee8ed00731079',1,'cutlass::library']]],
  ['ks8',['kS8',['../namespacecutlass_1_1library.html#a366ecc865ac5b24cfdfd392199ba8e9ea2a2118fa682cf5f6467f9bf206710261',1,'cutlass::library']]],
  ['kserial',['kSerial',['../namespacecutlass_1_1library.html#a5ccf134b261aafdde24f4185cf1ddda6af54983ae8eb79e77ee6be2f8384e1cb1',1,'cutlass::library']]],
  ['kshaperow',['kShapeRow',['../structcutlass_1_1epilogue_1_1threadblock_1_1detail_1_1RowArrangement_3_01Shape_00_01WarpsRemainief28e98b3f284469f271d28aba73de2e.html#ae5d0e7161ce9b83ba28af8ceff7a4d1b',1,'cutlass::epilogue::threadblock::detail::RowArrangement&lt; Shape, WarpsRemaining, ElementsPerAccess, ElementSize, true &gt;::Detail']]],
  ['kshapewidth',['kShapeWidth',['../structcutlass_1_1epilogue_1_1threadblock_1_1detail_1_1RowArrangement_3_01Shape_00_01WarpsRemainief28e98b3f284469f271d28aba73de2e.html#a593e4d35574f56874fc22adb909bb72b',1,'cutlass::epilogue::threadblock::detail::RowArrangement&lt; Shape, WarpsRemaining, ElementsPerAccess, ElementSize, true &gt;::Detail']]],
  ['ksharedmemalignment',['kSharedMemAlignment',['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultEpilogueVoltaTensorOp.html#a712eb6556c46a722bd71a9dc20b5f8c2',1,'cutlass::epilogue::threadblock::DefaultEpilogueVoltaTensorOp']]],
  ['ksigned',['kSigned',['../structcutlass_1_1integer__subbyte.html#ad38de198bfeed16a06a524cb299f38b5',1,'cutlass::integer_subbyte']]],
  ['ksimt',['kSimt',['../namespacecutlass_1_1library.html#a6e7f08a7db0273b3da7cc7ec6188b95eab87fbce17f79757deb0ac8aee64dcecf',1,'cutlass::library']]],
  ['ksizebits',['kSizeBits',['../classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a45932cad6b905c9ab72889c53112d529',1,'cutlass::Array&lt; T, N, false &gt;']]],
  ['ksplitkserial',['kSplitKSerial',['../classcutlass_1_1gemm_1_1device_1_1Gemm.html#af3c79a0271e684d93c3dca5ad230f45f',1,'cutlass::gemm::device::Gemm::kSplitKSerial()'],['../classcutlass_1_1gemm_1_1device_1_1Gemm_3_01ElementA___00_01LayoutA___00_01ElementB___00_01Layout4d0960ae6b1d1bf19e6239dbd002249c.html#a0b609010f97cb53cf4d8f1ecb4bb0b79',1,'cutlass::gemm::device::Gemm&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, AlignmentA, AlignmentB, SplitKSerial, Operator_, IsBetaZero &gt;::kSplitKSerial()'],['../classcutlass_1_1gemm_1_1device_1_1GemmBatched_3_01ElementA___00_01LayoutA___00_01ElementB___00_0c9bb6f4463ab6085e6008b5d5ad6abfd.html#a5a77d26d895197ff5224dac759e05766',1,'cutlass::gemm::device::GemmBatched&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, AlignmentA, AlignmentB, Operator_ &gt;::kSplitKSerial()'],['../classcutlass_1_1gemm_1_1device_1_1GemmComplex.html#a2b4efea0a570af23f2f1950bf0fb1f48',1,'cutlass::gemm::device::GemmComplex::kSplitKSerial()'],['../classcutlass_1_1gemm_1_1device_1_1GemmComplex_3_01ElementA___00_01LayoutA___00_01ElementB___00_07c56401b4df75709ae636675d9980a9a.html#afe14a91a30bea2204d4351591df7b5cc',1,'cutlass::gemm::device::GemmComplex&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, TransformA, TransformB, SplitKSerial &gt;::kSplitKSerial()'],['../structcutlass_1_1gemm_1_1kernel_1_1Gemm.html#a0bd3c75edcf3f56e591e3034cc31bd91',1,'cutlass::gemm::kernel::Gemm::kSplitKSerial()']]],
  ['kstages',['kStages',['../structcutlass_1_1gemm_1_1device_1_1DefaultGemmConfiguration_3_01arch_1_1OpClassSimt_00_01ArchTag286687c5e6abe22d241f789fe344a465.html#abfca75e731cc512898a2ab276ffd5144',1,'cutlass::gemm::device::DefaultGemmConfiguration&lt; arch::OpClassSimt, ArchTag, ElementA, ElementB, ElementC, ElementAccumulator &gt;::kStages()'],['../structcutlass_1_1gemm_1_1device_1_1DefaultGemmConfiguration_3_01arch_1_1OpClassSimt_00_01ArchTag3026e48abb8c905d1cc6d13d669700e4.html#a0c3b821edf5bbabeace6e9aec9884ca8',1,'cutlass::gemm::device::DefaultGemmConfiguration&lt; arch::OpClassSimt, ArchTag, int8_t, int8_t, ElementC, int32_t &gt;::kStages()'],['../structcutlass_1_1gemm_1_1device_1_1DefaultGemmConfiguration_3_01arch_1_1OpClassWmmaTensorOp_00_0884059ecad03bea3e86c4cf722226097.html#a7854425adb13cc0494f1d0aec9cfa10a',1,'cutlass::gemm::device::DefaultGemmConfiguration&lt; arch::OpClassWmmaTensorOp, ArchTag, ElementA, ElementB, ElementC, ElementAccumulator &gt;::kStages()'],['../structcutlass_1_1gemm_1_1device_1_1DefaultGemmConfiguration_3_01arch_1_1OpClassTensorOp_00_01arc567cad318a31d04b70ea615d6321decd.html#aa206c1d0f903a0d1e8305ecac7b5b986',1,'cutlass::gemm::device::DefaultGemmConfiguration&lt; arch::OpClassTensorOp, arch::Sm70, ElementA, ElementB, ElementC, ElementAccumulator &gt;::kStages()'],['../structcutlass_1_1gemm_1_1device_1_1DefaultGemmConfiguration_3_01arch_1_1OpClassTensorOp_00_01arcde61af9be1337dac1fdb210e7e7a6e01.html#a315ef650f3bd070d43aeac4ee2994ab3',1,'cutlass::gemm::device::DefaultGemmConfiguration&lt; arch::OpClassTensorOp, arch::Sm75, ElementA, ElementB, ElementC, ElementAccumulator &gt;::kStages()'],['../structcutlass_1_1gemm_1_1device_1_1DefaultGemmConfiguration_3_01arch_1_1OpClassTensorOp_00_01arc4fada4957d463c80a2831e47f28157c4.html#aaa28acab30dfb9f30e5955b76d8fe795',1,'cutlass::gemm::device::DefaultGemmConfiguration&lt; arch::OpClassTensorOp, arch::Sm75, int8_t, int8_t, ElementC, int32_t &gt;::kStages()'],['../structcutlass_1_1gemm_1_1device_1_1DefaultGemmConfiguration_3_01arch_1_1OpClassTensorOp_00_01arc8ab5fd2693c6a6ec43e447acb07f784c.html#a739b8cd8e4c5fbc81efffd14521d3dc3',1,'cutlass::gemm::device::DefaultGemmConfiguration&lt; arch::OpClassTensorOp, arch::Sm75, int8_t, uint8_t, ElementC, int32_t &gt;::kStages()'],['../structcutlass_1_1gemm_1_1device_1_1DefaultGemmConfiguration_3_01arch_1_1OpClassTensorOp_00_01arcb27bf218007928652d5b803193eab473.html#ad053a45f7561831dff99efe794542ea9',1,'cutlass::gemm::device::DefaultGemmConfiguration&lt; arch::OpClassTensorOp, arch::Sm75, uint8_t, int8_t, ElementC, int32_t &gt;::kStages()'],['../structcutlass_1_1gemm_1_1device_1_1DefaultGemmConfiguration_3_01arch_1_1OpClassTensorOp_00_01arcfea0f3503156e8e3fba6456f0cedafdd.html#a6f47dbfb2bf6c4e89485e4eab02b0310',1,'cutlass::gemm::device::DefaultGemmConfiguration&lt; arch::OpClassTensorOp, arch::Sm75, uint8_t, uint8_t, ElementC, int32_t &gt;::kStages()'],['../structcutlass_1_1gemm_1_1device_1_1DefaultGemmConfiguration_3_01arch_1_1OpClassTensorOp_00_01arc485a4f0b5a7d2d4ab2c1a24da6328048.html#a24208068fee538bcd6feae5c050f535a',1,'cutlass::gemm::device::DefaultGemmConfiguration&lt; arch::OpClassTensorOp, arch::Sm75, int4b_t, int4b_t, ElementC, int32_t &gt;::kStages()'],['../structcutlass_1_1gemm_1_1device_1_1DefaultGemmConfiguration_3_01arch_1_1OpClassTensorOp_00_01arc8e2604a56dff3a7595da9ee0604ae55e.html#a0792c0d1d0c2b68348cb1a5c6f8c2ff5',1,'cutlass::gemm::device::DefaultGemmConfiguration&lt; arch::OpClassTensorOp, arch::Sm75, int4b_t, uint4b_t, ElementC, int32_t &gt;::kStages()'],['../structcutlass_1_1gemm_1_1device_1_1DefaultGemmConfiguration_3_01arch_1_1OpClassTensorOp_00_01arcffcf31256aed23d4d8d0eab627bc0cad.html#acd22964dfbd6db348fb268aefddeff2f',1,'cutlass::gemm::device::DefaultGemmConfiguration&lt; arch::OpClassTensorOp, arch::Sm75, uint4b_t, int4b_t, ElementC, int32_t &gt;::kStages()'],['../structcutlass_1_1gemm_1_1device_1_1DefaultGemmConfiguration_3_01arch_1_1OpClassTensorOp_00_01arcb2e258b7bd321c633dd65d3ebcf6414a.html#a127eb3f67589a25d3e6c158e91bcca83',1,'cutlass::gemm::device::DefaultGemmConfiguration&lt; arch::OpClassTensorOp, arch::Sm75, uint4b_t, uint4b_t, ElementC, int32_t &gt;::kStages()'],['../classcutlass_1_1gemm_1_1device_1_1Gemm.html#a5cd83a90660626f6c446d45881f0fc22',1,'cutlass::gemm::device::Gemm::kStages()'],['../classcutlass_1_1gemm_1_1device_1_1Gemm_3_01ElementA___00_01LayoutA___00_01ElementB___00_01Layout4d0960ae6b1d1bf19e6239dbd002249c.html#a5de0cfa9c3831daebbdc8326c239dd33',1,'cutlass::gemm::device::Gemm&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, AlignmentA, AlignmentB, SplitKSerial, Operator_, IsBetaZero &gt;::kStages()'],['../classcutlass_1_1gemm_1_1device_1_1GemmBatched.html#a73837bda9ba209e546f6d996ede1afad',1,'cutlass::gemm::device::GemmBatched::kStages()'],['../classcutlass_1_1gemm_1_1device_1_1GemmBatched_3_01ElementA___00_01LayoutA___00_01ElementB___00_0c9bb6f4463ab6085e6008b5d5ad6abfd.html#ab7f6a87909a3c2d45de71367a0d6eae3',1,'cutlass::gemm::device::GemmBatched&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, AlignmentA, AlignmentB, Operator_ &gt;::kStages()'],['../classcutlass_1_1gemm_1_1device_1_1GemmComplex.html#a5de13507f93aaf28f02ca42f64a1e343',1,'cutlass::gemm::device::GemmComplex::kStages()'],['../classcutlass_1_1gemm_1_1device_1_1GemmComplex_3_01ElementA___00_01LayoutA___00_01ElementB___00_07c56401b4df75709ae636675d9980a9a.html#a689afffc991cf4e6aab7d6e4f5fe4d46',1,'cutlass::gemm::device::GemmComplex&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, TransformA, TransformB, SplitKSerial &gt;::kStages()'],['../classcutlass_1_1gemm_1_1device_1_1GemmSplitKParallel.html#af1f647942f7734bbf01e473118f2512c',1,'cutlass::gemm::device::GemmSplitKParallel::kStages()'],['../classcutlass_1_1gemm_1_1device_1_1GemmSplitKParallel_3_01ElementA___00_01LayoutA___00_01ElementBbe7c1f7154ad5b5bf9d4d28301e2b457.html#a87961d33bf1aff6a6cbb5a6bc022493e',1,'cutlass::gemm::device::GemmSplitKParallel&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ConvertScaledOp_, ReductionOp_, ThreadblockSwizzle_, Stages, kAlignmentA, kAlignmentB, Operator_ &gt;::kStages()'],['../classcutlass_1_1gemm_1_1threadblock_1_1MmaBase.html#ad1267d78374c170d9addd137310d2d9a',1,'cutlass::gemm::threadblock::MmaBase::kStages()']]],
  ['kstorageelements',['kStorageElements',['../classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#aff4b09f36ec3f8861ebd2db338a298b2',1,'cutlass::Array&lt; T, N, true &gt;::kStorageElements()'],['../classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#afbe4f574d87e61bf18ac5b9f5a6ea8aa',1,'cutlass::Array&lt; T, N, false &gt;::kStorageElements()']]],
  ['kstrided',['kStrided',['../structcutlass_1_1layout_1_1PitchLinearShape.html#aaec0afa0c26627d951d2d2b98a3e5601',1,'cutlass::layout::PitchLinearShape']]],
  ['kstriderank',['kStrideRank',['../classcutlass_1_1layout_1_1RowMajor.html#a4bf573d3c00092f8117ff960b82e2f12',1,'cutlass::layout::RowMajor::kStrideRank()'],['../classcutlass_1_1layout_1_1ColumnMajor.html#a5cf5e74b50b844269a07a93d7c018e7c',1,'cutlass::layout::ColumnMajor::kStrideRank()'],['../structcutlass_1_1layout_1_1RowMajorInterleaved.html#a2fc89f8f53f6e27e99e66c47b844a3d4',1,'cutlass::layout::RowMajorInterleaved::kStrideRank()'],['../structcutlass_1_1layout_1_1ColumnMajorInterleaved.html#a14ae8fdc44e1a349ffc1ec0ee217f75c',1,'cutlass::layout::ColumnMajorInterleaved::kStrideRank()'],['../structcutlass_1_1layout_1_1ContiguousMatrix.html#a6e67c889ff67dffb7fe78f316ad62838',1,'cutlass::layout::ContiguousMatrix::kStrideRank()'],['../structcutlass_1_1layout_1_1ColumnMajorBlockLinear.html#a0732c2b94df9a0c10453cba940684cca',1,'cutlass::layout::ColumnMajorBlockLinear::kStrideRank()'],['../structcutlass_1_1layout_1_1RowMajorBlockLinear.html#a7f37ee2ad80dd5c0d75cf12fd4da74b8',1,'cutlass::layout::RowMajorBlockLinear::kStrideRank()'],['../structcutlass_1_1layout_1_1GeneralMatrix.html#a11fd27e0e5293d0256e9d413cea3605b',1,'cutlass::layout::GeneralMatrix::kStrideRank()'],['../classcutlass_1_1layout_1_1PitchLinear.html#a31af18ae99034c00a551bab1c04c118d',1,'cutlass::layout::PitchLinear::kStrideRank()'],['../classcutlass_1_1layout_1_1TensorNHWC.html#aa03e4031f1ea30e534df11ffdc0cf59c',1,'cutlass::layout::TensorNHWC::kStrideRank()'],['../classcutlass_1_1layout_1_1TensorNCHW.html#a7ee93120511aa8735105c4417739e815',1,'cutlass::layout::TensorNCHW::kStrideRank()'],['../classcutlass_1_1layout_1_1TensorNCxHWx.html#aa17170d312069cb54025c207b318e76a',1,'cutlass::layout::TensorNCxHWx::kStrideRank()'],['../classcutlass_1_1layout_1_1TensorCxRSKx.html#af93c5ebb2e713da1e75bded321e6bd0e',1,'cutlass::layout::TensorCxRSKx::kStrideRank()'],['../structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a80aa98e0c1d3ee7f7d5bb92fc0b2efac',1,'cutlass::layout::VoltaTensorOpMultiplicandCongruous::kStrideRank()'],['../structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#a1125039c8a7a3f636806282a53a91414',1,'cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandCongruous::kStrideRank()'],['../structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#aa740540df69532336fc076787bb76f3a',1,'cutlass::layout::RowMajorVoltaTensorOpMultiplicandCongruous::kStrideRank()'],['../structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#a04bc03f9b125eb07ca99289c49520845',1,'cutlass::layout::VoltaTensorOpMultiplicandBCongruous::kStrideRank()'],['../structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#a54c1bf44ae979e72af5946bb76d19f22',1,'cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandBCongruous::kStrideRank()'],['../structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#af4a8ae7ae7264c025a223d49bc296a56',1,'cutlass::layout::RowMajorVoltaTensorOpMultiplicandBCongruous::kStrideRank()'],['../structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#ad519840cf6c7dc8a88e06133c15ac598',1,'cutlass::layout::VoltaTensorOpMultiplicandCrosswise::kStrideRank()'],['../structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#a5749b6879a23f4f94e383dd4e84f4208',1,'cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandCrosswise::kStrideRank()'],['../structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#afbff2da11814cff114a68a12206cf1fb',1,'cutlass::layout::RowMajorVoltaTensorOpMultiplicandCrosswise::kStrideRank()'],['../structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a8c6e0632e7a38d03612a73a93f5f6a1f',1,'cutlass::layout::TensorOpMultiplicand::kStrideRank()'],['../structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous.html#a616795a0ef2b4b1d38d666972ec91a65',1,'cutlass::layout::TensorOpMultiplicandCongruous::kStrideRank()'],['../structcutlass_1_1layout_1_1TensorOpMultiplicandCongruous_3_0132_00_01Crosswise_01_4.html#a942daade1d67cf023fa8e7a98055d7e6',1,'cutlass::layout::TensorOpMultiplicandCongruous&lt; 32, Crosswise &gt;::kStrideRank()'],['../structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCongruous.html#a45984a21ce8ab362d62f390328072bed',1,'cutlass::layout::ColumnMajorTensorOpMultiplicandCongruous::kStrideRank()'],['../structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#ac44e7b11d94d2ee3fa0cc7ec2bc1423a',1,'cutlass::layout::RowMajorTensorOpMultiplicandCongruous::kStrideRank()'],['../structcutlass_1_1layout_1_1TensorOpMultiplicandCrosswise.html#ad165f5ccdcd9a82efec86e830c03ba00',1,'cutlass::layout::TensorOpMultiplicandCrosswise::kStrideRank()'],['../structcutlass_1_1layout_1_1ColumnMajorTensorOpMultiplicandCrosswise.html#ad618ebb0d23785d18e56bc3b7663fe4f',1,'cutlass::layout::ColumnMajorTensorOpMultiplicandCrosswise::kStrideRank()'],['../structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#ad38dac4a905cd2074f8c33da3b46954e',1,'cutlass::layout::RowMajorTensorOpMultiplicandCrosswise::kStrideRank()'],['../structcutlass_1_1layout_1_1TensorOpMultiplicandColumnMajorInterleaved.html#a202a4b11a76201686a1f33b458ceb715',1,'cutlass::layout::TensorOpMultiplicandColumnMajorInterleaved::kStrideRank()'],['../structcutlass_1_1layout_1_1TensorOpMultiplicandRowMajorInterleaved.html#a72aed2ba4379167f238e139801ce0816',1,'cutlass::layout::TensorOpMultiplicandRowMajorInterleaved::kStrideRank()'],['../classcutlass_1_1layout_1_1PackedVectorLayout.html#a98a28ffe4b3919f3c011ddf86fec6e69',1,'cutlass::layout::PackedVectorLayout::kStrideRank()'],['../classcutlass_1_1IdentityTensorLayout.html#ad0ed9dc11a6284d25ea588d6933d2965',1,'cutlass::IdentityTensorLayout::kStrideRank()']]],
  ['ksuccess',['kSuccess',['../namespacecutlass.html#ac5a88c5840a28a9e0206b9cc7812a18da8c632159fa131f09d04f94e3cbcd8782',1,'cutlass']]],
  ['ktargetaccessrows',['kTargetAccessRows',['../structcutlass_1_1epilogue_1_1threadblock_1_1detail_1_1RowArrangement_3_01Shape_00_01WarpsRemainief28e98b3f284469f271d28aba73de2e.html#ad7ffcad57652220b5fdc5bac13522392',1,'cutlass::epilogue::threadblock::detail::RowArrangement&lt; Shape, WarpsRemaining, ElementsPerAccess, ElementSize, true &gt;::Detail']]],
  ['ktargetmemoryaccesswidth',['kTargetMemoryAccessWidth',['../structcutlass_1_1epilogue_1_1threadblock_1_1detail_1_1RowArrangement_3_01Shape_00_01WarpsRemainief28e98b3f284469f271d28aba73de2e.html#a94165daf04a1d10f8ab04b4c07f1c357',1,'cutlass::epilogue::threadblock::detail::RowArrangement&lt; Shape, WarpsRemaining, ElementsPerAccess, ElementSize, true &gt;::Detail']]],
  ['ktensornchw',['kTensorNCHW',['../namespacecutlass_1_1library.html#aa863c416529c1fe76555be9760619a30aac41b2fb91b88429aecccaf31b717c47',1,'cutlass::library']]],
  ['ktensornhwc',['kTensorNHWC',['../namespacecutlass_1_1library.html#aa863c416529c1fe76555be9760619a30a90f415c1bef303b8a2401e2b0d7ad89c',1,'cutlass::library']]],
  ['ktensorop',['kTensorOp',['../namespacecutlass_1_1library.html#a6e7f08a7db0273b3da7cc7ec6188b95eae2f60ea81883c37bdf80acf94ac9fdef',1,'cutlass::library']]],
  ['ktensoroprows',['kTensorOpRows',['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultThreadMapTensorOp_1_1Detail.html#ad91cb340f6a3be93ef40d9891659e29e',1,'cutlass::epilogue::threadblock::DefaultThreadMapTensorOp::Detail::kTensorOpRows()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultInterleavedThreadMapTensorOp_1_1Detail.html#a7bd08f838f7c65891fb1e8600c0b5dcf',1,'cutlass::epilogue::threadblock::DefaultInterleavedThreadMapTensorOp::Detail::kTensorOpRows()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultThreadMapVoltaTensorOp_3_01ThreadblockShape__4433cc988100e98097a748d2670fb0fc.html#abefdea13828326ce59cf4768483e3a96',1,'cutlass::epilogue::threadblock::DefaultThreadMapVoltaTensorOp&lt; ThreadblockShape_, WarpShape_, PartitionsK, ElementOutput_, ElementsPerAccess, half_t &gt;::Detail::kTensorOpRows()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultThreadMapVoltaTensorOp_3_01ThreadblockShape__52116c60c62f0fd520071558e42b814f.html#aa9e5d51def1064ea8087ffddfafdfb26',1,'cutlass::epilogue::threadblock::DefaultThreadMapVoltaTensorOp&lt; ThreadblockShape_, WarpShape_, PartitionsK, ElementOutput_, ElementsPerAccess, float &gt;::Detail::kTensorOpRows()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultThreadMapWmmaTensorOp_1_1Detail.html#a6b72af5c7f39ff1eeefa0eb28a9b4a6f',1,'cutlass::epilogue::threadblock::DefaultThreadMapWmmaTensorOp::Detail::kTensorOpRows()']]],
  ['kthreadblockaccesses',['kThreadblockAccesses',['../classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#ac0bb3a94eeabdeffbb2a2ae50e44fc2c',1,'cutlass::epilogue::EpilogueWorkspace']]],
  ['kthreadcount',['kThreadCount',['../structcutlass_1_1gemm_1_1kernel_1_1Gemm.html#a63a3564945b339b5e3f0a0ab127874f7',1,'cutlass::gemm::kernel::Gemm::kThreadCount()'],['../structcutlass_1_1gemm_1_1kernel_1_1GemmBatched.html#aafddaefa35d27c76a89be8e692005615',1,'cutlass::gemm::kernel::GemmBatched::kThreadCount()'],['../structcutlass_1_1gemm_1_1kernel_1_1GemmSplitKParallel.html#ac376c54b10cfd6a40aa38263a0c5f7ea',1,'cutlass::gemm::kernel::GemmSplitKParallel::kThreadCount()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaComplexTensorOp_3_01Shape___00_01complex_3_01RealElementA_01_146441010dad1f40eb51b6dae3ded216.html#a3c6b8cbbbbd6c7c79da2c81ef8bc19f1',1,'cutlass::gemm::warp::MmaComplexTensorOp&lt; Shape_, complex&lt; RealElementA &gt;, LayoutA_, complex&lt; RealElementB &gt;, LayoutB_, complex&lt; RealElementC &gt;, LayoutC_, Policy_, TransformA, TransformB, Enable &gt;::kThreadCount()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaTensorOp.html#a51960b3e3cdde83bc7321b1607b78302',1,'cutlass::gemm::warp::MmaTensorOp::kThreadCount()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaVoltaTensorOp.html#ab263f394262ccf84ce0da72b2f7c2c9d',1,'cutlass::gemm::warp::MmaVoltaTensorOp::kThreadCount()']]],
  ['kthreads',['kThreads',['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultThreadMapSimt_1_1Detail.html#a62b7574dcf62d9c0409d986848f3844c',1,'cutlass::epilogue::threadblock::DefaultThreadMapSimt::Detail::kThreads()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultThreadMapTensorOp_1_1Detail.html#a095825584c05b51f25a99e08788fa578',1,'cutlass::epilogue::threadblock::DefaultThreadMapTensorOp::Detail::kThreads()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultInterleavedThreadMapTensorOp_1_1Detail.html#a0ce47b2fb9ee85298b77992b10804075',1,'cutlass::epilogue::threadblock::DefaultInterleavedThreadMapTensorOp::Detail::kThreads()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultThreadMapVoltaTensorOp_3_01ThreadblockShape__4433cc988100e98097a748d2670fb0fc.html#a00cbee272e5c9b0e4e11065054f60fa0',1,'cutlass::epilogue::threadblock::DefaultThreadMapVoltaTensorOp&lt; ThreadblockShape_, WarpShape_, PartitionsK, ElementOutput_, ElementsPerAccess, half_t &gt;::Detail::kThreads()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultThreadMapVoltaTensorOp_3_01ThreadblockShape__52116c60c62f0fd520071558e42b814f.html#a8a9e67935629344f7e700701101c2223',1,'cutlass::epilogue::threadblock::DefaultThreadMapVoltaTensorOp&lt; ThreadblockShape_, WarpShape_, PartitionsK, ElementOutput_, ElementsPerAccess, float &gt;::Detail::kThreads()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultThreadMapWmmaTensorOp_1_1Detail.html#afc1accef02f5b85ec0a3c8840b35c698',1,'cutlass::epilogue::threadblock::DefaultThreadMapWmmaTensorOp::Detail::kThreads()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1OutputTileThreadMap.html#afd483897f0a554f44c61c73caf4c2922',1,'cutlass::epilogue::threadblock::OutputTileThreadMap::kThreads()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1OutputTileOptimalThreadMap.html#a7e0c8849bc8e879c97ff9ded76df471f',1,'cutlass::epilogue::threadblock::OutputTileOptimalThreadMap::kThreads()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1OutputTileOptimalThreadMap_1_1CompactedThreadMap.html#a3994e5c6ad1cdc533e13c974af50267f',1,'cutlass::epilogue::threadblock::OutputTileOptimalThreadMap::CompactedThreadMap::kThreads()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedOutputTileThreadMap.html#aa714495a585f75f0f77b0308f0a8c4df',1,'cutlass::epilogue::threadblock::InterleavedOutputTileThreadMap::kThreads()'],['../classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#ad6b1a44f14127ee55f70b8d2c043c67e',1,'cutlass::epilogue::threadblock::PredicatedTileIterator::kThreads()'],['../classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#a2db384dfc9540ad07a8e4b21ff0799cb',1,'cutlass::epilogue::threadblock::InterleavedPredicatedTileIterator::kThreads()'],['../classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#a5adc9c1897c49421e3b31fc3f8d34cbc',1,'cutlass::epilogue::threadblock::SharedLoadIterator::kThreads()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmShafafd5c61db86cbfe90863578ddd11092.html#a42736c96be2a22cf7530a574a2464920',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 1, 1, 1 &gt;, ElementA_, layout::ColumnMajor, ElementB_, layout::RowMajor, ElementC_, LayoutC_, arch::OpClassSimt, 2, Operator_ &gt;::kThreads()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmSha8da7a0cfbbe859b701fdd9f2b8566aa7.html#a2144315db649fe20ba9d42f88aee510b',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 1, 1, 1 &gt;, ElementA_, layout::RowMajor, ElementB_, layout::ColumnMajor, ElementC_, LayoutC_, arch::OpClassSimt, 2, Operator_ &gt;::kThreads()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmSha84e9f8afb6a4ca9f5dcd219b182d16e7.html#a08416c46082a019721f129ac878ecce3',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 1, 1, 1 &gt;, ElementA_, layout::RowMajor, ElementB_, layout::RowMajor, ElementC_, LayoutC_, arch::OpClassSimt, 2, Operator_ &gt;::kThreads()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmShab94a11a77dd0565102710907089acee0.html#ab11fdc63d433ecd0be3224709b6552a8',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 1, 1, 1 &gt;, ElementA_, layout::ColumnMajor, ElementB_, layout::ColumnMajor, ElementC_, LayoutC_, arch::OpClassSimt, 2, Operator_ &gt;::kThreads()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmSha34a52cc7b2942e8c290f0032b6779b52.html#a6b92b85ebec9e381f2fc5eaac9f61567',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 1, 1, 4 &gt;, int8_t, layout::ColumnMajor, int8_t, layout::RowMajor, ElementC_, LayoutC_, arch::OpClassSimt, 2, Operator_ &gt;::kThreads()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmShaaf312aafe9da92ea9d417bcc12a8e7dc.html#a75d55d083ad9c4a59222366b802bfc41',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 1, 1, 4 &gt;, int8_t, layout::RowMajor, int8_t, layout::ColumnMajor, ElementC_, LayoutC_, arch::OpClassSimt, 2, Operator_ &gt;::kThreads()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmSha863d4139ccaa713bc4bde32c425f4067.html#ac8eeafe5d1ca4cd04ab498e34a93a650',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 1, 1, 4 &gt;, int8_t, layout::RowMajor, int8_t, layout::RowMajor, ElementC_, LayoutC_, arch::OpClassSimt, 2, Operator_ &gt;::kThreads()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmSha2c0d0b7cdb5c4bcb11e83c058eb65345.html#a39e0c6bd96f947e0db63fae49ba267bf',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 1, 1, 4 &gt;, int8_t, layout::ColumnMajor, int8_t, layout::ColumnMajor, ElementC_, LayoutC_, arch::OpClassSimt, 2, Operator_ &gt;::kThreads()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmSha46446d1e3871e31d2e728f710d78c8c1.html#a261cad07837e8cf96d430f726686e074',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 1, 1, 1 &gt;, ElementA_, layout::ColumnMajor, ElementB_, layout::RowMajor, ElementC_, LayoutC_, arch::OpClassSimt, 2, Operator_, &gt;::kThreads()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmSha69bef08ea63dd930f99d9788105873dd.html#ada2536fb91814110dec128a60c529e5d',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 8, 8, 4 &gt;, ElementA_, layout::ColumnMajor, ElementB_, layout::RowMajor, ElementC_, LayoutC_, arch::OpClassTensorOp, 2, Operator_ &gt;::kThreads()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmSha3adf608332a8c9ee7014fced0da8a9ca.html#a5a36b9d6c0eb1cad3eb4250787963e40',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 8, 8, 4 &gt;, ElementA_, layout::RowMajor, ElementB_, layout::ColumnMajor, ElementC_, LayoutC_, arch::OpClassTensorOp, 2, Operator_ &gt;::kThreads()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmShab7edfba3cdf43a07e3c4d719d87565a4.html#a3c93c1d4b5da2410503dca9be6996d73',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 8, 8, 4 &gt;, ElementA_, layout::RowMajor, ElementB_, layout::RowMajor, ElementC_, LayoutC_, arch::OpClassTensorOp, 2, Operator_ &gt;::kThreads()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmShaf03a122202ad10acdc96f280106d678b.html#addc01deee5de88583fed9363c9b32ea2',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 8, 8, 4 &gt;, ElementA_, layout::ColumnMajor, ElementB_, layout::ColumnMajor, ElementC_, LayoutC_, arch::OpClassTensorOp, 2, Operator_ &gt;::kThreads()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01Instrucf60fe02fcdd80d28b7fd419133465dcc.html#a351cb54d1c2627f877505217fb8ccecc',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, InstructionShape_, ElementA_, layout::ColumnMajor, ElementB_, layout::RowMajor, ElementC_, LayoutC_, arch::OpClassTensorOp, 2, Operator_ &gt;::kThreads()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01Instruc24092ddc01fc83dabb7db4c14880fe60.html#ad8bc0240bfbe8337000c63c28abb7b81',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, InstructionShape_, ElementA_, layout::RowMajor, ElementB_, layout::ColumnMajor, ElementC_, LayoutC_, arch::OpClassTensorOp, 2, Operator_ &gt;::kThreads()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01Instruc4fee9f2965b8468bfb42b94a74527d22.html#a99be5bc8d448625f37921ea5ac8ab3ea',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, InstructionShape_, ElementA_, layout::RowMajor, ElementB_, layout::RowMajor, ElementC_, LayoutC_, arch::OpClassTensorOp, 2, Operator_ &gt;::kThreads()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01Instruc803d38bc1e4618c07c47f54c87ae2678.html#addb90df26f78357e5eaa9c1e1ad28323',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, InstructionShape_, ElementA_, layout::ColumnMajor, ElementB_, layout::ColumnMajor, ElementC_, LayoutC_, arch::OpClassTensorOp, 2, Operator_ &gt;::kThreads()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01Instruc2bf00737f4ad0a9da9a8be6d3e66c152.html#a7ad9e79133b9efbce514917f0295fd21',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, InstructionShape_, ElementA_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt;, ElementB_, layout::RowMajorInterleaved&lt; InterleavedK &gt;, ElementC_, LayoutC_, arch::OpClassTensorOp, 2, Operator_, AccumulatorsInRowMajor &gt;::kThreads()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operand___0ed7daaeba1c095e77f68533d4d2c475c.html#aa50dce798dbb20fde71ba74627659cca',1,'cutlass::gemm::warp::MmaTensorOpMultiplicandTileIterator&lt; Shape_, Operand_, Element_, cutlass::layout::TensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, 64 &gt;, InstructionShape_, OpDelta_, 32, PartitionsK_ &gt;::kThreads()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operand___0b84f53cd44b339eccc12067c9f86e11c.html#a4b4676149ae60900c59b5aee37f32356',1,'cutlass::gemm::warp::MmaTensorOpMultiplicandTileIterator&lt; Shape_, Operand_, Element_, cutlass::layout::ColumnMajorTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element_))&gt;, InstructionShape_, OpDelta_, 32, PartitionsK_ &gt;::kThreads()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operand___039819fb3ccd43786d556c2c9669508ef.html#a4e504f50ad5b8106d8f26abdf2c77eed',1,'cutlass::gemm::warp::MmaTensorOpMultiplicandTileIterator&lt; Shape_, Operand_, Element_, cutlass::layout::RowMajorTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element_))&gt;, InstructionShape_, OpDelta_, 32, PartitionsK_ &gt;::kThreads()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operand___0c7d419c589d601ce4eb603be566fea21.html#a9c503e45168ab6d5f770520e29d61c98',1,'cutlass::gemm::warp::MmaTensorOpMultiplicandTileIterator&lt; Shape_, Operand_, Element_, cutlass::layout::TensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, InstructionShape_, OpDelta_, 32, PartitionsK_ &gt;::kThreads()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operand___0e52ad425e1ee3e68544873f66733237b.html#a63155c7e31f604d81cc80d8dd43aa4c1',1,'cutlass::gemm::warp::MmaTensorOpMultiplicandTileIterator&lt; Shape_, Operand_, Element_, cutlass::layout::ColumnMajorTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, InstructionShape_, OpDelta_, 32, PartitionsK_ &gt;::kThreads()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operand___0352e0dcab42bc8360606874e00173556.html#a0b9caf53db250e8dc24316df2c710b53',1,'cutlass::gemm::warp::MmaTensorOpMultiplicandTileIterator&lt; Shape_, Operand_, Element_, cutlass::layout::RowMajorTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, InstructionShape_, OpDelta_, 32, PartitionsK_ &gt;::kThreads()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaTensorOpAccumulatorTileIterator_3_01Shape___00_01Element___006c39f57875e0aa9d0ad82c8043ed8b98.html#a4d099bfdea5ea89de5a9ed29c5707b39',1,'cutlass::gemm::warp::MmaTensorOpAccumulatorTileIterator&lt; Shape_, Element_, cutlass::layout::RowMajor, InstructionShape_, OpDelta_ &gt;::kThreads()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaTensorOpAccumulatorTileIterator_3_01Shape___00_01Element___008f607b871a2b3d854eb4def64712c042.html#a7ef8f84a0bfed1a42a0a0f3b01814ecf',1,'cutlass::gemm::warp::MmaTensorOpAccumulatorTileIterator&lt; Shape_, Element_, cutlass::layout::ColumnMajor, InstructionShape_, OpDelta_ &gt;::kThreads()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaTensorOpAccumulatorTileIterator_3_01Shape___00_01Element___00027dabdc144edd6276f664ca74088510.html#ace40436c7aaeee19e49ac1ab917300a6',1,'cutlass::gemm::warp::MmaTensorOpAccumulatorTileIterator&lt; Shape_, Element_, cutlass::layout::ColumnMajorInterleaved&lt; InterleavedN &gt;, InstructionShape_, OpDelta_ &gt;::kThreads()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaVoltaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operan34be8e21a40af3ebd2dc3dff460dca72.html#ad1dee4fcf12a8a7549583e9503d7dd66',1,'cutlass::gemm::warp::MmaVoltaTensorOpMultiplicandTileIterator&lt; Shape_, Operand::kA, Element_, cutlass::layout::VoltaTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;, InstructionShape_, OpDelta_, 32 &gt;::kThreads()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaVoltaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operan16c56cdc2dda5eeb996af8ec0242d501.html#aa045a737f4dfd8ad8e1bc520df9e9b8b',1,'cutlass::gemm::warp::MmaVoltaTensorOpMultiplicandTileIterator&lt; Shape_, Operand::kB, Element_, cutlass::layout::VoltaTensorOpMultiplicandBCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;, InstructionShape_, OpDelta_, 32 &gt;::kThreads()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaVoltaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operan0d3248553e52cd61ed8a2b3b12a20343.html#a8ac5d3c8a717fd10286d5d87b1142179',1,'cutlass::gemm::warp::MmaVoltaTensorOpMultiplicandTileIterator&lt; Shape_, Operand::kA, Element_, cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;, InstructionShape_, OpDelta_, 32 &gt;::kThreads()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaVoltaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operand734577b7e54a074d143aba59828c2f2.html#aa8ad5855f256043b1b232d0149f4ae66',1,'cutlass::gemm::warp::MmaVoltaTensorOpMultiplicandTileIterator&lt; Shape_, Operand::kB, Element_, cutlass::layout::RowMajorVoltaTensorOpMultiplicandBCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;, InstructionShape_, OpDelta_, 32 &gt;::kThreads()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaVoltaTensorOpAccumulatorTileIterator.html#a89382d6c3b4754de25687f52bc4e988d',1,'cutlass::gemm::warp::MmaVoltaTensorOpAccumulatorTileIterator::kThreads()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaVoltaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operana2f40b28f0d2286b84d86f7238d67b52.html#a7157fa9ef858a8a232bb9ee50c0b1c47',1,'cutlass::gemm::warp::MmaVoltaTensorOpMultiplicandTileIterator&lt; Shape_, Operand_, Element_, cutlass::layout::VoltaTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, KBlock &gt;, InstructionShape_, OpDelta_, 32 &gt;::kThreads()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaVoltaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operan5a221944f4a0e16ccab77ba684856942.html#a4c646f3044367f33d7478f2ef1dcdb5e',1,'cutlass::gemm::warp::MmaVoltaTensorOpMultiplicandTileIterator&lt; Shape_, Operand_, Element_, cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, KBlock &gt;, InstructionShape_, OpDelta_, 32 &gt;::kThreads()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaVoltaTensorOpMultiplicandTileIterator_3_01Shape___00_01Operandcc9821c435540895138bc9af495f321.html#a2b7f29ccb0a5eace9b4628d6a1b04c13',1,'cutlass::gemm::warp::MmaVoltaTensorOpMultiplicandTileIterator&lt; Shape_, Operand_, Element_, cutlass::layout::RowMajorVoltaTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, KBlock &gt;, InstructionShape_, OpDelta_, 32 &gt;::kThreads()'],['../structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ab35edeae5cd8767bd376fad5f6680e25',1,'cutlass::reduction::BatchedReductionTraits::kThreads()'],['../structcutlass_1_1transform_1_1PitchLinearStripminedThreadMap.html#ad4089315fc07133a415189b088a6d04c',1,'cutlass::transform::PitchLinearStripminedThreadMap::kThreads()'],['../structcutlass_1_1transform_1_1PitchLinearTilePolicyStripminedThreadContiguous.html#aa71c8006d116e8826691230a392ed47f',1,'cutlass::transform::PitchLinearTilePolicyStripminedThreadContiguous::kThreads()'],['../structcutlass_1_1transform_1_1PitchLinearTilePolicyStripminedThreadStrided.html#ac5da64e378f9b4688857b489b9055f54',1,'cutlass::transform::PitchLinearTilePolicyStripminedThreadStrided::kThreads()'],['../structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html#ab0d2e70502e80e8f85ef7b83cade093c',1,'cutlass::transform::PitchLinearWarpRakedThreadMap::kThreads()'],['../structcutlass_1_1transform_1_1TransposePitchLinearThreadMap.html#acec5d21914f954b2dc12d8db0273e9ee',1,'cutlass::transform::TransposePitchLinearThreadMap::kThreads()'],['../structcutlass_1_1transform_1_1TransposePitchLinearThreadMapSimt.html#a36ea7cfc98a39e28986bc77bca38c08f',1,'cutlass::transform::TransposePitchLinearThreadMapSimt::kThreads()'],['../structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap.html#a58fa53118303ef62f8b7557616606f8f',1,'cutlass::transform::PitchLinearWarpStripedThreadMap::kThreads()'],['../structcutlass_1_1transform_1_1PitchLinear2DThreadTileStripminedThreadMap_3_01Shape___00_01Thread0082c3467229b12cc9dd996283ee7160.html#ad53906dfb9b5d6be913fd60f3b865751',1,'cutlass::transform::PitchLinear2DThreadTileStripminedThreadMap&lt; Shape_, Threads, cutlass::layout::PitchLinearShape&lt; 4, 4 &gt; &gt;::kThreads()'],['../structcutlass_1_1transform_1_1TransposePitchLinearThreadMap2DThreadTile.html#a5245459784b7b74a864cf676dfe94228',1,'cutlass::transform::TransposePitchLinearThreadMap2DThreadTile::kThreads()']]],
  ['kthreadspern',['kThreadsPerN',['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultGemvCore.html#a8cd2bd9fc021ca52c5b441561b530ad2',1,'cutlass::gemm::threadblock::DefaultGemvCore']]],
  ['ktile',['kTile',['../structcutlass_1_1epilogue_1_1threadblock_1_1OutputTileShape.html#a7ae1bbb19dcd98860f73711ced104082',1,'cutlass::epilogue::threadblock::OutputTileShape::kTile()'],['../structcutlass_1_1gemm_1_1threadblock_1_1GemmIdentityThreadblockSwizzle.html#a697f38679bc882bf8a3c183cd078b370',1,'cutlass::gemm::threadblock::GemmIdentityThreadblockSwizzle::kTile()']]],
  ['ktileshapecontiguous',['kTileShapeContiguous',['../structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a1a8ae93363b37a31b2b0ec0df2ea00d2',1,'cutlass::layout::TensorOpMultiplicand']]],
  ['ktileshapestride',['kTileShapeStride',['../structcutlass_1_1layout_1_1TensorOpMultiplicand.html#a56de60deb0ebd18347ad4fa33e10ffce',1,'cutlass::layout::TensorOpMultiplicand']]],
  ['ktransforma',['kTransformA',['../classcutlass_1_1gemm_1_1device_1_1GemmComplex.html#a0999f0c0348dfb06c4ad815933cc00f2',1,'cutlass::gemm::device::GemmComplex::kTransformA()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaComplexTensorOp_3_01Shape___00_01complex_3_01RealElementA_01_146441010dad1f40eb51b6dae3ded216.html#a4609afc5d188db147aed72b6ffaff7cf',1,'cutlass::gemm::warp::MmaComplexTensorOp&lt; Shape_, complex&lt; RealElementA &gt;, LayoutA_, complex&lt; RealElementB &gt;, LayoutB_, complex&lt; RealElementC &gt;, LayoutC_, Policy_, TransformA, TransformB, Enable &gt;::kTransformA()']]],
  ['ktransformb',['kTransformB',['../classcutlass_1_1gemm_1_1device_1_1GemmComplex.html#a6dd46e941525efdc1ae8f7183aa890d7',1,'cutlass::gemm::device::GemmComplex::kTransformB()'],['../classcutlass_1_1gemm_1_1warp_1_1MmaComplexTensorOp_3_01Shape___00_01complex_3_01RealElementA_01_146441010dad1f40eb51b6dae3ded216.html#abf449b3aef0c8ec33f15fecbce4cd691',1,'cutlass::gemm::warp::MmaComplexTensorOp&lt; Shape_, complex&lt; RealElementA &gt;, LayoutA_, complex&lt; RealElementB &gt;, LayoutB_, complex&lt; RealElementC &gt;, LayoutC_, Policy_, TransformA, TransformB, Enable &gt;::kTransformB()']]],
  ['ktranspose',['kTranspose',['../namespacecutlass.html#ab7e605b25da48d89f98764c12d50b467a0b1273062aff94c51d1d2788bc9052d2',1,'cutlass']]],
  ['ku16',['kU16',['../namespacecutlass_1_1library.html#a366ecc865ac5b24cfdfd392199ba8e9ea9070b7eed719acaf36119d4a2835a230',1,'cutlass::library']]],
  ['ku32',['kU32',['../namespacecutlass_1_1library.html#a366ecc865ac5b24cfdfd392199ba8e9ea4f2abe781a12702f9fbaea79fb6ba042',1,'cutlass::library']]],
  ['ku4',['kU4',['../namespacecutlass_1_1library.html#a366ecc865ac5b24cfdfd392199ba8e9ea054401f7563f9941ed01d9edc32463a9',1,'cutlass::library']]],
  ['ku64',['kU64',['../namespacecutlass_1_1library.html#a366ecc865ac5b24cfdfd392199ba8e9ea6256cfbcd370939b8454d9eca86b9d2a',1,'cutlass::library']]],
  ['ku8',['kU8',['../namespacecutlass_1_1library.html#a366ecc865ac5b24cfdfd392199ba8e9eaeda0b712c7690a5c8921fa54baad333e',1,'cutlass::library']]],
  ['kunknown',['kUnknown',['../namespacecutlass_1_1library.html#aa863c416529c1fe76555be9760619a30a25c2dc47991b3df171ed5192bcf70390',1,'cutlass::library::kUnknown()'],['../namespacecutlass_1_1library.html#a366ecc865ac5b24cfdfd392199ba8e9ea25c2dc47991b3df171ed5192bcf70390',1,'cutlass::library::kUnknown()']]],
  ['kvalue',['kValue',['../structcutlass_1_1Min.html#a97e6dd3ff6fb5404e8a6e6109f73f429',1,'cutlass::Min::kValue()'],['../structcutlass_1_1Max.html#a6ed8be7ed855eea8f8d08921f7b5d763',1,'cutlass::Max::kValue()']]],
  ['kvoid',['kVoid',['../namespacecutlass_1_1library.html#a366ecc865ac5b24cfdfd392199ba8e9eabd383557eb16fc23863cf3fc70b77ab3',1,'cutlass::library']]],
  ['kw',['kW',['../structcutlass_1_1Tensor4DCoord.html#a01e55a99e690d697ca62cfaeb4bcde9f',1,'cutlass::Tensor4DCoord']]],
  ['kwarpaccesses',['kWarpAccesses',['../classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#a808f585be1d17e849faf0fc58ab8bd8c',1,'cutlass::epilogue::EpilogueWorkspace']]],
  ['kwarpcount',['kWarpCount',['../classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#a0bdff651696fabadbb71e6471eb3711b',1,'cutlass::epilogue::EpilogueWorkspace::kWarpCount()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1OutputTileOptimalThreadMap.html#a8f0b8beac69612a91011ec4485100f07',1,'cutlass::epilogue::threadblock::OutputTileOptimalThreadMap::kWarpCount()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedOutputTileThreadMap.html#a50a795ed6bcbaa460268ed5b209387f8',1,'cutlass::epilogue::threadblock::InterleavedOutputTileThreadMap::kWarpCount()'],['../structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap_1_1Detail.html#a80d262dcae35245106c627941bf2e2c5',1,'cutlass::transform::PitchLinearWarpRakedThreadMap::Detail::kWarpCount()'],['../structcutlass_1_1transform_1_1TransposePitchLinearThreadMap_1_1Detail.html#a36142112d9239d4b4b90bbe41038dc15',1,'cutlass::transform::TransposePitchLinearThreadMap::Detail::kWarpCount()'],['../structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap_1_1Detail.html#af6622be9e439ee44ee1f05b8ea696450',1,'cutlass::transform::PitchLinearWarpStripedThreadMap::Detail::kWarpCount()']]],
  ['kwarpgemmiterations',['kWarpGemmIterations',['../classcutlass_1_1gemm_1_1threadblock_1_1MmaBase.html#a02f496a0fd1df929d8d4db9fea19160d',1,'cutlass::gemm::threadblock::MmaBase']]],
  ['kwarppartitionscluster',['kWarpPartitionsCluster',['../structcutlass_1_1epilogue_1_1threadblock_1_1OutputTileOptimalThreadMap_1_1Detail.html#a8fe2e2aa661b949632e3b6cc4aacc143',1,'cutlass::epilogue::threadblock::OutputTileOptimalThreadMap::Detail']]],
  ['kwarppartitionscolumn',['kWarpPartitionsColumn',['../structcutlass_1_1epilogue_1_1threadblock_1_1detail_1_1RowArrangement_3_01Shape_00_01WarpsRemaini91159e6f7e123d881e3ec45101fa4f81.html#a45b42667bcc5459b94d43f3cc94611e0',1,'cutlass::epilogue::threadblock::detail::RowArrangement&lt; Shape, WarpsRemaining, ElementsPerAccess, ElementSize, false &gt;::kWarpPartitionsColumn()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1detail_1_1RowArrangement_3_01Shape_00_01WarpsRemaini6d8790249bf12cac580da73bb37eb791.html#acafcaed1f898976599ea448f384031d1',1,'cutlass::epilogue::threadblock::detail::RowArrangement&lt; Shape, WarpsRemaining, ElementsPerAccess, ElementSize, true &gt;::kWarpPartitionsColumn()']]],
  ['kwarppartitionsgroup',['kWarpPartitionsGroup',['../structcutlass_1_1epilogue_1_1threadblock_1_1OutputTileOptimalThreadMap_1_1Detail.html#a895f4d4f0f84c0ea2dcb63118d2a6d22',1,'cutlass::epilogue::threadblock::OutputTileOptimalThreadMap::Detail']]],
  ['kwarppartitionsrow',['kWarpPartitionsRow',['../structcutlass_1_1epilogue_1_1threadblock_1_1detail_1_1RowArrangement_3_01Shape_00_01WarpsRemaini91159e6f7e123d881e3ec45101fa4f81.html#a7f80314e46fbbf601a80fc4473088a5e',1,'cutlass::epilogue::threadblock::detail::RowArrangement&lt; Shape, WarpsRemaining, ElementsPerAccess, ElementSize, false &gt;::kWarpPartitionsRow()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1detail_1_1RowArrangement_3_01Shape_00_01WarpsRemaini6d8790249bf12cac580da73bb37eb791.html#a8aceb748d35c4e8d891d5c162c83f890',1,'cutlass::epilogue::threadblock::detail::RowArrangement&lt; Shape, WarpsRemaining, ElementsPerAccess, ElementSize, true &gt;::kWarpPartitionsRow()']]],
  ['kwarpscontiguous',['kWarpsContiguous',['../structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap_1_1Detail.html#a05bcdb4e8fb32bfe30ac862ffc5de279',1,'cutlass::transform::PitchLinearWarpRakedThreadMap::Detail::kWarpsContiguous()'],['../structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap_1_1Detail.html#a363250282dfea2d4c3c42500b6fb90e9',1,'cutlass::transform::PitchLinearWarpStripedThreadMap::Detail::kWarpsContiguous()']]],
  ['kwarpsize',['kWarpSize',['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultThreadMapSimt_1_1Detail.html#afa4da0a64b7775eb859a62ce13b43beb',1,'cutlass::epilogue::threadblock::DefaultThreadMapSimt::Detail::kWarpSize()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultThreadMapTensorOp_1_1Detail.html#aac8533f95b338ac9c5be143107893021',1,'cutlass::epilogue::threadblock::DefaultThreadMapTensorOp::Detail::kWarpSize()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultInterleavedThreadMapTensorOp_1_1Detail.html#afd48cd1baa1e6809c72f5adf1700b261',1,'cutlass::epilogue::threadblock::DefaultInterleavedThreadMapTensorOp::Detail::kWarpSize()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultThreadMapVoltaTensorOp_3_01ThreadblockShape__4433cc988100e98097a748d2670fb0fc.html#a698b74b827c49a4cbc341fccd15f22eb',1,'cutlass::epilogue::threadblock::DefaultThreadMapVoltaTensorOp&lt; ThreadblockShape_, WarpShape_, PartitionsK, ElementOutput_, ElementsPerAccess, half_t &gt;::Detail::kWarpSize()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultThreadMapVoltaTensorOp_3_01ThreadblockShape__52116c60c62f0fd520071558e42b814f.html#afb104fd1885be18e82c1b27ff144a861',1,'cutlass::epilogue::threadblock::DefaultThreadMapVoltaTensorOp&lt; ThreadblockShape_, WarpShape_, PartitionsK, ElementOutput_, ElementsPerAccess, float &gt;::Detail::kWarpSize()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1DefaultThreadMapWmmaTensorOp_1_1Detail.html#aea2433024dde002e594b2e6da968ff88',1,'cutlass::epilogue::threadblock::DefaultThreadMapWmmaTensorOp::Detail::kWarpSize()'],['../classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#aa4f276efe10e1cbba8c994ef06313114',1,'cutlass::epilogue::EpilogueWorkspace::kWarpSize()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1detail_1_1RowArrangement_3_01Shape_00_01WarpsRemaini91159e6f7e123d881e3ec45101fa4f81.html#a85d3a7ffcb258c130923c963cce25634',1,'cutlass::epilogue::threadblock::detail::RowArrangement&lt; Shape, WarpsRemaining, ElementsPerAccess, ElementSize, false &gt;::kWarpSize()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1detail_1_1RowArrangement_3_01Shape_00_01WarpsRemaini6d8790249bf12cac580da73bb37eb791.html#aa081af4cb649f104a3da2aca01516aa8',1,'cutlass::epilogue::threadblock::detail::RowArrangement&lt; Shape, WarpsRemaining, ElementsPerAccess, ElementSize, true &gt;::kWarpSize()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1OutputTileOptimalThreadMap.html#a6a557c0acc8eb0b309fbd6faab228c71',1,'cutlass::epilogue::threadblock::OutputTileOptimalThreadMap::kWarpSize()'],['../structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedOutputTileThreadMap.html#a5099e0c92ca9207571b0ce1374560b60',1,'cutlass::epilogue::threadblock::InterleavedOutputTileThreadMap::kWarpSize()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmShafafd5c61db86cbfe90863578ddd11092.html#a2d8ac452ce8d9fa0fe416fd61bff3db4',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 1, 1, 1 &gt;, ElementA_, layout::ColumnMajor, ElementB_, layout::RowMajor, ElementC_, LayoutC_, arch::OpClassSimt, 2, Operator_ &gt;::kWarpSize()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmSha8da7a0cfbbe859b701fdd9f2b8566aa7.html#a0523fef53729c2e2431783b546f558fb',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 1, 1, 1 &gt;, ElementA_, layout::RowMajor, ElementB_, layout::ColumnMajor, ElementC_, LayoutC_, arch::OpClassSimt, 2, Operator_ &gt;::kWarpSize()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmSha84e9f8afb6a4ca9f5dcd219b182d16e7.html#a018a1dcfae2420f8df12803b60c12563',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 1, 1, 1 &gt;, ElementA_, layout::RowMajor, ElementB_, layout::RowMajor, ElementC_, LayoutC_, arch::OpClassSimt, 2, Operator_ &gt;::kWarpSize()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmShab94a11a77dd0565102710907089acee0.html#a4fd0ab760457ff4a67e15a33f98d9ee8',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 1, 1, 1 &gt;, ElementA_, layout::ColumnMajor, ElementB_, layout::ColumnMajor, ElementC_, LayoutC_, arch::OpClassSimt, 2, Operator_ &gt;::kWarpSize()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmSha34a52cc7b2942e8c290f0032b6779b52.html#aabe489e7c3115f2083ec0c64d2459c08',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 1, 1, 4 &gt;, int8_t, layout::ColumnMajor, int8_t, layout::RowMajor, ElementC_, LayoutC_, arch::OpClassSimt, 2, Operator_ &gt;::kWarpSize()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmShaaf312aafe9da92ea9d417bcc12a8e7dc.html#ae290ffe7237c2aac3b87528927df4289',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 1, 1, 4 &gt;, int8_t, layout::RowMajor, int8_t, layout::ColumnMajor, ElementC_, LayoutC_, arch::OpClassSimt, 2, Operator_ &gt;::kWarpSize()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmSha863d4139ccaa713bc4bde32c425f4067.html#a40bc7311a7bce3a1720a12c8442335c5',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 1, 1, 4 &gt;, int8_t, layout::RowMajor, int8_t, layout::RowMajor, ElementC_, LayoutC_, arch::OpClassSimt, 2, Operator_ &gt;::kWarpSize()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmSha2c0d0b7cdb5c4bcb11e83c058eb65345.html#acfa25506e0870c822e4c5c452b7091f1',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 1, 1, 4 &gt;, int8_t, layout::ColumnMajor, int8_t, layout::ColumnMajor, ElementC_, LayoutC_, arch::OpClassSimt, 2, Operator_ &gt;::kWarpSize()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmSha46446d1e3871e31d2e728f710d78c8c1.html#a8be674d077362d122aa0c790f20eae30',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 1, 1, 1 &gt;, ElementA_, layout::ColumnMajor, ElementB_, layout::RowMajor, ElementC_, LayoutC_, arch::OpClassSimt, 2, Operator_, &gt;::kWarpSize()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmSha69bef08ea63dd930f99d9788105873dd.html#ad860546d4ad48fde1742eb335ba8219a',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 8, 8, 4 &gt;, ElementA_, layout::ColumnMajor, ElementB_, layout::RowMajor, ElementC_, LayoutC_, arch::OpClassTensorOp, 2, Operator_ &gt;::kWarpSize()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmSha3adf608332a8c9ee7014fced0da8a9ca.html#adeafed9a222b36a846015d88ca7122fa',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 8, 8, 4 &gt;, ElementA_, layout::RowMajor, ElementB_, layout::ColumnMajor, ElementC_, LayoutC_, arch::OpClassTensorOp, 2, Operator_ &gt;::kWarpSize()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmShab7edfba3cdf43a07e3c4d719d87565a4.html#a70c83b9995472d3c649e69467c54fb32',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 8, 8, 4 &gt;, ElementA_, layout::RowMajor, ElementB_, layout::RowMajor, ElementC_, LayoutC_, arch::OpClassTensorOp, 2, Operator_ &gt;::kWarpSize()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01GemmShaf03a122202ad10acdc96f280106d678b.html#a6df3bc70f299c8db385ddfb4c1dfe3ba',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, GemmShape&lt; 8, 8, 4 &gt;, ElementA_, layout::ColumnMajor, ElementB_, layout::ColumnMajor, ElementC_, LayoutC_, arch::OpClassTensorOp, 2, Operator_ &gt;::kWarpSize()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01Instrucf60fe02fcdd80d28b7fd419133465dcc.html#a9960b0e2078e17d0de7f7f71d51f8a1d',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, InstructionShape_, ElementA_, layout::ColumnMajor, ElementB_, layout::RowMajor, ElementC_, LayoutC_, arch::OpClassTensorOp, 2, Operator_ &gt;::kWarpSize()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01Instruc24092ddc01fc83dabb7db4c14880fe60.html#a9ab570118363b968b85681119f9b6652',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, InstructionShape_, ElementA_, layout::RowMajor, ElementB_, layout::ColumnMajor, ElementC_, LayoutC_, arch::OpClassTensorOp, 2, Operator_ &gt;::kWarpSize()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01Instruc4fee9f2965b8468bfb42b94a74527d22.html#a9c391abcd4e99ec3fbc52c520c6e2345',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, InstructionShape_, ElementA_, layout::RowMajor, ElementB_, layout::RowMajor, ElementC_, LayoutC_, arch::OpClassTensorOp, 2, Operator_ &gt;::kWarpSize()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01Instruc803d38bc1e4618c07c47f54c87ae2678.html#a61577a7cf5b66f6203f02016ace7d967',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, InstructionShape_, ElementA_, layout::ColumnMajor, ElementB_, layout::ColumnMajor, ElementC_, LayoutC_, arch::OpClassTensorOp, 2, Operator_ &gt;::kWarpSize()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01Instruc2bf00737f4ad0a9da9a8be6d3e66c152.html#a8616b34382d3ce6aff8adf06e31a60bd',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, InstructionShape_, ElementA_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt;, ElementB_, layout::RowMajorInterleaved&lt; InterleavedK &gt;, ElementC_, LayoutC_, arch::OpClassTensorOp, 2, Operator_, AccumulatorsInRowMajor &gt;::kWarpSize()'],['../structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap_1_1Detail.html#a4a9356476175000839fe4dd4d642311a',1,'cutlass::transform::PitchLinearWarpRakedThreadMap::Detail::kWarpSize()'],['../structcutlass_1_1transform_1_1TransposePitchLinearThreadMap_1_1Detail.html#a591ed25004b30b5f4da39161800801d1',1,'cutlass::transform::TransposePitchLinearThreadMap::Detail::kWarpSize()'],['../structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap_1_1Detail.html#adc91199cda80ad6d1b41681b319fc3d6',1,'cutlass::transform::PitchLinearWarpStripedThreadMap::Detail::kWarpSize()']]],
  ['kwarpsremainingforgroups',['kWarpsRemainingForGroups',['../structcutlass_1_1epilogue_1_1threadblock_1_1OutputTileOptimalThreadMap_1_1Detail.html#a67b57acab9109111106e0a7f7fe2f2f3',1,'cutlass::epilogue::threadblock::OutputTileOptimalThreadMap::Detail']]],
  ['kwarpsremainingforrows',['kWarpsRemainingForRows',['../structcutlass_1_1epilogue_1_1threadblock_1_1OutputTileOptimalThreadMap_1_1Detail.html#a6ae11f3ee8b2ac3949ca6dc6d0a59f38',1,'cutlass::epilogue::threadblock::OutputTileOptimalThreadMap::Detail']]],
  ['kwarpsstrided',['kWarpsStrided',['../structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap_1_1Detail.html#a74a1c31d82466657f6d2eb4126ed212f',1,'cutlass::transform::PitchLinearWarpRakedThreadMap::Detail::kWarpsStrided()'],['../structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap_1_1Detail.html#a20bc67607c17bf1fc367a6b3a3bb6ad3',1,'cutlass::transform::PitchLinearWarpStripedThreadMap::Detail::kWarpsStrided()']]],
  ['kwarpthreadarrangementcontiguous',['kWarpThreadArrangementContiguous',['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01Instruc2bf00737f4ad0a9da9a8be6d3e66c152.html#a7ba518da65e2272517d722f6b16eb663',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, InstructionShape_, ElementA_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt;, ElementB_, layout::RowMajorInterleaved&lt; InterleavedK &gt;, ElementC_, LayoutC_, arch::OpClassTensorOp, 2, Operator_, AccumulatorsInRowMajor &gt;']]],
  ['kwarpthreadarrangementcontiguousa',['kWarpThreadArrangementContiguousA',['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01Instruc24092ddc01fc83dabb7db4c14880fe60.html#a718693d6764b43f92b2dbfeff0fe68e9',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, InstructionShape_, ElementA_, layout::RowMajor, ElementB_, layout::ColumnMajor, ElementC_, LayoutC_, arch::OpClassTensorOp, 2, Operator_ &gt;::kWarpThreadArrangementContiguousA()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01Instruc4fee9f2965b8468bfb42b94a74527d22.html#a19c688a3ba14ea54805dbd6e25597296',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, InstructionShape_, ElementA_, layout::RowMajor, ElementB_, layout::RowMajor, ElementC_, LayoutC_, arch::OpClassTensorOp, 2, Operator_ &gt;::kWarpThreadArrangementContiguousA()']]],
  ['kwarpthreadarrangementcontiguousb',['kWarpThreadArrangementContiguousB',['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01Instruc24092ddc01fc83dabb7db4c14880fe60.html#aaa93260e53c7832eea25ac3c6edc5abf',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, InstructionShape_, ElementA_, layout::RowMajor, ElementB_, layout::ColumnMajor, ElementC_, LayoutC_, arch::OpClassTensorOp, 2, Operator_ &gt;::kWarpThreadArrangementContiguousB()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01Instruc803d38bc1e4618c07c47f54c87ae2678.html#a9ebeb24892fa3cbe55258bbd2bbd86e4',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, InstructionShape_, ElementA_, layout::ColumnMajor, ElementB_, layout::ColumnMajor, ElementC_, LayoutC_, arch::OpClassTensorOp, 2, Operator_ &gt;::kWarpThreadArrangementContiguousB()']]],
  ['kwarpthreadarrangementstrided',['kWarpThreadArrangementStrided',['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01Instruc2bf00737f4ad0a9da9a8be6d3e66c152.html#a758333ccd6685c8e01505f524eb3ecc0',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, InstructionShape_, ElementA_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt;, ElementB_, layout::RowMajorInterleaved&lt; InterleavedK &gt;, ElementC_, LayoutC_, arch::OpClassTensorOp, 2, Operator_, AccumulatorsInRowMajor &gt;']]],
  ['kwarpthreadarrangementstrideda',['kWarpThreadArrangementStridedA',['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01Instruc24092ddc01fc83dabb7db4c14880fe60.html#afad87172b76657dd1f7f8c06264f5a88',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, InstructionShape_, ElementA_, layout::RowMajor, ElementB_, layout::ColumnMajor, ElementC_, LayoutC_, arch::OpClassTensorOp, 2, Operator_ &gt;::kWarpThreadArrangementStridedA()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01Instruc4fee9f2965b8468bfb42b94a74527d22.html#a31a5b1882f75ea9b5998b9b59c3330a9',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, InstructionShape_, ElementA_, layout::RowMajor, ElementB_, layout::RowMajor, ElementC_, LayoutC_, arch::OpClassTensorOp, 2, Operator_ &gt;::kWarpThreadArrangementStridedA()']]],
  ['kwarpthreadarrangementstridedb',['kWarpThreadArrangementStridedB',['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01Instruc24092ddc01fc83dabb7db4c14880fe60.html#a5ee880deee900fcb04657755cb728d76',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, InstructionShape_, ElementA_, layout::RowMajor, ElementB_, layout::ColumnMajor, ElementC_, LayoutC_, arch::OpClassTensorOp, 2, Operator_ &gt;::kWarpThreadArrangementStridedB()'],['../structcutlass_1_1gemm_1_1threadblock_1_1DefaultMmaCore_3_01Shape___00_01WarpShape___00_01Instruc803d38bc1e4618c07c47f54c87ae2678.html#aa9adc3c58ee84a698e4445600911ae4f',1,'cutlass::gemm::threadblock::DefaultMmaCore&lt; Shape_, WarpShape_, InstructionShape_, ElementA_, layout::ColumnMajor, ElementB_, layout::ColumnMajor, ElementC_, LayoutC_, arch::OpClassTensorOp, 2, Operator_ &gt;::kWarpThreadArrangementStridedB()']]],
  ['kwmmatensorop',['kWmmaTensorOp',['../namespacecutlass_1_1library.html#a6e7f08a7db0273b3da7cc7ec6188b95ea4b5b8d90d96d36bc4c7ade9e31975a61',1,'cutlass::library']]],
  ['kwordcount',['kWordCount',['../structcutlass_1_1PredicateVector.html#a734bbfaf3829f73ef0b44fa7db4ccd42',1,'cutlass::PredicateVector']]]
];
