var searchData=
[
  ['random',['random',['../structcutlass_1_1reference_1_1device_1_1detail_1_1TensorFillRandomGaussianFunc_1_1Params.html#aa21637b631a16fc4e2860e27e422765b',1,'cutlass::reference::device::detail::TensorFillRandomGaussianFunc::Params::random()'],['../structcutlass_1_1reference_1_1device_1_1detail_1_1TensorFillRandomGaussianFunc.html#ab7a72f16421d8bc596af374af0fae1d1',1,'cutlass::reference::device::detail::TensorFillRandomGaussianFunc::random()'],['../structcutlass_1_1reference_1_1device_1_1detail_1_1TensorFillRandomUniformFunc_1_1Params.html#aff721d5c0b74fd3a6edefeecca97debe',1,'cutlass::reference::device::detail::TensorFillRandomUniformFunc::Params::random()'],['../structcutlass_1_1reference_1_1device_1_1detail_1_1TensorFillRandomUniformFunc.html#a134e1c6b57395a313718e8ad5590feab',1,'cutlass::reference::device::detail::TensorFillRandomUniformFunc::random()']]],
  ['random_5fnormal_5ffloat',['random_normal_float',['../namespacecutlass_1_1reference_1_1device_1_1detail.html#aad6bb03250c194b0492e95836ff0670f',1,'cutlass::reference::device::detail']]],
  ['random_5fnormal_5ffloat_3c_20double_20_3e',['random_normal_float&lt; double &gt;',['../namespacecutlass_1_1reference_1_1device_1_1detail.html#a01407aef17bb71937749d54212a4e1dc',1,'cutlass::reference::device::detail']]],
  ['random_5funiform_5ffloat',['random_uniform_float',['../namespacecutlass_1_1reference_1_1device_1_1detail.html#a51f1d0adae831ce5ec07dfbfb36b6408',1,'cutlass::reference::device::detail']]],
  ['random_5funiform_5ffloat_3c_20double_20_3e',['random_uniform_float&lt; double &gt;',['../namespacecutlass_1_1reference_1_1device_1_1detail.html#a205da4816550538f0589fde5a43d34e8',1,'cutlass::reference::device::detail']]],
  ['randomfunc',['RandomFunc',['../structcutlass_1_1reference_1_1device_1_1detail_1_1TensorFillRandomGaussianFunc.html#a411755eb3303cc9681111ac2fabb2a48',1,'cutlass::reference::device::detail::TensorFillRandomGaussianFunc::RandomFunc()'],['../structcutlass_1_1reference_1_1device_1_1detail_1_1TensorFillRandomUniformFunc.html#afafde9750cffecbaf6d0dba8a55fc5d5',1,'cutlass::reference::device::detail::TensorFillRandomUniformFunc::RandomFunc()']]],
  ['randomgaussianfunc',['RandomGaussianFunc',['../structcutlass_1_1reference_1_1device_1_1detail_1_1RandomGaussianFunc.html',1,'cutlass::reference::device::detail']]],
  ['randomgaussianfunc',['RandomGaussianFunc',['../structcutlass_1_1reference_1_1host_1_1detail_1_1RandomGaussianFunc.html',1,'cutlass::reference::host::detail']]],
  ['randomgaussianfunc',['RandomGaussianFunc',['../structcutlass_1_1reference_1_1device_1_1detail_1_1RandomGaussianFunc.html#a38bf4f3bfe2df73c264a23f3956a65fd',1,'cutlass::reference::device::detail::RandomGaussianFunc::RandomGaussianFunc()'],['../structcutlass_1_1reference_1_1host_1_1detail_1_1RandomGaussianFunc.html#a45ca53d7fcac978533361d8fc5f15311',1,'cutlass::reference::host::detail::RandomGaussianFunc::RandomGaussianFunc()'],['../structcutlass_1_1reference_1_1host_1_1detail_1_1RandomGaussianFunc_3_01complex_3_01Element_01_4_01_4.html#a72b71664e9ddc5ce392e1db822cfdada',1,'cutlass::reference::host::detail::RandomGaussianFunc&lt; complex&lt; Element &gt; &gt;::RandomGaussianFunc()']]],
  ['randomgaussianfunc_3c_20complex_3c_20element_20_3e_20_3e',['RandomGaussianFunc&lt; complex&lt; Element &gt; &gt;',['../structcutlass_1_1reference_1_1host_1_1detail_1_1RandomGaussianFunc_3_01complex_3_01Element_01_4_01_4.html',1,'cutlass::reference::host::detail']]],
  ['randomuniformfunc',['RandomUniformFunc',['../structcutlass_1_1reference_1_1host_1_1detail_1_1RandomUniformFunc.html',1,'cutlass::reference::host::detail']]],
  ['randomuniformfunc',['RandomUniformFunc',['../structcutlass_1_1reference_1_1device_1_1detail_1_1RandomUniformFunc.html',1,'cutlass::reference::device::detail']]],
  ['randomuniformfunc',['RandomUniformFunc',['../structcutlass_1_1reference_1_1device_1_1detail_1_1RandomUniformFunc.html#a50179367fdbaf59e6fb585b9e871730e',1,'cutlass::reference::device::detail::RandomUniformFunc::RandomUniformFunc()'],['../structcutlass_1_1reference_1_1host_1_1detail_1_1RandomUniformFunc.html#ac39a320d0975f140d51cf2cd7dd53441',1,'cutlass::reference::host::detail::RandomUniformFunc::RandomUniformFunc()'],['../structcutlass_1_1reference_1_1host_1_1detail_1_1RandomUniformFunc_3_01complex_3_01Element_01_4_01_4.html#aa894090fefa7f3cc99ec66f5b6e7b7ac',1,'cutlass::reference::host::detail::RandomUniformFunc&lt; complex&lt; Element &gt; &gt;::RandomUniformFunc()']]],
  ['randomuniformfunc_3c_20complex_3c_20element_20_3e_20_3e',['RandomUniformFunc&lt; complex&lt; Element &gt; &gt;',['../structcutlass_1_1reference_1_1host_1_1detail_1_1RandomUniformFunc_3_01complex_3_01Element_01_4_01_4.html',1,'cutlass::reference::host::detail']]],
  ['range',['range',['../structcutlass_1_1reference_1_1device_1_1detail_1_1RandomUniformFunc_1_1Params.html#ace319d38113a83e3cccc7860897154c3',1,'cutlass::reference::device::detail::RandomUniformFunc::Params::range()'],['../structcutlass_1_1reference_1_1host_1_1detail_1_1RandomUniformFunc.html#ab65f9bd7b329d6ce077daf50fb3148f1',1,'cutlass::reference::host::detail::RandomUniformFunc::range()'],['../structcutlass_1_1reference_1_1host_1_1detail_1_1RandomUniformFunc_3_01complex_3_01Element_01_4_01_4.html#a17c91db74ff727d9f42442c09d21d0b3',1,'cutlass::reference::host::detail::RandomUniformFunc&lt; complex&lt; Element &gt; &gt;::range()']]],
  ['raw',['raw',['../structcutlass_1_1half__t.html#aaee5cf278f88e1de09cbebf8cdd2cbe8',1,'cutlass::half_t::raw()'],['../structcutlass_1_1half__t.html#a46379df65c25c4d05d8520430d2daf19',1,'cutlass::half_t::raw() const ']]],
  ['raw_5fdata',['raw_data',['../structcutlass_1_1AlignedBuffer.html#a3a87c3b8f14893d30f374bde2b88052c',1,'cutlass::AlignedBuffer::raw_data()'],['../structcutlass_1_1AlignedBuffer.html#ae591f458d228ec8ac08caf8846dab67d',1,'cutlass::AlignedBuffer::raw_data() const '],['../classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ae4e76ed2b36a4deda6ef36b00fdda363',1,'cutlass::Array&lt; T, N, true &gt;::raw_data()'],['../classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a90aaac40587e3ae5622030e999995f40',1,'cutlass::Array&lt; T, N, true &gt;::raw_data() const '],['../classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a66e2465301e46afebf9e56c4060fb3cb',1,'cutlass::Array&lt; T, N, false &gt;::raw_data()'],['../classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a16e55f7c4ae1700ae09c2bce137d06ae',1,'cutlass::Array&lt; T, N, false &gt;::raw_data() const ']]],
  ['rbegin',['rbegin',['../classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ad8ec17a6d004cb6ffd4450c0686cd924',1,'cutlass::Array&lt; T, N, true &gt;::rbegin()'],['../classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a2098c88aed61f9b27bac37a083130336',1,'cutlass::Array&lt; T, N, false &gt;::rbegin()']]],
  ['real',['Real',['../structcutlass_1_1reference_1_1host_1_1detail_1_1RandomUniformFunc.html#a317b294d017b81224eb1aca2742e8a3c',1,'cutlass::reference::host::detail::RandomUniformFunc::Real()'],['../structcutlass_1_1reference_1_1host_1_1detail_1_1RandomUniformFunc_3_01complex_3_01Element_01_4_01_4.html#abd7e66b999df7719f6ac77f0a82a0d5d',1,'cutlass::reference::host::detail::RandomUniformFunc&lt; complex&lt; Element &gt; &gt;::Real()'],['../structcutlass_1_1TypeTraits_3_01complex_3_01double_01_4_01_4_1_1integer__type.html#ad95c7b0fd5538d193b3581bf4cd7eca1',1,'cutlass::TypeTraits&lt; complex&lt; double &gt; &gt;::integer_type::real()'],['../structcutlass_1_1TypeTraits_3_01complex_3_01double_01_4_01_4_1_1unsigned__type.html#ab1d0b72c9376509deaf61bb2eed09dc5',1,'cutlass::TypeTraits&lt; complex&lt; double &gt; &gt;::unsigned_type::real()'],['../classcutlass_1_1complex.html#afaeeace8c2987047400b83973e196aeb',1,'cutlass::complex::real() const '],['../classcutlass_1_1complex.html#a2b4f7ab916a7b0f672a05a73a4d77e4b',1,'cutlass::complex::real()'],['../namespacecutlass.html#ac0ea92c9a2a594446a84f7f86a79e143',1,'cutlass::real(cuFloatComplex const &amp;z)'],['../namespacecutlass.html#a8fd098f5be681292ce2051ce171f0aa5',1,'cutlass::real(cuFloatComplex &amp;z)'],['../namespacecutlass.html#ae0cde0dbf619d61fcbeed8bf9f21c6ca',1,'cutlass::real(cuDoubleComplex const &amp;z)'],['../namespacecutlass.html#a2ccc4e86a30fe5337bc3664e6bcd1bb4',1,'cutlass::real(cuDoubleComplex &amp;z)'],['../namespacecutlass.html#a8538ac12cfc174e0965cabfe6bd3d859',1,'cutlass::real(complex&lt; T &gt; const &amp;z)'],['../namespacecutlass.html#a491c61e7a51c2efdc93c61a549e20eb6',1,'cutlass::real(complex&lt; T &gt; &amp;z)']]],
  ['real_2eh',['real.h',['../real_8h.html',1,'']]],
  ['realtype',['RealType',['../structcutlass_1_1RealType.html',1,'cutlass']]],
  ['realtype_3c_20complex_3c_20t_20_3e_20_3e',['RealType&lt; complex&lt; T &gt; &gt;',['../structcutlass_1_1RealType_3_01complex_3_01T_01_4_01_4.html',1,'cutlass']]],
  ['reduce',['Reduce',['../structcutlass_1_1reduction_1_1thread_1_1Reduce.html',1,'cutlass::reduction::thread']]],
  ['reduce_2eh',['reduce.h',['../reduce_8h.html',1,'']]],
  ['reduce_3c_20plus_3c_20half_5ft_20_3e_2c_20alignedarray_3c_20half_5ft_2c_20n_20_3e_20_3e',['Reduce&lt; plus&lt; half_t &gt;, AlignedArray&lt; half_t, N &gt; &gt;',['../structcutlass_1_1reduction_1_1thread_1_1Reduce_3_01plus_3_01half__t_01_4_00_01AlignedArray_3_01half__t_00_01N_01_4_01_4.html',1,'cutlass::reduction::thread']]],
  ['reduce_3c_20plus_3c_20half_5ft_20_3e_2c_20array_3c_20half_5ft_2c_20n_20_3e_20_3e',['Reduce&lt; plus&lt; half_t &gt;, Array&lt; half_t, N &gt; &gt;',['../structcutlass_1_1reduction_1_1thread_1_1Reduce_3_01plus_3_01half__t_01_4_00_01Array_3_01half__t_00_01N_01_4_01_4.html',1,'cutlass::reduction::thread']]],
  ['reduce_3c_20plus_3c_20t_20_3e_2c_20array_3c_20t_2c_20n_20_3e_20_3e',['Reduce&lt; plus&lt; T &gt;, Array&lt; T, N &gt; &gt;',['../structcutlass_1_1reduction_1_1thread_1_1Reduce_3_01plus_3_01T_01_4_00_01Array_3_01T_00_01N_01_4_01_4.html',1,'cutlass::reduction::thread']]],
  ['reduce_3c_20plus_3c_20t_20_3e_2c_20t_20_3e',['Reduce&lt; plus&lt; T &gt;, T &gt;',['../structcutlass_1_1reduction_1_1thread_1_1Reduce_3_01plus_3_01T_01_4_00_01T_01_4.html',1,'cutlass::reduction::thread']]],
  ['reduce_5fsplit_5fk_2eh',['reduce_split_k.h',['../reduce__split__k_8h.html',1,'']]],
  ['reduceadd',['ReduceAdd',['../structcutlass_1_1reduction_1_1thread_1_1ReduceAdd.html',1,'cutlass::reduction::thread']]],
  ['reduceadd',['ReduceAdd',['../structcutlass_1_1reduction_1_1thread_1_1ReduceAdd.html#ac9ccfca110d19133c4b203227577e2ec',1,'cutlass::reduction::thread::ReduceAdd']]],
  ['reducesplitk',['ReduceSplitK',['../classcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK.html',1,'cutlass::reduction::kernel']]],
  ['reduction',['reduction',['../structcutlass_1_1gemm_1_1device_1_1GemmSplitKParallel_1_1Arguments.html#af6008a17370eec923078a23564972383',1,'cutlass::gemm::device::GemmSplitKParallel::Arguments::reduction()'],['../structcutlass_1_1gemm_1_1device_1_1GemmSplitKParallel_3_01ElementA___00_01LayoutA___00_01Elementafcb1aeaf2035a7ac769d7acc233423b.html#a63048fa3419753d96a60eaee28f6cfe4',1,'cutlass::gemm::device::GemmSplitKParallel&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ConvertScaledOp_, ReductionOp_, ThreadblockSwizzle_, Stages, kAlignmentA, kAlignmentB, Operator_ &gt;::Arguments::reduction()'],['../structcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK_1_1Params.html#ab59614242d435c963b9607eb7da6f5b5',1,'cutlass::reduction::kernel::ReduceSplitK::Params::reduction()']]],
  ['reduction_5fop_2eh',['reduction_op.h',['../reduction__op_8h.html',1,'']]],
  ['reduction_5foperators_2eh',['reduction_operators.h',['../reduction__operators_8h.html',1,'']]],
  ['reduction_5fstride',['reduction_stride',['../structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#a5d1463d473d4226b0d19c581b16ed3b2',1,'cutlass::reduction::BatchedReductionTraits::Params']]],
  ['reductionkernel',['ReductionKernel',['../classcutlass_1_1gemm_1_1device_1_1GemmSplitKParallel.html#aaf83264eb3effceee610d9547ddf32e9',1,'cutlass::gemm::device::GemmSplitKParallel::ReductionKernel()'],['../classcutlass_1_1gemm_1_1device_1_1GemmSplitKParallel_3_01ElementA___00_01LayoutA___00_01ElementBbe7c1f7154ad5b5bf9d4d28301e2b457.html#aa69c465611c07990cdc79605c16b04ff',1,'cutlass::gemm::device::GemmSplitKParallel&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ConvertScaledOp_, ReductionOp_, ThreadblockSwizzle_, Stages, kAlignmentA, kAlignmentB, Operator_ &gt;::ReductionKernel()']]],
  ['reductionop',['ReductionOp',['../classcutlass_1_1gemm_1_1device_1_1GemmSplitKParallel.html#ac82ba3da12b03bc91586a3947ce99fc5',1,'cutlass::gemm::device::GemmSplitKParallel::ReductionOp()'],['../classcutlass_1_1gemm_1_1device_1_1GemmSplitKParallel_3_01ElementA___00_01LayoutA___00_01ElementBbe7c1f7154ad5b5bf9d4d28301e2b457.html#a2d8d3a504dd8807ed09e25f37a658783',1,'cutlass::gemm::device::GemmSplitKParallel&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ConvertScaledOp_, ReductionOp_, ThreadblockSwizzle_, Stages, kAlignmentA, kAlignmentB, Operator_ &gt;::ReductionOp()'],['../classcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK.html#a9fed5689109358e708a27d487db15232',1,'cutlass::reduction::kernel::ReduceSplitK::ReductionOp()']]],
  ['reductionopplus',['ReductionOpPlus',['../classcutlass_1_1epilogue_1_1thread_1_1ReductionOpPlus.html',1,'cutlass::epilogue::thread']]],
  ['reductionopplus',['ReductionOpPlus',['../classcutlass_1_1epilogue_1_1thread_1_1ReductionOpPlus.html#a06e34382d5f50a7331f723df7b3f709b',1,'cutlass::epilogue::thread::ReductionOpPlus']]],
  ['reductionsize',['ReductionSize',['../structcutlass_1_1reduction_1_1BatchedReductionTraits.html#a00c71c9a18aaad84f4a48023dbbb454e',1,'cutlass::reduction::BatchedReductionTraits']]],
  ['ref',['ref',['../classcutlass_1_1TensorView.html#a33385840adab818ef25076edab42175d',1,'cutlass::TensorView::ref()'],['../classcutlass_1_1thread_1_1Matrix.html#a21e0504879de0558718e05977a7b8a73',1,'cutlass::thread::Matrix::ref()']]],
  ['ref_5fa',['ref_a',['../structcutlass_1_1reference_1_1host_1_1detail_1_1TensorFuncBinaryOp.html#a77a01e6a9da3273bde175cf5df5d62b4',1,'cutlass::reference::host::detail::TensorFuncBinaryOp::ref_a()'],['../structcutlass_1_1gemm_1_1device_1_1Gemm_1_1Arguments.html#a390abae1ca0e01a4b6e58f3724b48eed',1,'cutlass::gemm::device::Gemm::Arguments::ref_A()'],['../structcutlass_1_1gemm_1_1device_1_1Gemm_3_01ElementA___00_01LayoutA___00_01ElementB___00_01Layou1b211cc9c97c022d8fe10f2dd32c8709.html#a9bdaf3563983efcca649460be169b334',1,'cutlass::gemm::device::Gemm&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, AlignmentA, AlignmentB, SplitKSerial, Operator_, IsBetaZero &gt;::Arguments::ref_A()'],['../structcutlass_1_1gemm_1_1device_1_1GemmBatched_1_1Arguments.html#a55f32be45559dbf84dcc2db26784f625',1,'cutlass::gemm::device::GemmBatched::Arguments::ref_A()'],['../structcutlass_1_1gemm_1_1device_1_1GemmBatched_3_01ElementA___00_01LayoutA___00_01ElementB___00_213d78696663f4231cd52c6a277c60e5.html#a1727630fc0525724df28a75ccf2580b9',1,'cutlass::gemm::device::GemmBatched&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, AlignmentA, AlignmentB, Operator_ &gt;::Arguments::ref_A()'],['../structcutlass_1_1gemm_1_1device_1_1GemmComplex_1_1Arguments.html#a654ced578699b96a0f805434afc5074c',1,'cutlass::gemm::device::GemmComplex::Arguments::ref_A()'],['../structcutlass_1_1gemm_1_1device_1_1GemmComplex_3_01ElementA___00_01LayoutA___00_01ElementB___00_a3923967cafb5cb9774c320dc24baa77.html#ac8e9298e3786e9391d740faa4d0566f2',1,'cutlass::gemm::device::GemmComplex&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, TransformA, TransformB, SplitKSerial &gt;::Arguments::ref_A()'],['../structcutlass_1_1gemm_1_1device_1_1GemmSplitKParallel_1_1Arguments.html#a3f4d6497cd54e624b3e410996a8b7d10',1,'cutlass::gemm::device::GemmSplitKParallel::Arguments::ref_A()'],['../structcutlass_1_1gemm_1_1device_1_1GemmSplitKParallel_3_01ElementA___00_01LayoutA___00_01Elementafcb1aeaf2035a7ac769d7acc233423b.html#a8b10e75e5d6cd348dacc085f5264ee95',1,'cutlass::gemm::device::GemmSplitKParallel&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ConvertScaledOp_, ReductionOp_, ThreadblockSwizzle_, Stages, kAlignmentA, kAlignmentB, Operator_ &gt;::Arguments::ref_A()'],['../structcutlass_1_1gemm_1_1kernel_1_1Gemm_1_1Params.html#a3c4db6514188c51f63ee88130d9b9b0c',1,'cutlass::gemm::kernel::Gemm::Params::ref_A()'],['../structcutlass_1_1gemm_1_1kernel_1_1GemmBatched_1_1Params.html#ad1867c0875c10e6327c7fae16acd35a3',1,'cutlass::gemm::kernel::GemmBatched::Params::ref_A()'],['../structcutlass_1_1gemm_1_1kernel_1_1GemmSplitKParallel_1_1Params.html#a475c24216aef2580f9b3405a1a42bfd3',1,'cutlass::gemm::kernel::GemmSplitKParallel::Params::ref_A()']]],
  ['ref_5fb',['ref_b',['../structcutlass_1_1reference_1_1host_1_1detail_1_1TensorFuncBinaryOp.html#a416b2983d56932fa8970a7b988f3d7e6',1,'cutlass::reference::host::detail::TensorFuncBinaryOp::ref_b()'],['../structcutlass_1_1gemm_1_1device_1_1Gemm_1_1Arguments.html#ae712c362f83fbd45679a6e989315d3dc',1,'cutlass::gemm::device::Gemm::Arguments::ref_B()'],['../structcutlass_1_1gemm_1_1device_1_1Gemm_3_01ElementA___00_01LayoutA___00_01ElementB___00_01Layou1b211cc9c97c022d8fe10f2dd32c8709.html#ab77204c1010b17c6643d26a89f41c3d0',1,'cutlass::gemm::device::Gemm&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, AlignmentA, AlignmentB, SplitKSerial, Operator_, IsBetaZero &gt;::Arguments::ref_B()'],['../structcutlass_1_1gemm_1_1device_1_1GemmBatched_1_1Arguments.html#a48844293c34b9c44fe57f577370664ea',1,'cutlass::gemm::device::GemmBatched::Arguments::ref_B()'],['../structcutlass_1_1gemm_1_1device_1_1GemmBatched_3_01ElementA___00_01LayoutA___00_01ElementB___00_213d78696663f4231cd52c6a277c60e5.html#ad7d2b82b83d7503b9f920ce3bdcdffa5',1,'cutlass::gemm::device::GemmBatched&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, AlignmentA, AlignmentB, Operator_ &gt;::Arguments::ref_B()'],['../structcutlass_1_1gemm_1_1device_1_1GemmComplex_1_1Arguments.html#a1eb0b3a45baf02021c1d0d12ad728e69',1,'cutlass::gemm::device::GemmComplex::Arguments::ref_B()'],['../structcutlass_1_1gemm_1_1device_1_1GemmComplex_3_01ElementA___00_01LayoutA___00_01ElementB___00_a3923967cafb5cb9774c320dc24baa77.html#ab706387c660af35ae2b9579165eec85d',1,'cutlass::gemm::device::GemmComplex&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, TransformA, TransformB, SplitKSerial &gt;::Arguments::ref_B()'],['../structcutlass_1_1gemm_1_1device_1_1GemmSplitKParallel_1_1Arguments.html#ac37c811b2f58c7756ef720fdfc0d072b',1,'cutlass::gemm::device::GemmSplitKParallel::Arguments::ref_B()'],['../structcutlass_1_1gemm_1_1device_1_1GemmSplitKParallel_3_01ElementA___00_01LayoutA___00_01Elementafcb1aeaf2035a7ac769d7acc233423b.html#a9a22df7c4d515a48e03fd6f16e074217',1,'cutlass::gemm::device::GemmSplitKParallel&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ConvertScaledOp_, ReductionOp_, ThreadblockSwizzle_, Stages, kAlignmentA, kAlignmentB, Operator_ &gt;::Arguments::ref_B()'],['../structcutlass_1_1gemm_1_1kernel_1_1Gemm_1_1Params.html#ac9e6c1f13f20d925af51c682e2031a81',1,'cutlass::gemm::kernel::Gemm::Params::ref_B()'],['../structcutlass_1_1gemm_1_1kernel_1_1GemmBatched_1_1Params.html#ade55adc311c5561efe76f53ffd56d1f4',1,'cutlass::gemm::kernel::GemmBatched::Params::ref_B()'],['../structcutlass_1_1gemm_1_1kernel_1_1GemmSplitKParallel_1_1Params.html#a2b67aca90833b8a5a85605f1902de539',1,'cutlass::gemm::kernel::GemmSplitKParallel::Params::ref_B()']]],
  ['ref_5fc',['ref_C',['../structcutlass_1_1gemm_1_1device_1_1Gemm_1_1Arguments.html#abdba57a68d6982fffbb1cc3db34ef0f9',1,'cutlass::gemm::device::Gemm::Arguments::ref_C()'],['../structcutlass_1_1gemm_1_1device_1_1Gemm_3_01ElementA___00_01LayoutA___00_01ElementB___00_01Layou1b211cc9c97c022d8fe10f2dd32c8709.html#a590b8da88ae9350042838451e3e37a22',1,'cutlass::gemm::device::Gemm&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, AlignmentA, AlignmentB, SplitKSerial, Operator_, IsBetaZero &gt;::Arguments::ref_C()'],['../structcutlass_1_1gemm_1_1device_1_1GemmBatched_1_1Arguments.html#ab0955b722ad4ea0217f725e34b3bcfbe',1,'cutlass::gemm::device::GemmBatched::Arguments::ref_C()'],['../structcutlass_1_1gemm_1_1device_1_1GemmBatched_3_01ElementA___00_01LayoutA___00_01ElementB___00_213d78696663f4231cd52c6a277c60e5.html#aa9e30e41627595590421d8b53941b2b2',1,'cutlass::gemm::device::GemmBatched&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, AlignmentA, AlignmentB, Operator_ &gt;::Arguments::ref_C()'],['../structcutlass_1_1gemm_1_1device_1_1GemmComplex_1_1Arguments.html#a6d245eb700f43bb8bf0935e9c5ea2587',1,'cutlass::gemm::device::GemmComplex::Arguments::ref_C()'],['../structcutlass_1_1gemm_1_1device_1_1GemmComplex_3_01ElementA___00_01LayoutA___00_01ElementB___00_a3923967cafb5cb9774c320dc24baa77.html#a3a59aa793429bc57d796b40fa4fab622',1,'cutlass::gemm::device::GemmComplex&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, TransformA, TransformB, SplitKSerial &gt;::Arguments::ref_C()'],['../structcutlass_1_1gemm_1_1device_1_1GemmSplitKParallel_1_1Arguments.html#aa111536496495825c5d2ac2ea7360998',1,'cutlass::gemm::device::GemmSplitKParallel::Arguments::ref_C()'],['../structcutlass_1_1gemm_1_1device_1_1GemmSplitKParallel_3_01ElementA___00_01LayoutA___00_01Elementafcb1aeaf2035a7ac769d7acc233423b.html#a2aabb13f196a087b77245c67c8664b7b',1,'cutlass::gemm::device::GemmSplitKParallel&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ConvertScaledOp_, ReductionOp_, ThreadblockSwizzle_, Stages, kAlignmentA, kAlignmentB, Operator_ &gt;::Arguments::ref_C()'],['../structcutlass_1_1gemm_1_1kernel_1_1Gemm_1_1Params.html#a37660d1a2a1031c44f0bb0c27d438ba3',1,'cutlass::gemm::kernel::Gemm::Params::ref_C()'],['../structcutlass_1_1gemm_1_1kernel_1_1GemmBatched_1_1Params.html#a08ecd763b6785dfe872a6e517dc731e6',1,'cutlass::gemm::kernel::GemmBatched::Params::ref_C()']]],
  ['ref_5fd',['ref_D',['../structcutlass_1_1gemm_1_1device_1_1Gemm_1_1Arguments.html#a66f9983db4a09ac0d90291c0f8723897',1,'cutlass::gemm::device::Gemm::Arguments::ref_D()'],['../structcutlass_1_1gemm_1_1device_1_1Gemm_3_01ElementA___00_01LayoutA___00_01ElementB___00_01Layou1b211cc9c97c022d8fe10f2dd32c8709.html#ab1d4d5865786a415f87db1def1b029e7',1,'cutlass::gemm::device::Gemm&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, AlignmentA, AlignmentB, SplitKSerial, Operator_, IsBetaZero &gt;::Arguments::ref_D()'],['../structcutlass_1_1gemm_1_1device_1_1GemmBatched_1_1Arguments.html#ae4450f06a6975191d94026865e445578',1,'cutlass::gemm::device::GemmBatched::Arguments::ref_D()'],['../structcutlass_1_1gemm_1_1device_1_1GemmBatched_3_01ElementA___00_01LayoutA___00_01ElementB___00_213d78696663f4231cd52c6a277c60e5.html#a17c4e381e91229a8ef15b18ee5ec073d',1,'cutlass::gemm::device::GemmBatched&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, AlignmentA, AlignmentB, Operator_ &gt;::Arguments::ref_D()'],['../structcutlass_1_1gemm_1_1device_1_1GemmComplex_1_1Arguments.html#a19d893bbaaef4122d9a66a74bb7fa21f',1,'cutlass::gemm::device::GemmComplex::Arguments::ref_D()'],['../structcutlass_1_1gemm_1_1device_1_1GemmComplex_3_01ElementA___00_01LayoutA___00_01ElementB___00_a3923967cafb5cb9774c320dc24baa77.html#a2904e3ad7a47b3d85ea60d94eeebe84b',1,'cutlass::gemm::device::GemmComplex&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, TransformA, TransformB, SplitKSerial &gt;::Arguments::ref_D()'],['../structcutlass_1_1gemm_1_1device_1_1GemmSplitKParallel_1_1Arguments.html#a1550cfad6be8e22c9dc13b05f5601845',1,'cutlass::gemm::device::GemmSplitKParallel::Arguments::ref_D()'],['../structcutlass_1_1gemm_1_1device_1_1GemmSplitKParallel_3_01ElementA___00_01LayoutA___00_01Elementafcb1aeaf2035a7ac769d7acc233423b.html#a850da307d8741296e515add0f716eaf9',1,'cutlass::gemm::device::GemmSplitKParallel&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ConvertScaledOp_, ReductionOp_, ThreadblockSwizzle_, Stages, kAlignmentA, kAlignmentB, Operator_ &gt;::Arguments::ref_D()'],['../structcutlass_1_1gemm_1_1kernel_1_1Gemm_1_1Params.html#a33618c431b2f6a6730c8ab1f1c1a590f',1,'cutlass::gemm::kernel::Gemm::Params::ref_D()'],['../structcutlass_1_1gemm_1_1kernel_1_1GemmBatched_1_1Params.html#a4f18093b18b0b6dd01a5df0a3813cd40',1,'cutlass::gemm::kernel::GemmBatched::Params::ref_D()'],['../structcutlass_1_1gemm_1_1kernel_1_1GemmSplitKParallel_1_1Params.html#ab30d65bd2883bcf15a37a404df944aa4',1,'cutlass::gemm::kernel::GemmSplitKParallel::Params::ref_D()']]],
  ['reference',['reference',['../structcutlass_1_1AlignedBuffer.html#afa029189fb46528b5eb5f50060cbf28e',1,'cutlass::AlignedBuffer::reference()'],['../classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a5827968c9c3deca639f5981ad895fe67',1,'cutlass::Array&lt; T, N, true &gt;::reference()'],['../classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4_1_1reference.html#a257c25bee7fa54ff1d492bc0697b05cc',1,'cutlass::Array&lt; T, N, false &gt;::reference::reference()'],['../classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4_1_1reference.html#a2c2ac2556e27f48703a9bc1c4e6ed2aa',1,'cutlass::Array&lt; T, N, false &gt;::reference::reference(Storage *ptr, int idx=0)'],['../structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage.html#a99b724aec92bebece9bc187fb636bc11',1,'cutlass::epilogue::threadblock::EpilogueBase::SharedStorage::reference()'],['../classcutlass_1_1TensorRef.html#a0aba29ebf8715d217817a49356f95d3f',1,'cutlass::TensorRef::Reference()'],['../classcutlass_1_1TensorView.html#a4d8af7fd842866a218722e7686d6bc3c',1,'cutlass::TensorView::Reference()'],['../classcutlass_1_1thread_1_1Matrix.html#a3c9535ffa08c91d0040b09667c81b201',1,'cutlass::thread::Matrix::Reference()'],['../classcutlass_1_1HostTensor.html#a33698c7aa33255e7a2e7abc298e28f39',1,'cutlass::HostTensor::Reference()']]],
  ['reference',['reference',['../classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4_1_1reference.html',1,'cutlass::Array&lt; T, N, false &gt;']]],
  ['referencefactory',['ReferenceFactory',['../structcutlass_1_1ReferenceFactory.html',1,'cutlass']]],
  ['referencefactory_3c_20element_2c_20false_20_3e',['ReferenceFactory&lt; Element, false &gt;',['../structcutlass_1_1ReferenceFactory_3_01Element_00_01false_01_4.html',1,'cutlass']]],
  ['referencefactory_3c_20element_2c_20true_20_3e',['ReferenceFactory&lt; Element, true &gt;',['../structcutlass_1_1ReferenceFactory_3_01Element_00_01true_01_4.html',1,'cutlass']]],
  ['regular_5ftile_5faccess_5fiterator_2eh',['regular_tile_access_iterator.h',['../regular__tile__access__iterator_8h.html',1,'']]],
  ['regular_5ftile_5faccess_5fiterator_5fpitch_5flinear_2eh',['regular_tile_access_iterator_pitch_linear.h',['../regular__tile__access__iterator__pitch__linear_8h.html',1,'']]],
  ['regular_5ftile_5faccess_5fiterator_5ftensor_5fop_2eh',['regular_tile_access_iterator_tensor_op.h',['../regular__tile__access__iterator__tensor__op_8h.html',1,'']]],
  ['regular_5ftile_5fiterator_2eh',['regular_tile_iterator.h',['../regular__tile__iterator_8h.html',1,'']]],
  ['regular_5ftile_5fiterator_5fpitch_5flinear_2eh',['regular_tile_iterator_pitch_linear.h',['../regular__tile__iterator__pitch__linear_8h.html',1,'']]],
  ['regular_5ftile_5fiterator_5fpitch_5flinear_5f2dthreadtile_2eh',['regular_tile_iterator_pitch_linear_2dthreadtile.h',['../regular__tile__iterator__pitch__linear__2dthreadtile_8h.html',1,'']]],
  ['regular_5ftile_5fiterator_5ftensor_5fop_2eh',['regular_tile_iterator_tensor_op.h',['../regular__tile__iterator__tensor__op_8h.html',1,'']]],
  ['regular_5ftile_5fiterator_5ftensor_5fop_5fsm70_2eh',['regular_tile_iterator_tensor_op_sm70.h',['../regular__tile__iterator__tensor__op__sm70_8h.html',1,'']]],
  ['regulartileaccessiterator',['RegularTileAccessIterator',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__0855e9d9ab619202d2397180c1e4c4a5.html#ad71831a7593c13375f067f8acd048f35',1,'cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, Alignment &gt;::RegularTileAccessIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__eb7d20f8b9d69e0ae5e7ef51dc480867.html#a993c3d66e26ad80da41290a77c49134b',1,'cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, Alignment &gt;::RegularTileAccessIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__f04332958a49a47d6fb2b25201764630.html#a64df8a853b101bd8acddc06d4a973da5',1,'cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, Alignment &gt;::RegularTileAccessIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__ebf4714349612673e8b6609b763eeb6f.html#ae4927c7f718b52aa80f603d9911b9639',1,'cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::TensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element_))&gt;, AdvanceRank, ThreadMap_, Alignment &gt;::RegularTileAccessIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__2c1476eaf582bfe972793e17babfe985.html#a633db64682aa6494c2553e11614b5e1d',1,'cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::ColumnMajorTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element_))&gt;, AdvanceRank, ThreadMap_, Alignment &gt;::RegularTileAccessIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__6baada077236f1a368c61c5e11b45b72.html#abf23348f7e8d3b8aaac6f23c116dcf5c',1,'cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::RowMajorTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element_))&gt;, AdvanceRank, ThreadMap_, Alignment &gt;::RegularTileAccessIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__e9a9e0f4286f652f55eb9b863b21effe.html#a0f1e846cd4287c51e2d6135a8472c1a8',1,'cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::TensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::RegularTileAccessIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__a3c11cf1f00ef7a1efb8389ac6e4c6e0.html#acc48b323a8a9a8e75e0bfdb77c1cbcc5',1,'cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::ColumnMajorTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::RegularTileAccessIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__0184b7188941788a96624510a4b2f876.html#a025187783fb6d708885d31af2492bca4',1,'cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::RowMajorTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::RegularTileAccessIterator()']]],
  ['regulartileaccessiterator',['RegularTileAccessIterator',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator.html',1,'cutlass::transform::threadblock']]],
  ['regulartileaccessiterator_3c_20layout_3a_3apitchlinearshape_3c_20shape_3a_3akcolumn_2c_20shape_3a_3akrow_20_3e_2c_20element_2c_20layout_3a_3apitchlinear_2c_28kadvancerank_3d_3d0_3f1_3a0_29_2c_20threadmap_5f_20_3e',['RegularTileAccessIterator&lt; layout::PitchLinearShape&lt; Shape::kColumn, Shape::kRow &gt;, Element, layout::PitchLinear,(kAdvanceRank==0?1:0), ThreadMap_ &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator.html',1,'cutlass::transform::threadblock']]],
  ['regulartileaccessiterator_3c_20layout_3a_3apitchlinearshape_3c_20shape_3a_3akcolumn_2c_20shape_3a_3akrow_20_3e_2c_20element_2c_20layout_3a_3atensoropmultiplicandcongruous_3c_20sizeof_5fbits_3c_20element_5f_20_3e_3a_3avalue_2c_20int_28128_2fsizeof_28element_5f_29_29_3e_2c_28kadvancerank_3d_3d0_3f1_3a0_29_2c_20threadmap_5f_20_3e',['RegularTileAccessIterator&lt; layout::PitchLinearShape&lt; Shape::kColumn, Shape::kRow &gt;, Element, layout::TensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element_))&gt;,(kAdvanceRank==0?1:0), ThreadMap_ &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator.html',1,'cutlass::transform::threadblock']]],
  ['regulartileaccessiterator_3c_20layout_3a_3apitchlinearshape_3c_20shape_3a_3akcolumn_2c_20shape_3a_3akrow_20_3e_2c_20element_2c_20layout_3a_3atensoropmultiplicandcrosswise_3c_20sizeof_5fbits_3c_20element_5f_20_3e_3a_3avalue_2c_20crosswise_20_3e_2c_28kadvancerank_3d_3d0_3f1_3a0_29_2c_20threadmap_5f_20_3e',['RegularTileAccessIterator&lt; layout::PitchLinearShape&lt; Shape::kColumn, Shape::kRow &gt;, Element, layout::TensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;,(kAdvanceRank==0?1:0), ThreadMap_ &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator.html',1,'cutlass::transform::threadblock']]],
  ['regulartileaccessiterator_3c_20layout_3a_3apitchlinearshape_3c_20shape_3a_3akrow_2c_20shape_3a_3akcolumn_20_3e_2c_20element_2c_20layout_3a_3apitchlinear_2c_28kadvancerank_3d_3d0_3f0_3a1_29_2c_20threadmap_5f_20_3e',['RegularTileAccessIterator&lt; layout::PitchLinearShape&lt; Shape::kRow, Shape::kColumn &gt;, Element, layout::PitchLinear,(kAdvanceRank==0?0:1), ThreadMap_ &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator.html',1,'cutlass::transform::threadblock']]],
  ['regulartileaccessiterator_3c_20layout_3a_3apitchlinearshape_3c_20shape_3a_3akrow_2c_20shape_3a_3akcolumn_20_3e_2c_20element_2c_20layout_3a_3atensoropmultiplicandcongruous_3c_20sizeof_5fbits_3c_20element_5f_20_3e_3a_3avalue_2c_20int_28128_2fsizeof_28element_5f_29_29_3e_2c_28kadvancerank_3d_3d0_3f0_3a1_29_2c_20threadmap_5f_20_3e',['RegularTileAccessIterator&lt; layout::PitchLinearShape&lt; Shape::kRow, Shape::kColumn &gt;, Element, layout::TensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element_))&gt;,(kAdvanceRank==0?0:1), ThreadMap_ &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator.html',1,'cutlass::transform::threadblock']]],
  ['regulartileaccessiterator_3c_20layout_3a_3apitchlinearshape_3c_20shape_3a_3akrow_2c_20shape_3a_3akcolumn_20_3e_2c_20element_2c_20layout_3a_3atensoropmultiplicandcrosswise_3c_20sizeof_5fbits_3c_20element_5f_20_3e_3a_3avalue_2c_20crosswise_20_3e_2c_28kadvancerank_3d_3d0_3f0_3a1_29_2c_20threadmap_5f_20_3e',['RegularTileAccessIterator&lt; layout::PitchLinearShape&lt; Shape::kRow, Shape::kColumn &gt;, Element, layout::TensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;,(kAdvanceRank==0?0:1), ThreadMap_ &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator.html',1,'cutlass::transform::threadblock']]],
  ['regulartileaccessiterator_3c_20shape_2c_20element_2c_20layout_2c_20kadvancerank_2c_20threadmap_20_3e',['RegularTileAccessIterator&lt; Shape, Element, Layout, kAdvanceRank, ThreadMap &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator.html',1,'cutlass::transform::threadblock']]],
  ['regulartileaccessiterator_3c_20shape_5f_2c_20element_5f_2c_20layout_3a_3acolumnmajor_2c_20advancerank_2c_20threadmap_5f_2c_20alignment_20_3e',['RegularTileAccessIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, Alignment &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__eb7d20f8b9d69e0ae5e7ef51dc480867.html',1,'cutlass::transform::threadblock']]],
  ['regulartileaccessiterator_3c_20shape_5f_2c_20element_5f_2c_20layout_3a_3acolumnmajortensoropmultiplicandcongruous_3c_20sizeof_5fbits_3c_20element_5f_20_3e_3a_3avalue_2c_20int_28128_2fsizeof_28element_5f_29_29_3e_2c_20advancerank_2c_20threadmap_5f_2c_20alignment_20_3e',['RegularTileAccessIterator&lt; Shape_, Element_, layout::ColumnMajorTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element_))&gt;, AdvanceRank, ThreadMap_, Alignment &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__2c1476eaf582bfe972793e17babfe985.html',1,'cutlass::transform::threadblock']]],
  ['regulartileaccessiterator_3c_20shape_5f_2c_20element_5f_2c_20layout_3a_3acolumnmajortensoropmultiplicandcrosswise_3c_20sizeof_5fbits_3c_20element_5f_20_3e_3a_3avalue_2c_20crosswise_20_3e_2c_20advancerank_2c_20threadmap_5f_2c_20alignment_20_3e',['RegularTileAccessIterator&lt; Shape_, Element_, layout::ColumnMajorTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, AdvanceRank, ThreadMap_, Alignment &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__a3c11cf1f00ef7a1efb8389ac6e4c6e0.html',1,'cutlass::transform::threadblock']]],
  ['regulartileaccessiterator_3c_20shape_5f_2c_20element_5f_2c_20layout_3a_3apitchlinear_2c_20advancerank_2c_20threadmap_5f_2c_20alignment_20_3e',['RegularTileAccessIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, Alignment &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__0855e9d9ab619202d2397180c1e4c4a5.html',1,'cutlass::transform::threadblock']]],
  ['regulartileaccessiterator_3c_20shape_5f_2c_20element_5f_2c_20layout_3a_3arowmajor_2c_20advancerank_2c_20threadmap_5f_2c_20alignment_20_3e',['RegularTileAccessIterator&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, Alignment &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__f04332958a49a47d6fb2b25201764630.html',1,'cutlass::transform::threadblock']]],
  ['regulartileaccessiterator_3c_20shape_5f_2c_20element_5f_2c_20layout_3a_3arowmajortensoropmultiplicandcongruous_3c_20sizeof_5fbits_3c_20element_5f_20_3e_3a_3avalue_2c_20int_28128_2fsizeof_28element_5f_29_29_3e_2c_20advancerank_2c_20threadmap_5f_2c_20alignment_20_3e',['RegularTileAccessIterator&lt; Shape_, Element_, layout::RowMajorTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element_))&gt;, AdvanceRank, ThreadMap_, Alignment &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__6baada077236f1a368c61c5e11b45b72.html',1,'cutlass::transform::threadblock']]],
  ['regulartileaccessiterator_3c_20shape_5f_2c_20element_5f_2c_20layout_3a_3arowmajortensoropmultiplicandcrosswise_3c_20sizeof_5fbits_3c_20element_5f_20_3e_3a_3avalue_2c_20crosswise_20_3e_2c_20advancerank_2c_20threadmap_5f_2c_20alignment_20_3e',['RegularTileAccessIterator&lt; Shape_, Element_, layout::RowMajorTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, AdvanceRank, ThreadMap_, Alignment &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__0184b7188941788a96624510a4b2f876.html',1,'cutlass::transform::threadblock']]],
  ['regulartileaccessiterator_3c_20shape_5f_2c_20element_5f_2c_20layout_3a_3atensoropmultiplicandcongruous_3c_20sizeof_5fbits_3c_20element_5f_20_3e_3a_3avalue_2c_20int_28128_2fsizeof_28element_5f_29_29_3e_2c_20advancerank_2c_20threadmap_5f_2c_20alignment_20_3e',['RegularTileAccessIterator&lt; Shape_, Element_, layout::TensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element_))&gt;, AdvanceRank, ThreadMap_, Alignment &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__ebf4714349612673e8b6609b763eeb6f.html',1,'cutlass::transform::threadblock']]],
  ['regulartileaccessiterator_3c_20shape_5f_2c_20element_5f_2c_20layout_3a_3atensoropmultiplicandcrosswise_3c_20sizeof_5fbits_3c_20element_5f_20_3e_3a_3avalue_2c_20crosswise_20_3e_2c_20advancerank_2c_20threadmap_5f_2c_20alignment_20_3e',['RegularTileAccessIterator&lt; Shape_, Element_, layout::TensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, AdvanceRank, ThreadMap_, Alignment &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__e9a9e0f4286f652f55eb9b863b21effe.html',1,'cutlass::transform::threadblock']]],
  ['regulartileiterator',['RegularTileIterator',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_0184a89653916f5d51ab59d1b386989a17.html#a77af9f2f9a530fbd63703bb83190135b',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, Alignment &gt;::RegularTileIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_0184a89653916f5d51ab59d1b386989a17.html#aaeef1f0d4985612d1d68b6e52e0e0af8',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, Alignment &gt;::RegularTileIterator(TensorRef const &amp;ref, int thread_idx)'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_0149454d361ea5885cf5166a920b5145df.html#aad803b85a96da22725a335b911975718',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, Alignment &gt;::RegularTileIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_0149454d361ea5885cf5166a920b5145df.html#a4e9c82df0fcde08f06a65d9efbefede6',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, Alignment &gt;::RegularTileIterator(TensorRef const &amp;ref, int thread_idx)'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_011d3637dbd8bc58bcb020b51bf57fbfc0.html#a90dedd03e47edabf0f9fd6d51e70272e',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, Alignment &gt;::RegularTileIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_011d3637dbd8bc58bcb020b51bf57fbfc0.html#aaa48072b3ce5b8753710ac6b8c4e99d7',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, Alignment &gt;::RegularTileIterator(TensorRef const &amp;ref, int thread_idx)'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01efd5013a2503d6567e2bf6b40c97360c.html#a7786e21804d73da02c2fce5be1b36c99',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::TensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element_))&gt;, AdvanceRank, ThreadMap_, Alignment &gt;::RegularTileIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_017982f81d4ef592e19c8427de2ea933a3.html#a87b0a8a59b5e46f5bcb761b1d8fd620e',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::ColumnMajorTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element_))&gt;, AdvanceRank, ThreadMap_, Alignment &gt;::RegularTileIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01c20d35180520077a5a09b1e33543c1a5.html#a8eff0cc46aa62dd1014eeaaccafdaec1',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::RowMajorTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element_))&gt;, AdvanceRank, ThreadMap_, Alignment &gt;::RegularTileIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_0197fef2242a3454a7d1cebe61aee28b43.html#a7bed1f96a9586042f4b69771da9c67e4',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::TensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::RegularTileIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_010889a732373c350de9b9a9f6c13cd761.html#a0295a753f239eab411f239cd75e05a91',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::ColumnMajorTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::RegularTileIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01a31b454d9c930525c1e9ca406a514f40.html#a55a9941eebde58406226a94aab1cdc37',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::RowMajorTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::RegularTileIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01f96bbeb63e6d4ce4a2551279de3a9f0e.html#a134de9f5c72872a443ce7a051df6f489',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::VoltaTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::RegularTileIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01793f74bfd8f116a827948ab01a37349a.html#af66b203bacaabdfe217f003dc256ba7a',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::ColumnMajorVoltaTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::RegularTileIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01f6f6511b5033cad31083644ac69c54d8.html#a2b42db145a79615c5eb6a35c92f06048',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::RowMajorVoltaTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::RegularTileIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01a75d2cd74e722d6ad6a3b41aabfd432d.html#aa72dc3870a62c972b4abdb085724d6a0',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::VoltaTensorOpMultiplicandBCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::RegularTileIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01187f8574e1fe9d7d5e8fbf09bd834bf0.html#a25253a14817a446458b519e368d41d11',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::ColumnMajorVoltaTensorOpMultiplicandBCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::RegularTileIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_0104ad31bd559a88cc418ae1cab7492ed5.html#a0d0e54f204b90ddf9af598655441864b',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::RowMajorVoltaTensorOpMultiplicandBCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::RegularTileIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01dbd6b8468d5bd787308d2f615a24d123.html#abc4b12b3974ae4de423d20ef902e3bf3',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::VoltaTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Shape_::kContiguous &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::RegularTileIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01bd31b3810c1fedf2e7e5959ff92b5d3d.html#ac2ccb3732d24e95444a186167dfc122d',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::ColumnMajorVoltaTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Shape_::kRow &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::RegularTileIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01b3fa5720e807697de61b9f937b269cd0.html#ab620e71608ef128668b2ac5cc4cb9abb',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::RowMajorVoltaTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Shape_::kColumn &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::RegularTileIterator()']]],
  ['regulartileiterator',['RegularTileIterator',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator.html',1,'cutlass::transform::threadblock']]],
  ['regulartileiterator2dthreadtile',['RegularTileIterator2dThreadTile',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator2dThreadTile_3_01Shape___00_01Ele76ed82829532ae1c17f4c78158f036c7.html#a5b5259960083255ea957de82f459d044',1,'cutlass::transform::threadblock::RegularTileIterator2dThreadTile&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, Alignment &gt;::RegularTileIterator2dThreadTile()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator2dThreadTile_3_01Shape___00_01Ele76ed82829532ae1c17f4c78158f036c7.html#acbf73cf231a03975f65ef557450bd66b',1,'cutlass::transform::threadblock::RegularTileIterator2dThreadTile&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, Alignment &gt;::RegularTileIterator2dThreadTile(TensorRef const &amp;ref, int thread_idx, int interleave)'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator2dThreadTile_3_01Shape___00_01Ele654c8f6161ae5340f040397a4e2e045c.html#abc31b5fb85bf3f84818ec89fca787362',1,'cutlass::transform::threadblock::RegularTileIterator2dThreadTile&lt; Shape_, Element_, layout::RowMajorInterleaved&lt; 4 &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::RegularTileIterator2dThreadTile()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator2dThreadTile_3_01Shape___00_01Ele654c8f6161ae5340f040397a4e2e045c.html#ab0ce7f8f5fbd795cd8d50e9f9191705e',1,'cutlass::transform::threadblock::RegularTileIterator2dThreadTile&lt; Shape_, Element_, layout::RowMajorInterleaved&lt; 4 &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::RegularTileIterator2dThreadTile(TensorRef const &amp;ref, int thread_idx)'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator2dThreadTile_3_01Shape___00_01Eleb60d066756d1c18f05fceee6a27bdb8a.html#aedc469dde216ca1b0471c1008b0662da',1,'cutlass::transform::threadblock::RegularTileIterator2dThreadTile&lt; Shape_, Element_, layout::ColumnMajorInterleaved&lt; 4 &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::RegularTileIterator2dThreadTile()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator2dThreadTile_3_01Shape___00_01Eleb60d066756d1c18f05fceee6a27bdb8a.html#addb68f2d9c0b94ff324c77adfc516eb6',1,'cutlass::transform::threadblock::RegularTileIterator2dThreadTile&lt; Shape_, Element_, layout::ColumnMajorInterleaved&lt; 4 &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::RegularTileIterator2dThreadTile(TensorRef const &amp;ref, int thread_idx)']]],
  ['regulartileiterator2dthreadtile',['RegularTileIterator2dThreadTile',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator2dThreadTile.html',1,'cutlass::transform::threadblock']]],
  ['regulartileiterator2dthreadtile_3c_20layout_3a_3apitchlinearshape_3c_20shape_3a_3akcolumn_2c_20shape_3a_3akrow_20_3e_2c_20element_2c_20layout_3a_3apitchlinear_2c_28kadvancerank_3d_3d0_3f1_3a0_29_2c_20threadmap_2c_20kalignment_20_3e',['RegularTileIterator2dThreadTile&lt; layout::PitchLinearShape&lt; Shape::kColumn, Shape::kRow &gt;, Element, layout::PitchLinear,(kAdvanceRank==0?1:0), ThreadMap, kAlignment &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator2dThreadTile.html',1,'cutlass::transform::threadblock']]],
  ['regulartileiterator2dthreadtile_3c_20layout_3a_3apitchlinearshape_3c_20shape_3a_3akrow_2c_20shape_3a_3akcolumn_20_3e_2c_20element_2c_20layout_3a_3apitchlinear_2c_28kadvancerank_3d_3d0_3f0_3a1_29_2c_20threadmap_20_3e',['RegularTileIterator2dThreadTile&lt; layout::PitchLinearShape&lt; Shape::kRow, Shape::kColumn &gt;, Element, layout::PitchLinear,(kAdvanceRank==0?0:1), ThreadMap &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator2dThreadTile.html',1,'cutlass::transform::threadblock']]],
  ['regulartileiterator2dthreadtile_3c_20shape_5f_2c_20element_5f_2c_20layout_3a_3acolumnmajorinterleaved_3c_204_20_3e_2c_20advancerank_2c_20threadmap_5f_2c_20alignment_20_3e',['RegularTileIterator2dThreadTile&lt; Shape_, Element_, layout::ColumnMajorInterleaved&lt; 4 &gt;, AdvanceRank, ThreadMap_, Alignment &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator2dThreadTile_3_01Shape___00_01Eleb60d066756d1c18f05fceee6a27bdb8a.html',1,'cutlass::transform::threadblock']]],
  ['regulartileiterator2dthreadtile_3c_20shape_5f_2c_20element_5f_2c_20layout_3a_3apitchlinear_2c_20advancerank_2c_20threadmap_5f_2c_20alignment_20_3e',['RegularTileIterator2dThreadTile&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, Alignment &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator2dThreadTile_3_01Shape___00_01Ele76ed82829532ae1c17f4c78158f036c7.html',1,'cutlass::transform::threadblock']]],
  ['regulartileiterator2dthreadtile_3c_20shape_5f_2c_20element_5f_2c_20layout_3a_3arowmajorinterleaved_3c_204_20_3e_2c_20advancerank_2c_20threadmap_5f_2c_20alignment_20_3e',['RegularTileIterator2dThreadTile&lt; Shape_, Element_, layout::RowMajorInterleaved&lt; 4 &gt;, AdvanceRank, ThreadMap_, Alignment &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator2dThreadTile_3_01Shape___00_01Ele654c8f6161ae5340f040397a4e2e045c.html',1,'cutlass::transform::threadblock']]],
  ['regulartileiterator_3c_20layout_3a_3apitchlinearshape_3c_20shape_3a_3akcolumn_2c_20shape_3a_3akrow_20_3e_2c_20element_2c_20layout_3a_3apitchlinear_2c_28kadvancerank_3d_3d0_3f1_3a0_29_2c_20threadmap_2c_20kalignment_20_3e',['RegularTileIterator&lt; layout::PitchLinearShape&lt; Shape::kColumn, Shape::kRow &gt;, Element, layout::PitchLinear,(kAdvanceRank==0?1:0), ThreadMap, kAlignment &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator.html',1,'cutlass::transform::threadblock']]],
  ['regulartileiterator_3c_20layout_3a_3apitchlinearshape_3c_20shape_3a_3akcolumn_2c_20shape_3a_3akrow_20_3e_2c_20element_2c_20layout_3a_3atensoropmultiplicandcongruous_3c_20sizeof_5fbits_3c_20element_5f_20_3e_3a_3avalue_2c_20int_28128_2fsizeof_28element_29_29_3e_2c_28kadvancerank_3d_3d0_3f1_3a0_29_2c_20threadmap_5f_20_3e',['RegularTileIterator&lt; layout::PitchLinearShape&lt; Shape::kColumn, Shape::kRow &gt;, Element, layout::TensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element))&gt;,(kAdvanceRank==0?1:0), ThreadMap_ &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator.html',1,'cutlass::transform::threadblock']]],
  ['regulartileiterator_3c_20layout_3a_3apitchlinearshape_3c_20shape_3a_3akcolumn_2c_20shape_3a_3akrow_20_3e_2c_20element_2c_20layout_3a_3atensoropmultiplicandcrosswise_3c_20sizeof_5fbits_3c_20element_5f_20_3e_3a_3avalue_2c_20crosswise_20_3e_2c_28kadvancerank_3d_3d0_3f1_3a0_29_2c_20threadmap_5f_20_3e',['RegularTileIterator&lt; layout::PitchLinearShape&lt; Shape::kColumn, Shape::kRow &gt;, Element, layout::TensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;,(kAdvanceRank==0?1:0), ThreadMap_ &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator.html',1,'cutlass::transform::threadblock']]],
  ['regulartileiterator_3c_20layout_3a_3apitchlinearshape_3c_20shape_3a_3akcolumn_2c_20shape_3a_3akrow_20_3e_2c_20element_2c_20layout_3a_3avoltatensoropmultiplicandbcongruous_3c_20sizeof_5fbits_3c_20element_5f_20_3e_3a_3avalue_20_3e_2c_28kadvancerank_3d_3d0_3f1_3a0_29_2c_20threadmap_5f_20_3e',['RegularTileIterator&lt; layout::PitchLinearShape&lt; Shape::kColumn, Shape::kRow &gt;, Element, layout::VoltaTensorOpMultiplicandBCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;,(kAdvanceRank==0?1:0), ThreadMap_ &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator.html',1,'cutlass::transform::threadblock']]],
  ['regulartileiterator_3c_20layout_3a_3apitchlinearshape_3c_20shape_3a_3akcolumn_2c_20shape_3a_3akrow_20_3e_2c_20element_2c_20layout_3a_3avoltatensoropmultiplicandcongruous_3c_20sizeof_5fbits_3c_20element_5f_20_3e_3a_3avalue_20_3e_2c_28kadvancerank_3d_3d0_3f1_3a0_29_2c_20threadmap_5f_20_3e',['RegularTileIterator&lt; layout::PitchLinearShape&lt; Shape::kColumn, Shape::kRow &gt;, Element, layout::VoltaTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;,(kAdvanceRank==0?1:0), ThreadMap_ &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator.html',1,'cutlass::transform::threadblock']]],
  ['regulartileiterator_3c_20layout_3a_3apitchlinearshape_3c_20shape_3a_3akcolumn_2c_20shape_3a_3akrow_20_3e_2c_20element_2c_20layout_3a_3avoltatensoropmultiplicandcrosswise_3c_20sizeof_5fbits_3c_20element_5f_20_3e_3a_3avalue_2c_20shape_3a_3akcolumn_20_3e_2c_28kadvancerank_3d_3d0_3f1_3a0_29_2c_20threadmap_5f_20_3e',['RegularTileIterator&lt; layout::PitchLinearShape&lt; Shape::kColumn, Shape::kRow &gt;, Element, layout::VoltaTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Shape::kColumn &gt;,(kAdvanceRank==0?1:0), ThreadMap_ &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator.html',1,'cutlass::transform::threadblock']]],
  ['regulartileiterator_3c_20layout_3a_3apitchlinearshape_3c_20shape_3a_3akrow_2c_20shape_3a_3akcolumn_20_3e_2c_20element_2c_20layout_3a_3apitchlinear_2c_28kadvancerank_3d_3d0_3f0_3a1_29_2c_20threadmap_20_3e',['RegularTileIterator&lt; layout::PitchLinearShape&lt; Shape::kRow, Shape::kColumn &gt;, Element, layout::PitchLinear,(kAdvanceRank==0?0:1), ThreadMap &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator.html',1,'cutlass::transform::threadblock']]],
  ['regulartileiterator_3c_20layout_3a_3apitchlinearshape_3c_20shape_3a_3akrow_2c_20shape_3a_3akcolumn_20_3e_2c_20element_2c_20layout_3a_3atensoropmultiplicandcongruous_3c_20sizeof_5fbits_3c_20element_5f_20_3e_3a_3avalue_2c_20int_28128_2fsizeof_28element_29_29_3e_2c_28kadvancerank_3d_3d0_3f0_3a1_29_2c_20threadmap_5f_20_3e',['RegularTileIterator&lt; layout::PitchLinearShape&lt; Shape::kRow, Shape::kColumn &gt;, Element, layout::TensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element))&gt;,(kAdvanceRank==0?0:1), ThreadMap_ &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator.html',1,'cutlass::transform::threadblock']]],
  ['regulartileiterator_3c_20layout_3a_3apitchlinearshape_3c_20shape_3a_3akrow_2c_20shape_3a_3akcolumn_20_3e_2c_20element_2c_20layout_3a_3atensoropmultiplicandcrosswise_3c_20sizeof_5fbits_3c_20element_5f_20_3e_3a_3avalue_2c_20crosswise_20_3e_2c_28kadvancerank_3d_3d0_3f0_3a1_29_2c_20threadmap_5f_20_3e',['RegularTileIterator&lt; layout::PitchLinearShape&lt; Shape::kRow, Shape::kColumn &gt;, Element, layout::TensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;,(kAdvanceRank==0?0:1), ThreadMap_ &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator.html',1,'cutlass::transform::threadblock']]],
  ['regulartileiterator_3c_20layout_3a_3apitchlinearshape_3c_20shape_3a_3akrow_2c_20shape_3a_3akcolumn_20_3e_2c_20element_2c_20layout_3a_3avoltatensoropmultiplicandbcongruous_3c_20sizeof_5fbits_3c_20element_5f_20_3e_3a_3avalue_20_3e_2c_28kadvancerank_3d_3d0_3f0_3a1_29_2c_20threadmap_5f_20_3e',['RegularTileIterator&lt; layout::PitchLinearShape&lt; Shape::kRow, Shape::kColumn &gt;, Element, layout::VoltaTensorOpMultiplicandBCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;,(kAdvanceRank==0?0:1), ThreadMap_ &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator.html',1,'cutlass::transform::threadblock']]],
  ['regulartileiterator_3c_20layout_3a_3apitchlinearshape_3c_20shape_3a_3akrow_2c_20shape_3a_3akcolumn_20_3e_2c_20element_2c_20layout_3a_3avoltatensoropmultiplicandcongruous_3c_20sizeof_5fbits_3c_20element_5f_20_3e_3a_3avalue_20_3e_2c_28kadvancerank_3d_3d0_3f0_3a1_29_2c_20threadmap_5f_20_3e',['RegularTileIterator&lt; layout::PitchLinearShape&lt; Shape::kRow, Shape::kColumn &gt;, Element, layout::VoltaTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;,(kAdvanceRank==0?0:1), ThreadMap_ &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator.html',1,'cutlass::transform::threadblock']]],
  ['regulartileiterator_3c_20layout_3a_3apitchlinearshape_3c_20shape_3a_3akrow_2c_20shape_3a_3akcolumn_20_3e_2c_20element_2c_20layout_3a_3avoltatensoropmultiplicandcrosswise_3c_20sizeof_5fbits_3c_20element_5f_20_3e_3a_3avalue_2c_20shape_3a_3akrow_20_3e_2c_28kadvancerank_3d_3d0_3f0_3a1_29_2c_20threadmap_5f_20_3e',['RegularTileIterator&lt; layout::PitchLinearShape&lt; Shape::kRow, Shape::kColumn &gt;, Element, layout::VoltaTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Shape::kRow &gt;,(kAdvanceRank==0?0:1), ThreadMap_ &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator.html',1,'cutlass::transform::threadblock']]],
  ['regulartileiterator_3c_20shape_5f_2c_20element_5f_2c_20layout_3a_3acolumnmajor_2c_20advancerank_2c_20threadmap_5f_2c_20alignment_20_3e',['RegularTileIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, Alignment &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_011d3637dbd8bc58bcb020b51bf57fbfc0.html',1,'cutlass::transform::threadblock']]],
  ['regulartileiterator_3c_20shape_5f_2c_20element_5f_2c_20layout_3a_3acolumnmajortensoropmultiplicandcongruous_3c_20sizeof_5fbits_3c_20element_5f_20_3e_3a_3avalue_2c_20int_28128_2fsizeof_28element_5f_29_29_3e_2c_20advancerank_2c_20threadmap_5f_2c_20alignment_20_3e',['RegularTileIterator&lt; Shape_, Element_, layout::ColumnMajorTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element_))&gt;, AdvanceRank, ThreadMap_, Alignment &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_017982f81d4ef592e19c8427de2ea933a3.html',1,'cutlass::transform::threadblock']]],
  ['regulartileiterator_3c_20shape_5f_2c_20element_5f_2c_20layout_3a_3acolumnmajortensoropmultiplicandcrosswise_3c_20sizeof_5fbits_3c_20element_5f_20_3e_3a_3avalue_2c_20crosswise_20_3e_2c_20advancerank_2c_20threadmap_5f_2c_20alignment_20_3e',['RegularTileIterator&lt; Shape_, Element_, layout::ColumnMajorTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, AdvanceRank, ThreadMap_, Alignment &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_010889a732373c350de9b9a9f6c13cd761.html',1,'cutlass::transform::threadblock']]],
  ['regulartileiterator_3c_20shape_5f_2c_20element_5f_2c_20layout_3a_3acolumnmajorvoltatensoropmultiplicandbcongruous_3c_20sizeof_5fbits_3c_20element_5f_20_3e_3a_3avalue_20_3e_2c_20advancerank_2c_20threadmap_5f_2c_20alignment_20_3e',['RegularTileIterator&lt; Shape_, Element_, layout::ColumnMajorVoltaTensorOpMultiplicandBCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;, AdvanceRank, ThreadMap_, Alignment &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01187f8574e1fe9d7d5e8fbf09bd834bf0.html',1,'cutlass::transform::threadblock']]],
  ['regulartileiterator_3c_20shape_5f_2c_20element_5f_2c_20layout_3a_3acolumnmajorvoltatensoropmultiplicandcongruous_3c_20sizeof_5fbits_3c_20element_5f_20_3e_3a_3avalue_20_3e_2c_20advancerank_2c_20threadmap_5f_2c_20alignment_20_3e',['RegularTileIterator&lt; Shape_, Element_, layout::ColumnMajorVoltaTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;, AdvanceRank, ThreadMap_, Alignment &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01793f74bfd8f116a827948ab01a37349a.html',1,'cutlass::transform::threadblock']]],
  ['regulartileiterator_3c_20shape_5f_2c_20element_5f_2c_20layout_3a_3acolumnmajorvoltatensoropmultiplicandcrosswise_3c_20sizeof_5fbits_3c_20element_5f_20_3e_3a_3avalue_2c_20shape_5f_3a_3akrow_20_3e_2c_20advancerank_2c_20threadmap_5f_2c_20alignment_20_3e',['RegularTileIterator&lt; Shape_, Element_, layout::ColumnMajorVoltaTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Shape_::kRow &gt;, AdvanceRank, ThreadMap_, Alignment &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01bd31b3810c1fedf2e7e5959ff92b5d3d.html',1,'cutlass::transform::threadblock']]],
  ['regulartileiterator_3c_20shape_5f_2c_20element_5f_2c_20layout_3a_3apitchlinear_2c_20advancerank_2c_20threadmap_5f_2c_20alignment_20_3e',['RegularTileIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, Alignment &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_0184a89653916f5d51ab59d1b386989a17.html',1,'cutlass::transform::threadblock']]],
  ['regulartileiterator_3c_20shape_5f_2c_20element_5f_2c_20layout_3a_3arowmajor_2c_20advancerank_2c_20threadmap_5f_2c_20alignment_20_3e',['RegularTileIterator&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, Alignment &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_0149454d361ea5885cf5166a920b5145df.html',1,'cutlass::transform::threadblock']]],
  ['regulartileiterator_3c_20shape_5f_2c_20element_5f_2c_20layout_3a_3arowmajortensoropmultiplicandcongruous_3c_20sizeof_5fbits_3c_20element_5f_20_3e_3a_3avalue_2c_20int_28128_2fsizeof_28element_5f_29_29_3e_2c_20advancerank_2c_20threadmap_5f_2c_20alignment_20_3e',['RegularTileIterator&lt; Shape_, Element_, layout::RowMajorTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element_))&gt;, AdvanceRank, ThreadMap_, Alignment &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01c20d35180520077a5a09b1e33543c1a5.html',1,'cutlass::transform::threadblock']]],
  ['regulartileiterator_3c_20shape_5f_2c_20element_5f_2c_20layout_3a_3arowmajortensoropmultiplicandcrosswise_3c_20sizeof_5fbits_3c_20element_5f_20_3e_3a_3avalue_2c_20crosswise_20_3e_2c_20advancerank_2c_20threadmap_5f_2c_20alignment_20_3e',['RegularTileIterator&lt; Shape_, Element_, layout::RowMajorTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, AdvanceRank, ThreadMap_, Alignment &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01a31b454d9c930525c1e9ca406a514f40.html',1,'cutlass::transform::threadblock']]],
  ['regulartileiterator_3c_20shape_5f_2c_20element_5f_2c_20layout_3a_3arowmajorvoltatensoropmultiplicandbcongruous_3c_20sizeof_5fbits_3c_20element_5f_20_3e_3a_3avalue_20_3e_2c_20advancerank_2c_20threadmap_5f_2c_20alignment_20_3e',['RegularTileIterator&lt; Shape_, Element_, layout::RowMajorVoltaTensorOpMultiplicandBCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;, AdvanceRank, ThreadMap_, Alignment &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_0104ad31bd559a88cc418ae1cab7492ed5.html',1,'cutlass::transform::threadblock']]],
  ['regulartileiterator_3c_20shape_5f_2c_20element_5f_2c_20layout_3a_3arowmajorvoltatensoropmultiplicandcongruous_3c_20sizeof_5fbits_3c_20element_5f_20_3e_3a_3avalue_20_3e_2c_20advancerank_2c_20threadmap_5f_2c_20alignment_20_3e',['RegularTileIterator&lt; Shape_, Element_, layout::RowMajorVoltaTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;, AdvanceRank, ThreadMap_, Alignment &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01f6f6511b5033cad31083644ac69c54d8.html',1,'cutlass::transform::threadblock']]],
  ['regulartileiterator_3c_20shape_5f_2c_20element_5f_2c_20layout_3a_3arowmajorvoltatensoropmultiplicandcrosswise_3c_20sizeof_5fbits_3c_20element_5f_20_3e_3a_3avalue_2c_20shape_5f_3a_3akcolumn_20_3e_2c_20advancerank_2c_20threadmap_5f_2c_20alignment_20_3e',['RegularTileIterator&lt; Shape_, Element_, layout::RowMajorVoltaTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Shape_::kColumn &gt;, AdvanceRank, ThreadMap_, Alignment &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01b3fa5720e807697de61b9f937b269cd0.html',1,'cutlass::transform::threadblock']]],
  ['regulartileiterator_3c_20shape_5f_2c_20element_5f_2c_20layout_3a_3atensoropmultiplicandcongruous_3c_20sizeof_5fbits_3c_20element_5f_20_3e_3a_3avalue_2c_20int_28128_2fsizeof_28element_5f_29_29_3e_2c_20advancerank_2c_20threadmap_5f_2c_20alignment_20_3e',['RegularTileIterator&lt; Shape_, Element_, layout::TensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element_))&gt;, AdvanceRank, ThreadMap_, Alignment &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01efd5013a2503d6567e2bf6b40c97360c.html',1,'cutlass::transform::threadblock']]],
  ['regulartileiterator_3c_20shape_5f_2c_20element_5f_2c_20layout_3a_3atensoropmultiplicandcrosswise_3c_20sizeof_5fbits_3c_20element_5f_20_3e_3a_3avalue_2c_20crosswise_20_3e_2c_20advancerank_2c_20threadmap_5f_2c_20alignment_20_3e',['RegularTileIterator&lt; Shape_, Element_, layout::TensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, AdvanceRank, ThreadMap_, Alignment &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_0197fef2242a3454a7d1cebe61aee28b43.html',1,'cutlass::transform::threadblock']]],
  ['regulartileiterator_3c_20shape_5f_2c_20element_5f_2c_20layout_3a_3avoltatensoropmultiplicandbcongruous_3c_20sizeof_5fbits_3c_20element_5f_20_3e_3a_3avalue_20_3e_2c_20advancerank_2c_20threadmap_5f_2c_20alignment_20_3e',['RegularTileIterator&lt; Shape_, Element_, layout::VoltaTensorOpMultiplicandBCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;, AdvanceRank, ThreadMap_, Alignment &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01a75d2cd74e722d6ad6a3b41aabfd432d.html',1,'cutlass::transform::threadblock']]],
  ['regulartileiterator_3c_20shape_5f_2c_20element_5f_2c_20layout_3a_3avoltatensoropmultiplicandcongruous_3c_20sizeof_5fbits_3c_20element_5f_20_3e_3a_3avalue_20_3e_2c_20advancerank_2c_20threadmap_5f_2c_20alignment_20_3e',['RegularTileIterator&lt; Shape_, Element_, layout::VoltaTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;, AdvanceRank, ThreadMap_, Alignment &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01f96bbeb63e6d4ce4a2551279de3a9f0e.html',1,'cutlass::transform::threadblock']]],
  ['regulartileiterator_3c_20shape_5f_2c_20element_5f_2c_20layout_3a_3avoltatensoropmultiplicandcrosswise_3c_20sizeof_5fbits_3c_20element_5f_20_3e_3a_3avalue_2c_20shape_5f_3a_3akcontiguous_20_3e_2c_20advancerank_2c_20threadmap_5f_2c_20alignment_20_3e',['RegularTileIterator&lt; Shape_, Element_, layout::VoltaTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Shape_::kContiguous &gt;, AdvanceRank, ThreadMap_, Alignment &gt;',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01dbd6b8468d5bd787308d2f615a24d123.html',1,'cutlass::transform::threadblock']]],
  ['relatively_5fequal',['relatively_equal',['../namespacecutlass.html#aebc97e25721b38692159a6d50d545fe0',1,'cutlass']]],
  ['relatively_5fequal_2eh',['relatively_equal.h',['../relatively__equal_8h.html',1,'']]],
  ['relatively_5fequal_3c_20double_20_3e',['relatively_equal&lt; double &gt;',['../namespacecutlass.html#ad5021c2735e27a869a571026bc9c6c7f',1,'cutlass']]],
  ['relatively_5fequal_3c_20float_20_3e',['relatively_equal&lt; float &gt;',['../namespacecutlass.html#a630d3b4208f2c6fd8a5d2a6dcdd990aa',1,'cutlass']]],
  ['relatively_5fequal_3c_20half_5ft_20_3e',['relatively_equal&lt; half_t &gt;',['../namespacecutlass.html#a26d02a5d1ffaf25731c3d6fb09ee1990',1,'cutlass']]],
  ['relatively_5fequal_3c_20int16_5ft_20_3e',['relatively_equal&lt; int16_t &gt;',['../namespacecutlass.html#aec96b8d17c5dc5c274fac2a3f94aecab',1,'cutlass']]],
  ['relatively_5fequal_3c_20int32_5ft_20_3e',['relatively_equal&lt; int32_t &gt;',['../namespacecutlass.html#ad7ce0f4e7abaf531c187dc535db935d4',1,'cutlass']]],
  ['relatively_5fequal_3c_20int4b_5ft_20_3e',['relatively_equal&lt; int4b_t &gt;',['../namespacecutlass.html#af7c5c3f6da119eafdde3244d7cf56797',1,'cutlass']]],
  ['relatively_5fequal_3c_20int64_5ft_20_3e',['relatively_equal&lt; int64_t &gt;',['../namespacecutlass.html#a9f5f21efb4b2490f8bf51a9eeee3e24f',1,'cutlass']]],
  ['relatively_5fequal_3c_20int8_5ft_20_3e',['relatively_equal&lt; int8_t &gt;',['../namespacecutlass.html#a8ba332bb1c717a6d13c2ad9287cfe5cc',1,'cutlass']]],
  ['relatively_5fequal_3c_20uint16_5ft_20_3e',['relatively_equal&lt; uint16_t &gt;',['../namespacecutlass.html#a1a6c0b9fec0db0628b085c1b3021eb0f',1,'cutlass']]],
  ['relatively_5fequal_3c_20uint1b_5ft_20_3e',['relatively_equal&lt; uint1b_t &gt;',['../namespacecutlass.html#af61f11e5381980cae375cf02893f102e',1,'cutlass']]],
  ['relatively_5fequal_3c_20uint32_5ft_20_3e',['relatively_equal&lt; uint32_t &gt;',['../namespacecutlass.html#aa869ccb365e73fa0c00b272b8e64eef2',1,'cutlass']]],
  ['relatively_5fequal_3c_20uint4b_5ft_20_3e',['relatively_equal&lt; uint4b_t &gt;',['../namespacecutlass.html#ace93a25aa5f3ba5318465362cd5be2a1',1,'cutlass']]],
  ['relatively_5fequal_3c_20uint64_5ft_20_3e',['relatively_equal&lt; uint64_t &gt;',['../namespacecutlass.html#a8f182001e54b8704d0cfc2e7b1456faf',1,'cutlass']]],
  ['relatively_5fequal_3c_20uint8_5ft_20_3e',['relatively_equal&lt; uint8_t &gt;',['../namespacecutlass.html#a301593d45e08bc8fa84a189261593879',1,'cutlass']]],
  ['relatively_5fequal_5ffloat',['relatively_equal_float',['../namespacecutlass_1_1detail.html#a761bb4b7b448dbc213d537ef3ded340c',1,'cutlass::detail']]],
  ['release',['release',['../classcutlass_1_1platform_1_1unique__ptr.html#a7ac06ebe7bc66573d3225891e12d2279',1,'cutlass::platform::unique_ptr::release()'],['../classcutlass_1_1Semaphore.html#a04e893ba5a9ddb20e1b3c6475771c0e9',1,'cutlass::Semaphore::release()'],['../structcutlass_1_1device__memory_1_1allocation.html#a138bc02627aab06ac09c73da67f06f7a',1,'cutlass::device_memory::allocation::release()'],['../classcutlass_1_1library_1_1Manifest.html#a05c62e11e5face82a64f40e7547d1ac5',1,'cutlass::library::Manifest::release()']]],
  ['rematerializeblockdimx',['RematerializeBlockDimX',['../namespacecutlass_1_1gemm_1_1threadblock.html#a846857170e9a0410739a3594748e74b0',1,'cutlass::gemm::threadblock']]],
  ['rematerializeblockdimy',['RematerializeBlockDimY',['../namespacecutlass_1_1gemm_1_1threadblock.html#a339ef715a891ee08c49b5e6cfeba5b82',1,'cutlass::gemm::threadblock']]],
  ['rematerializeblockdimz',['RematerializeBlockDimZ',['../namespacecutlass_1_1gemm_1_1threadblock.html#a423727aa969abf5b5366a3af2c6c2e1b',1,'cutlass::gemm::threadblock']]],
  ['rematerializeblockidxx',['RematerializeBlockIdxX',['../namespacecutlass_1_1gemm_1_1threadblock.html#ab7d937f18d3e5a4ec9ded385c7af507c',1,'cutlass::gemm::threadblock']]],
  ['rematerializeblockidxy',['RematerializeBlockIdxY',['../namespacecutlass_1_1gemm_1_1threadblock.html#a4675f298898b2ca2517c5887a7c207c1',1,'cutlass::gemm::threadblock']]],
  ['rematerializeblockidxz',['RematerializeBlockIdxZ',['../namespacecutlass_1_1gemm_1_1threadblock.html#ac2ba2ad2fe61f2135bc25c6cbd1340eb',1,'cutlass::gemm::threadblock']]],
  ['rematerializethreadidxx',['RematerializeThreadIdxX',['../namespacecutlass_1_1gemm_1_1threadblock.html#a3deb0f9ea5e380cbf6f54f536165abc9',1,'cutlass::gemm::threadblock']]],
  ['rematerializethreadidxy',['RematerializeThreadIdxY',['../namespacecutlass_1_1gemm_1_1threadblock.html#a467d5ab320ef9ee5ee1f966165ba53b5',1,'cutlass::gemm::threadblock']]],
  ['rematerializethreadidxz',['RematerializeThreadIdxZ',['../namespacecutlass_1_1gemm_1_1threadblock.html#a4a8ce0c78fde28ed38481ce8f9b20999',1,'cutlass::gemm::threadblock']]],
  ['remove_5fconst',['remove_const',['../structcutlass_1_1platform_1_1remove__const.html',1,'cutlass::platform']]],
  ['remove_5fconst_3c_20const_20t_20_3e',['remove_const&lt; const T &gt;',['../structcutlass_1_1platform_1_1remove__const_3_01const_01T_01_4.html',1,'cutlass::platform']]],
  ['remove_5fcv',['remove_cv',['../structcutlass_1_1platform_1_1remove__cv.html',1,'cutlass::platform']]],
  ['remove_5fnegative_5fzero',['remove_negative_zero',['../structcutlass_1_1TypeTraits.html#acdf0f64b79e6b8c550e85376232723a8',1,'cutlass::TypeTraits::remove_negative_zero()'],['../structcutlass_1_1TypeTraits_3_01int8__t_01_4.html#acb1301812bda85b78dde745889aa0d98',1,'cutlass::TypeTraits&lt; int8_t &gt;::remove_negative_zero()'],['../structcutlass_1_1TypeTraits_3_01uint8__t_01_4.html#a11a6931adbdd24743a919c999f44eda9',1,'cutlass::TypeTraits&lt; uint8_t &gt;::remove_negative_zero()'],['../structcutlass_1_1TypeTraits_3_01int_01_4.html#a4fec4afa5e9b0aa26b7026e4345d1549',1,'cutlass::TypeTraits&lt; int &gt;::remove_negative_zero()'],['../structcutlass_1_1TypeTraits_3_01unsigned_01_4.html#ac596d039114e2c2618da81c0076faa7e',1,'cutlass::TypeTraits&lt; unsigned &gt;::remove_negative_zero()'],['../structcutlass_1_1TypeTraits_3_01int64__t_01_4.html#a5a3dbcca8e7766783c0bb55a20e3a822',1,'cutlass::TypeTraits&lt; int64_t &gt;::remove_negative_zero()'],['../structcutlass_1_1TypeTraits_3_01uint64__t_01_4.html#afeda28af3151a5de60ecb0de5b525dd1',1,'cutlass::TypeTraits&lt; uint64_t &gt;::remove_negative_zero()'],['../structcutlass_1_1TypeTraits_3_01half__t_01_4.html#afd4eed55e9957fabbf513c9556973e04',1,'cutlass::TypeTraits&lt; half_t &gt;::remove_negative_zero()'],['../structcutlass_1_1TypeTraits_3_01float_01_4.html#a7a81197b153f223cf985e3fae743d437',1,'cutlass::TypeTraits&lt; float &gt;::remove_negative_zero()'],['../structcutlass_1_1TypeTraits_3_01double_01_4.html#afa38cd9c83856851bf305e15e424b057',1,'cutlass::TypeTraits&lt; double &gt;::remove_negative_zero()'],['../structcutlass_1_1TypeTraits_3_01complex_3_01half__t_01_4_01_4.html#ac3cad0e8f771e936bfa2a64df50982dc',1,'cutlass::TypeTraits&lt; complex&lt; half_t &gt; &gt;::remove_negative_zero()'],['../structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4.html#a9c5132250bbcfbe3e7747277b875c6bf',1,'cutlass::TypeTraits&lt; complex&lt; float &gt; &gt;::remove_negative_zero()'],['../structcutlass_1_1TypeTraits_3_01complex_3_01double_01_4_01_4.html#a116d619c589a5c64c57bbe01d880962a',1,'cutlass::TypeTraits&lt; complex&lt; double &gt; &gt;::remove_negative_zero()']]],
  ['remove_5fvolatile',['remove_volatile',['../structcutlass_1_1platform_1_1remove__volatile.html',1,'cutlass::platform']]],
  ['remove_5fvolatile_3c_20volatile_20t_20_3e',['remove_volatile&lt; volatile T &gt;',['../structcutlass_1_1platform_1_1remove__volatile_3_01volatile_01T_01_4.html',1,'cutlass::platform']]],
  ['rend',['rend',['../classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a6081f288dfc7b60da8d00913be8e83db',1,'cutlass::Array&lt; T, N, true &gt;::rend()'],['../classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a39c08a75c7cc22fcd296e6c9fefe754e',1,'cutlass::Array&lt; T, N, false &gt;::rend()']]],
  ['reorder_5fcolumn',['reorder_column',['../namespacecutlass.html#a733a528d54290aae699f939e0ea6e247',1,'cutlass']]],
  ['reserve',['reserve',['../classcutlass_1_1HostTensor.html#a6405f217fc2a52f8e23180c9f8160899',1,'cutlass::HostTensor::reserve()'],['../classcutlass_1_1library_1_1Manifest.html#a9dc44049b0d4cd3331b07985bd5b527e',1,'cutlass::library::Manifest::reserve()']]],
  ['reset',['reset',['../classcutlass_1_1platform_1_1unique__ptr.html#a6740f71511f5495d6038cf8878862331',1,'cutlass::platform::unique_ptr::reset()'],['../classcutlass_1_1TensorRef.html#a9c2149162016bc19c7735b824d57eb9e',1,'cutlass::TensorRef::reset(Element *ptr=nullptr)'],['../classcutlass_1_1TensorRef.html#a1c539841446205c482fe18cc0a99d913',1,'cutlass::TensorRef::reset(Element *ptr, Layout const &amp;layout)'],['../classcutlass_1_1TensorView.html#add0de4f0548c957df00cae0ee0b9257c',1,'cutlass::TensorView::reset()'],['../structcutlass_1_1device__memory_1_1allocation.html#aeef3a1cb8cfb96a2823e85f7fa52a31a',1,'cutlass::device_memory::allocation::reset()'],['../structcutlass_1_1device__memory_1_1allocation.html#a325a56e50f82d0a1aa0425d3ae061405',1,'cutlass::device_memory::allocation::reset(T *_ptr, size_t _capacity)'],['../classcutlass_1_1HostTensor.html#a898fc427538275635a55e7633cd89140',1,'cutlass::HostTensor::reset()'],['../classcutlass_1_1HostTensor.html#a4f23ad5953f9cd5dff8e97d9edbc0cf0',1,'cutlass::HostTensor::reset(TensorCoord const &amp;extent, Layout const &amp;layout, bool device_backed_=true)'],['../classcutlass_1_1HostTensor.html#ab91b456881d5f9c0f52a4bc89d448d55',1,'cutlass::HostTensor::reset(TensorCoord const &amp;extent, bool device_backed_=true)']]],
  ['resize',['resize',['../classcutlass_1_1TensorView.html#a3c3417b9b777f103ec86607a7751e668',1,'cutlass::TensorView::resize()'],['../classcutlass_1_1HostTensor.html#ac61cee5b25585439f4ccc7d7249ee853',1,'cutlass::HostTensor::resize(TensorCoord const &amp;extent, Layout const &amp;layout, bool device_backed_=true)'],['../classcutlass_1_1HostTensor.html#a784d0b84de512c1bff131710226e2338',1,'cutlass::HostTensor::resize(TensorCoord const &amp;extent, bool device_backed_=true)']]],
  ['result',['result',['../structcutlass_1_1reference_1_1host_1_1detail_1_1TensorEqualsFunc.html#a71114c0e6c154a9cc3f5a1e380dfc454',1,'cutlass::reference::host::detail::TensorEqualsFunc']]],
  ['result_5ftype',['result_type',['../structcutlass_1_1NumericConverter.html#a46b1b5c0c96c50176578fcd6f915ee8c',1,'cutlass::NumericConverter::result_type()'],['../structcutlass_1_1NumericConverter_3_01int8__t_00_01float_00_01Round_01_4.html#acce8af4bfd5837006dc66803fee491ca',1,'cutlass::NumericConverter&lt; int8_t, float, Round &gt;::result_type()'],['../structcutlass_1_1NumericConverter_3_01T_00_01T_00_01Round_01_4.html#a10b599dbfd3f4da945dc3ddb93e48ded',1,'cutlass::NumericConverter&lt; T, T, Round &gt;::result_type()'],['../structcutlass_1_1NumericConverter_3_01float_00_01half__t_00_01Round_01_4.html#ac1adcd31b0db52e8682680c9927a05c8',1,'cutlass::NumericConverter&lt; float, half_t, Round &gt;::result_type()'],['../structcutlass_1_1NumericConverter_3_01half__t_00_01float_00_01FloatRoundStyle_1_1round__to__nearest_01_4.html#a5dc993f38c6eedd917008e6c839c6300',1,'cutlass::NumericConverter&lt; half_t, float, FloatRoundStyle::round_to_nearest &gt;::result_type()'],['../structcutlass_1_1NumericConverter_3_01half__t_00_01float_00_01FloatRoundStyle_1_1round__toward__zero_01_4.html#aa5bfe0288e538f1df94d74fa52aa1e17',1,'cutlass::NumericConverter&lt; half_t, float, FloatRoundStyle::round_toward_zero &gt;::result_type()'],['../structcutlass_1_1NumericConverterClamp.html#a8e13f626ae7a0ebb228bf1ef59d4ed09',1,'cutlass::NumericConverterClamp::result_type()'],['../structcutlass_1_1NumericArrayConverter.html#a5ed5dd75f4310d887fa527dd177c6f91',1,'cutlass::NumericArrayConverter::result_type()'],['../structcutlass_1_1NumericArrayConverter_3_01half__t_00_01float_00_012_00_01FloatRoundStyle_1_1round__to__nearest_01_4.html#ad50d5ce8c7047513745c1fab77c3988c',1,'cutlass::NumericArrayConverter&lt; half_t, float, 2, FloatRoundStyle::round_to_nearest &gt;::result_type()'],['../structcutlass_1_1NumericArrayConverter_3_01float_00_01half__t_00_012_00_01Round_01_4.html#af14ce6b66b30bd8942a6693e6a06c8f9',1,'cutlass::NumericArrayConverter&lt; float, half_t, 2, Round &gt;::result_type()'],['../structcutlass_1_1NumericArrayConverter_3_01half__t_00_01float_00_01N_00_01Round_01_4.html#a411c3cd15a3f03d360c96c05025cc3d3',1,'cutlass::NumericArrayConverter&lt; half_t, float, N, Round &gt;::result_type()'],['../structcutlass_1_1NumericArrayConverter_3_01float_00_01half__t_00_01N_00_01Round_01_4.html#a644b3f49bf10e99d6a53061b350ac693',1,'cutlass::NumericArrayConverter&lt; float, half_t, N, Round &gt;::result_type()']]],
  ['reverse_5fiterator',['reverse_iterator',['../classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4_1_1reverse__iterator.html',1,'cutlass::Array&lt; T, N, false &gt;']]],
  ['reverse_5fiterator',['reverse_iterator',['../classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1reverse__iterator.html',1,'cutlass::Array&lt; T, N, true &gt;']]],
  ['reverse_5fiterator',['reverse_iterator',['../classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1reverse__iterator.html#af95b8971433b4acec7e25c00167adb6a',1,'cutlass::Array&lt; T, N, true &gt;::reverse_iterator::reverse_iterator()'],['../classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1reverse__iterator.html#af5056ae2c80aadeb027fffb8ed35f719',1,'cutlass::Array&lt; T, N, true &gt;::reverse_iterator::reverse_iterator(T *_ptr)'],['../classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4_1_1reverse__iterator.html#a539eda60222f630592b9914b51307ea1',1,'cutlass::Array&lt; T, N, false &gt;::reverse_iterator::reverse_iterator()'],['../classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4_1_1reverse__iterator.html#a939c336c7c727748d9efcd5efa066a88',1,'cutlass::Array&lt; T, N, false &gt;::reverse_iterator::reverse_iterator(Storage *ptr, int idx=0)']]],
  ['rhs',['rhs',['../structcutlass_1_1reference_1_1host_1_1detail_1_1TensorEqualsFunc.html#a15a7b08ee45105dc3d40b86426824c4c',1,'cutlass::reference::host::detail::TensorEqualsFunc']]],
  ['rng_5fstate',['rng_state',['../structcutlass_1_1reference_1_1device_1_1detail_1_1RandomGaussianFunc.html#a52dd271db62c366ac41e84407b9176c3',1,'cutlass::reference::device::detail::RandomGaussianFunc::rng_state()'],['../structcutlass_1_1reference_1_1device_1_1detail_1_1RandomUniformFunc.html#aa802faaaf5a6b3f7a5725d26a9d45ef2',1,'cutlass::reference::device::detail::RandomUniformFunc::rng_state()']]],
  ['round_5ferror',['round_error',['../structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#ab0a036db7a1ad11c65e876020c78b1a5',1,'std::numeric_limits&lt; cutlass::half_t &gt;']]],
  ['round_5fhalf_5fulp_5ftruncate',['round_half_ulp_truncate',['../namespacecutlass.html#aabe6b8ce223bf05f65a4721a3f5447a6a3d484cc14220848a89c785aaaab88a98',1,'cutlass']]],
  ['round_5findeterminate',['round_indeterminate',['../namespacecutlass.html#aabe6b8ce223bf05f65a4721a3f5447a6a71ab01baac7b3594fa75191a065de802',1,'cutlass']]],
  ['round_5fnearest',['round_nearest',['../namespacecutlass.html#a17c8c408d672d26f1c70d2435f6ac83e',1,'cutlass']]],
  ['round_5fstyle',['round_style',['../structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#ab0af85c1d7c83ca03ed0c083fe22262f',1,'std::numeric_limits&lt; cutlass::half_t &gt;::round_style()'],['../structcutlass_1_1NumericConverter.html#a6060b5f316853f791fc87ef3bd180b1a',1,'cutlass::NumericConverter::round_style()'],['../structcutlass_1_1NumericConverter_3_01int8__t_00_01float_00_01Round_01_4.html#ae66740da44250e5360e4c3c874634310',1,'cutlass::NumericConverter&lt; int8_t, float, Round &gt;::round_style()'],['../structcutlass_1_1NumericConverter_3_01T_00_01T_00_01Round_01_4.html#a5b9a558d800872eb73723e5af3e7d4f3',1,'cutlass::NumericConverter&lt; T, T, Round &gt;::round_style()'],['../structcutlass_1_1NumericConverter_3_01float_00_01half__t_00_01Round_01_4.html#a59f158bedaecd4a6f0d50c7f1567538a',1,'cutlass::NumericConverter&lt; float, half_t, Round &gt;::round_style()'],['../structcutlass_1_1NumericConverter_3_01half__t_00_01float_00_01FloatRoundStyle_1_1round__to__nearest_01_4.html#affd38515f30c26256ff5c06e5a567080',1,'cutlass::NumericConverter&lt; half_t, float, FloatRoundStyle::round_to_nearest &gt;::round_style()'],['../structcutlass_1_1NumericConverter_3_01half__t_00_01float_00_01FloatRoundStyle_1_1round__toward__zero_01_4.html#a43fde0bc2ddeeebf1f188c6d1ac7fbe0',1,'cutlass::NumericConverter&lt; half_t, float, FloatRoundStyle::round_toward_zero &gt;::round_style()'],['../structcutlass_1_1NumericArrayConverter.html#a3bb36be77b3f0464ab7d1c52b16db6f7',1,'cutlass::NumericArrayConverter::round_style()'],['../structcutlass_1_1NumericArrayConverter_3_01half__t_00_01float_00_012_00_01FloatRoundStyle_1_1round__to__nearest_01_4.html#a3c31373beb0e6a9c649134b21a02125a',1,'cutlass::NumericArrayConverter&lt; half_t, float, 2, FloatRoundStyle::round_to_nearest &gt;::round_style()'],['../structcutlass_1_1NumericArrayConverter_3_01float_00_01half__t_00_012_00_01Round_01_4.html#a07700d779e1dbfc62f29157e06073f79',1,'cutlass::NumericArrayConverter&lt; float, half_t, 2, Round &gt;::round_style()'],['../structcutlass_1_1NumericArrayConverter_3_01half__t_00_01float_00_01N_00_01Round_01_4.html#ac5060dfecda3f5a29600cfbd2c01def5',1,'cutlass::NumericArrayConverter&lt; half_t, float, N, Round &gt;::round_style()'],['../structcutlass_1_1NumericArrayConverter_3_01float_00_01half__t_00_01N_00_01Round_01_4.html#a5ae106211f42197a8f39a2f037ab6bba',1,'cutlass::NumericArrayConverter&lt; float, half_t, N, Round &gt;::round_style()']]],
  ['round_5fto_5fnearest',['round_to_nearest',['../namespacecutlass.html#aabe6b8ce223bf05f65a4721a3f5447a6a280b2492e1afdb76d63ad6a4cd4f38d7',1,'cutlass']]],
  ['round_5ftoward_5finfinity',['round_toward_infinity',['../namespacecutlass.html#aabe6b8ce223bf05f65a4721a3f5447a6a5202e179145325d6e7b16dc66351e729',1,'cutlass']]],
  ['round_5ftoward_5fneg_5finfinity',['round_toward_neg_infinity',['../namespacecutlass.html#aabe6b8ce223bf05f65a4721a3f5447a6ab8d591c42fa5ce43cc43e50153ec8a1b',1,'cutlass']]],
  ['round_5ftoward_5fzero',['round_toward_zero',['../namespacecutlass.html#aabe6b8ce223bf05f65a4721a3f5447a6a85e71d1c7477469da0e44a8cf7a644fa',1,'cutlass']]],
  ['row',['row',['../structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03',1,'cutlass::MatrixCoord::row() const '],['../structcutlass_1_1MatrixCoord.html#a67f3102e51abad1205e8a3450e7a6c7e',1,'cutlass::MatrixCoord::row()']]],
  ['rowarrangement',['RowArrangement',['../structcutlass_1_1epilogue_1_1threadblock_1_1detail_1_1RowArrangement.html',1,'cutlass::epilogue::threadblock::detail']]],
  ['rowarrangement',['RowArrangement',['../structcutlass_1_1epilogue_1_1threadblock_1_1OutputTileOptimalThreadMap_1_1Detail.html#a028dd493fdfc53f5189aa9936e170941',1,'cutlass::epilogue::threadblock::OutputTileOptimalThreadMap::Detail']]],
  ['rowarrangement_3c_20shape_2c_20warpsremaining_2c_20elementsperaccess_2c_20elementsize_2c_20false_20_3e',['RowArrangement&lt; Shape, WarpsRemaining, ElementsPerAccess, ElementSize, false &gt;',['../structcutlass_1_1epilogue_1_1threadblock_1_1detail_1_1RowArrangement_3_01Shape_00_01WarpsRemaini91159e6f7e123d881e3ec45101fa4f81.html',1,'cutlass::epilogue::threadblock::detail']]],
  ['rowarrangement_3c_20shape_2c_20warpsremaining_2c_20elementsperaccess_2c_20elementsize_2c_20true_20_3e',['RowArrangement&lt; Shape, WarpsRemaining, ElementsPerAccess, ElementSize, true &gt;',['../structcutlass_1_1epilogue_1_1threadblock_1_1detail_1_1RowArrangement_3_01Shape_00_01WarpsRemaini6d8790249bf12cac580da73bb37eb791.html',1,'cutlass::epilogue::threadblock::detail']]],
  ['rowmajor',['RowMajor',['../classcutlass_1_1layout_1_1RowMajor.html',1,'cutlass::layout']]],
  ['rowmajor',['RowMajor',['../classcutlass_1_1layout_1_1RowMajor.html#afdf845a439366f97f2bece7a6f9f03c4',1,'cutlass::layout::RowMajor::RowMajor(Index ldm=0)'],['../classcutlass_1_1layout_1_1RowMajor.html#a1fe768ae500e1960013c2cb95e58dce8',1,'cutlass::layout::RowMajor::RowMajor(Stride stride)']]],
  ['rowmajorblocklinear',['RowMajorBlockLinear',['../structcutlass_1_1layout_1_1RowMajorBlockLinear.html',1,'cutlass::layout']]],
  ['rowmajorblocklinear',['RowMajorBlockLinear',['../structcutlass_1_1layout_1_1RowMajorBlockLinear.html#ac9c5464ff08cbf9a00a9bd6e87b4367e',1,'cutlass::layout::RowMajorBlockLinear']]],
  ['rowmajorinterleaved',['RowMajorInterleaved',['../structcutlass_1_1layout_1_1RowMajorInterleaved.html#a9a965603dd8845191726450514c3463b',1,'cutlass::layout::RowMajorInterleaved::RowMajorInterleaved(Index ldm=0)'],['../structcutlass_1_1layout_1_1RowMajorInterleaved.html#a52b330ac2b80e6e38dfde427114baeea',1,'cutlass::layout::RowMajorInterleaved::RowMajorInterleaved(Stride stride)']]],
  ['rowmajorinterleaved',['RowMajorInterleaved',['../structcutlass_1_1layout_1_1RowMajorInterleaved.html',1,'cutlass::layout']]],
  ['rowmajorinterleaved_3c_204_20_3e',['RowMajorInterleaved&lt; 4 &gt;',['../structcutlass_1_1layout_1_1RowMajorInterleaved.html',1,'cutlass::layout']]],
  ['rowmajortensoropmultiplicandcongruous',['RowMajorTensorOpMultiplicandCongruous',['../structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html',1,'cutlass::layout']]],
  ['rowmajortensoropmultiplicandcongruous',['RowMajorTensorOpMultiplicandCongruous',['../structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#acd7ea2b943914f72f0040b208915aa89',1,'cutlass::layout::RowMajorTensorOpMultiplicandCongruous::RowMajorTensorOpMultiplicandCongruous(Index ldm=0)'],['../structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCongruous.html#a863ff7fbc770524406c5f3a72ec4e919',1,'cutlass::layout::RowMajorTensorOpMultiplicandCongruous::RowMajorTensorOpMultiplicandCongruous(Stride stride)']]],
  ['rowmajortensoropmultiplicandcrosswise',['RowMajorTensorOpMultiplicandCrosswise',['../structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#a90513da18bdcebc20f673d83031551f9',1,'cutlass::layout::RowMajorTensorOpMultiplicandCrosswise::RowMajorTensorOpMultiplicandCrosswise(Index ldm=0)'],['../structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html#afe4b1ec877e6f1266e4e57a862d43767',1,'cutlass::layout::RowMajorTensorOpMultiplicandCrosswise::RowMajorTensorOpMultiplicandCrosswise(Stride stride)']]],
  ['rowmajortensoropmultiplicandcrosswise',['RowMajorTensorOpMultiplicandCrosswise',['../structcutlass_1_1layout_1_1RowMajorTensorOpMultiplicandCrosswise.html',1,'cutlass::layout']]],
  ['rowmajorvoltatensoropmultiplicandbcongruous',['RowMajorVoltaTensorOpMultiplicandBCongruous',['../structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html',1,'cutlass::layout']]],
  ['rowmajorvoltatensoropmultiplicandbcongruous',['RowMajorVoltaTensorOpMultiplicandBCongruous',['../structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#aad4ca016701d73fee071bf10c35219ab',1,'cutlass::layout::RowMajorVoltaTensorOpMultiplicandBCongruous::RowMajorVoltaTensorOpMultiplicandBCongruous(Index ldm=0)'],['../structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#ab6c4659f6f6a893ae6327964158a762b',1,'cutlass::layout::RowMajorVoltaTensorOpMultiplicandBCongruous::RowMajorVoltaTensorOpMultiplicandBCongruous(Stride stride)']]],
  ['rowmajorvoltatensoropmultiplicandcongruous',['RowMajorVoltaTensorOpMultiplicandCongruous',['../structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html',1,'cutlass::layout']]],
  ['rowmajorvoltatensoropmultiplicandcongruous',['RowMajorVoltaTensorOpMultiplicandCongruous',['../structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#acefc319105f8150e6005d2a1a7ce9366',1,'cutlass::layout::RowMajorVoltaTensorOpMultiplicandCongruous::RowMajorVoltaTensorOpMultiplicandCongruous(Index ldm=0)'],['../structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#a3b857440e261091eef2cccb93608c54d',1,'cutlass::layout::RowMajorVoltaTensorOpMultiplicandCongruous::RowMajorVoltaTensorOpMultiplicandCongruous(Stride stride)']]],
  ['rowmajorvoltatensoropmultiplicandcrosswise',['RowMajorVoltaTensorOpMultiplicandCrosswise',['../structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#a602a5e3cd52edc8c6e733cc8ea271484',1,'cutlass::layout::RowMajorVoltaTensorOpMultiplicandCrosswise::RowMajorVoltaTensorOpMultiplicandCrosswise(Index ldm=0)'],['../structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#a324cdfe3699077b6ba1f25f807e117c7',1,'cutlass::layout::RowMajorVoltaTensorOpMultiplicandCrosswise::RowMajorVoltaTensorOpMultiplicandCrosswise(Stride stride)']]],
  ['rowmajorvoltatensoropmultiplicandcrosswise',['RowMajorVoltaTensorOpMultiplicandCrosswise',['../structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html',1,'cutlass::layout']]],
  ['rowvector',['RowVector',['../namespacecutlass_1_1thread.html#a4a8bdc0c4e09b113284e07228704f98a',1,'cutlass::thread']]],
  ['run',['run',['../classcutlass_1_1gemm_1_1device_1_1Gemm.html#aef8c133e539ef91efc9dba9012118147',1,'cutlass::gemm::device::Gemm::run()'],['../classcutlass_1_1gemm_1_1device_1_1Gemm_3_01ElementA___00_01LayoutA___00_01ElementB___00_01Layout4d0960ae6b1d1bf19e6239dbd002249c.html#a5f4f93ca97b358b4410f3d0b1e0a6387',1,'cutlass::gemm::device::Gemm&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, AlignmentA, AlignmentB, SplitKSerial, Operator_, IsBetaZero &gt;::run()'],['../classcutlass_1_1gemm_1_1device_1_1GemmBatched.html#ad7e9e393be872e401a5a777ceda529d9',1,'cutlass::gemm::device::GemmBatched::run()'],['../classcutlass_1_1gemm_1_1device_1_1GemmBatched_3_01ElementA___00_01LayoutA___00_01ElementB___00_0c9bb6f4463ab6085e6008b5d5ad6abfd.html#abcae3d15f1ec2ee7ae93690c82fbee8a',1,'cutlass::gemm::device::GemmBatched&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, AlignmentA, AlignmentB, Operator_ &gt;::run()'],['../classcutlass_1_1gemm_1_1device_1_1GemmComplex.html#ac6cfa33deb7e829c4122543aad00964d',1,'cutlass::gemm::device::GemmComplex::run()'],['../classcutlass_1_1gemm_1_1device_1_1GemmComplex_3_01ElementA___00_01LayoutA___00_01ElementB___00_07c56401b4df75709ae636675d9980a9a.html#a4111bba1e9d2000fcc9bba2f114ee801',1,'cutlass::gemm::device::GemmComplex&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, TransformA, TransformB, SplitKSerial &gt;::run()'],['../classcutlass_1_1gemm_1_1device_1_1GemmSplitKParallel.html#aeadc76210d06ec22776aca4a58de9930',1,'cutlass::gemm::device::GemmSplitKParallel::run()'],['../classcutlass_1_1gemm_1_1device_1_1GemmSplitKParallel_3_01ElementA___00_01LayoutA___00_01ElementBbe7c1f7154ad5b5bf9d4d28301e2b457.html#a1de7cf5d8bad27b3ff6c803dbc572077',1,'cutlass::gemm::device::GemmSplitKParallel&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ConvertScaledOp_, ReductionOp_, ThreadblockSwizzle_, Stages, kAlignmentA, kAlignmentB, Operator_ &gt;::run()'],['../structcutlass_1_1reduction_1_1BatchedReduction.html#a4bfcdfd8f2edeb4fc7081443584d599b',1,'cutlass::reduction::BatchedReduction::run()'],['../classcutlass_1_1library_1_1Operation.html#ae4a336419eb6dba60e3ffc04ef7ebdf3',1,'cutlass::library::Operation::run()']]],
  ['threadblock_5fswizzle_2eh',['threadblock_swizzle.h',['../reduction_2threadblock__swizzle_8h.html',1,'']]]
];
