<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: gemm.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_4eeb864c4eec08c7d6b9d3b0352cfdde.html">tools</a></li><li class="navelem"><a class="el" href="dir_88de82f9e8d739a2f42f92d95f0d7933.html">util</a></li><li class="navelem"><a class="el" href="dir_7e9e609009df72bf6226de354e72c328.html">include</a></li><li class="navelem"><a class="el" href="dir_ade2f6ff57439d30f4164e14e54bcf30.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_ff60863f958a43c892071bb1f8a4c81a.html">util</a></li><li class="navelem"><a class="el" href="dir_01de8928c960cafb028e5f164701e1de.html">reference</a></li><li class="navelem"><a class="el" href="dir_ebbbb6f6f10686db77ac27d0af6d8201.html">device</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">tools/util/include/cutlass/util/reference/device/gemm.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="tools_2util_2include_2cutlass_2util_2reference_2device_2gemm_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2017-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> **************************************************************************************************/</span></div><div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;</div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="coord_8h.html">cutlass/coord.h</a>&quot;</span></div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;</div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="numeric__types_8h.html">cutlass/numeric_types.h</a>&quot;</span></div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="functional_8h.html">cutlass/functional.h</a>&quot;</span></div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="numeric__conversion_8h.html">cutlass/numeric_conversion.h</a>&quot;</span></div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;</div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="matrix__traits_8h.html">cutlass/matrix_traits.h</a>&quot;</span></div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="tensor__view_8h.html">cutlass/tensor_view.h</a>&quot;</span></div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="include_2cutlass_2gemm_2gemm_8h.html">cutlass/gemm/gemm.h</a>&quot;</span></div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;</div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="tools_2util_2include_2cutlass_2util_2reference_2device_2kernel_2gemm_8h.html">cutlass/util/reference/device/kernel/gemm.h</a>&quot;</span></div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;</div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;<span class="keyword">namespace </span>reference {</div><div class="line"><a name="l00045"></a><span class="lineno"><a class="line" href="namespacecutlass_1_1reference_1_1device.html">   45</a></span>&#160;<span class="keyword">namespace </span>device {</div><div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;</div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;</div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;  <span class="keyword">typename</span> ElementA,</div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;  <span class="keyword">typename</span> LayoutA,</div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;  <span class="keyword">typename</span> ElementB,</div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;  <span class="keyword">typename</span> LayoutB,</div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;  <span class="keyword">typename</span> ElementC,</div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;  <span class="keyword">typename</span> LayoutC,</div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;  <span class="keyword">typename</span> ScalarType,</div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;  <span class="keyword">typename</span> AccumulatorType,</div><div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;  <span class="keyword">typename</span> InnerProductOp = <a class="code" href="structcutlass_1_1multiply__add.html">multiply_add&lt;AccumulatorType&gt;</a>,</div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;  <span class="keyword">typename</span> ConvertOp = <a class="code" href="structcutlass_1_1NumericConverter.html">NumericConverter&lt;ElementC, ScalarType&gt;</a></div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;&gt;</div><div class="line"><a name="l00068"></a><span class="lineno"><a class="line" href="namespacecutlass_1_1reference_1_1device.html#a4b872e5b16985b2cf31530a9090a8423">   68</a></span>&#160;<span class="keywordtype">void</span> <a class="code" href="namespacecutlass_1_1reference_1_1device.html#a4b872e5b16985b2cf31530a9090a8423">compute_gemm</a>(</div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;  <a class="code" href="structcutlass_1_1gemm_1_1GemmCoord.html">gemm::GemmCoord</a> problem_size,</div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;  ScalarType alpha,</div><div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;  <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;ElementA, LayoutA&gt;</a> tensor_a,</div><div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;  <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;ElementB, LayoutB&gt;</a> tensor_b,</div><div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;  ScalarType beta,</div><div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;  <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;ElementC, LayoutC&gt;</a> tensor_c,</div><div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;  <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;ElementC, LayoutC&gt;</a> tensor_d,</div><div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;  AccumulatorType initial_accum) {</div><div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;</div><div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;  <a class="code" href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a>(</div><div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;    LayoutA::kRank == 2 &amp;&amp;</div><div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;    LayoutB::kRank == 2 &amp;&amp;</div><div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;    LayoutC::kRank == 2, <span class="stringliteral">&quot;Tensors must be of rank 2&quot;</span>);</div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;</div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;  <span class="comment">// Blocking structure potentially improves performance of reference implementation</span></div><div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;  <span class="comment">// with a minor increase in complexity.</span></div><div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;  <span class="comment">// Note, this reference implementation is NOT expected to approach peak performance.</span></div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;  <span class="keyword">using</span> OutputTile = <a class="code" href="structcutlass_1_1MatrixShape.html">MatrixShape&lt;4, 4&gt;</a>;</div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;</div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;  dim3 block(16, 8);</div><div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;</div><div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;  dim3 grid(</div><div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;    (problem_size.<a class="code" href="structcutlass_1_1gemm_1_1GemmCoord.html#a93515a41db6c4b7e9101067f60d41b8c">m</a>() + block.x * OutputTile::kRow - 1) / (block.x * OutputTile::kRow),</div><div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;    (problem_size.<a class="code" href="structcutlass_1_1gemm_1_1GemmCoord.html#a1b29d2cb15360ad5499216859ad5436a">n</a>() + block.y * OutputTile::kColumn - 1) / (block.y * OutputTile::kColumn)</div><div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;  );</div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;</div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;  <span class="comment">// Launch a GEMM kernel</span></div><div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;  <a class="code" href="namespacecutlass_1_1reference_1_1device_1_1kernel.html#a0f44a48b38f56a69beade68adb32df6f">kernel::Gemm</a>&lt;</div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;    <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;ElementA, LayoutA&gt;</a>,</div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;    <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;ElementB, LayoutB&gt;</a>,</div><div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;    <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;ElementC, LayoutC&gt;</a>,</div><div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;    ScalarType,</div><div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;    AccumulatorType,</div><div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;    OutputTile,</div><div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;    InnerProductOp,</div><div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;    ConvertOp</div><div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;  &gt;&lt;&lt;&lt; grid, block &gt;&gt;&gt;(</div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;    problem_size,</div><div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;    alpha,</div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;    tensor_a,</div><div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;    tensor_b,</div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;    beta,</div><div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;    tensor_c,</div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;    tensor_d,</div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;    initial_accum</div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;  );</div><div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;}</div><div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;</div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;  <span class="keyword">typename</span> ElementA,</div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;  <span class="keyword">typename</span> LayoutA,</div><div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;  <span class="keyword">typename</span> ElementB,</div><div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;  <span class="keyword">typename</span> LayoutB,</div><div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;  <span class="keyword">typename</span> ElementC,</div><div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;  <span class="keyword">typename</span> LayoutC,</div><div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;  <span class="keyword">typename</span> ScalarType,</div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;  <span class="keyword">typename</span> AccumulatorType,</div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;  <span class="keyword">typename</span> InnerProductOp = <a class="code" href="structcutlass_1_1multiply__add.html">multiply_add&lt;AccumulatorType&gt;</a>,</div><div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;  <span class="keyword">typename</span> ConvertOp = <a class="code" href="structcutlass_1_1NumericConverter.html">NumericConverter&lt;ElementC, ScalarType&gt;</a></div><div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;&gt;</div><div class="line"><a name="l00135"></a><span class="lineno"><a class="line" href="namespacecutlass_1_1reference_1_1device.html#aa1b04f721cb13fb3f110acf6b29dc53b">  135</a></span>&#160;<span class="keywordtype">void</span> <a class="code" href="namespacecutlass_1_1reference_1_1device.html#a4b872e5b16985b2cf31530a9090a8423">compute_gemm</a>(</div><div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;  <a class="code" href="structcutlass_1_1gemm_1_1GemmCoord.html">gemm::GemmCoord</a> problem_size,</div><div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;  ScalarType alpha,</div><div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;  <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;ElementA, LayoutA&gt;</a> tensor_a,</div><div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;  <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;ElementB, LayoutB&gt;</a> tensor_b,</div><div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;  ScalarType beta,</div><div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;  <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;ElementC, LayoutC&gt;</a> tensor_c,</div><div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;  AccumulatorType initial_accum) {</div><div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;</div><div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;  <a class="code" href="namespacecutlass_1_1reference_1_1device.html#a4b872e5b16985b2cf31530a9090a8423">compute_gemm</a>&lt;ElementA, LayoutA, ElementB, LayoutB, ElementC, LayoutC,</div><div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;                ScalarType, AccumulatorType, InnerProductOp, ConvertOp&gt;(</div><div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;        problem_size, alpha, tensor_a, tensor_b, beta, tensor_c, tensor_c,</div><div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;        initial_accum);</div><div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;}</div><div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;</div><div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;  <span class="keyword">typename</span> ElementA,</div><div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;  <span class="keyword">typename</span> LayoutA,</div><div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;  <span class="keyword">typename</span> ElementB,</div><div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;  <span class="keyword">typename</span> LayoutB,</div><div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;  <span class="keyword">typename</span> ElementC,</div><div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;  <span class="keyword">typename</span> LayoutC,</div><div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;  <span class="keyword">typename</span> ScalarType,</div><div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;  <span class="keyword">typename</span> AccumulatorType,</div><div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;  <span class="keyword">typename</span> InnerProductOp = cutlass::arch::OpMultiplyAdd</div><div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;&gt;</div><div class="line"><a name="l00161"></a><span class="lineno"><a class="line" href="structcutlass_1_1reference_1_1device_1_1Gemm.html">  161</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1reference_1_1device_1_1Gemm.html">Gemm</a>;</div><div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;</div><div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;</div><div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> ElementA, <span class="keyword">typename</span> LayoutA, <span class="keyword">typename</span> ElementB,</div><div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;          <span class="keyword">typename</span> LayoutB, <span class="keyword">typename</span> ElementC, <span class="keyword">typename</span> LayoutC,</div><div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;          <span class="keyword">typename</span> ScalarType, <span class="keyword">typename</span> AccumulatorType&gt;</div><div class="line"><a name="l00169"></a><span class="lineno"><a class="line" href="structcutlass_1_1reference_1_1device_1_1Gemm_3_01ElementA_00_01LayoutA_00_01ElementB_00_01Layout4e016ab7cfc644acd7cb4ae770339773.html">  169</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1reference_1_1device_1_1Gemm.html">Gemm</a>&lt;ElementA, LayoutA, ElementB, LayoutB, ElementC, LayoutC,</div><div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;            ScalarType, AccumulatorType, arch::OpMultiplyAdd&gt; {</div><div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;</div><div class="line"><a name="l00172"></a><span class="lineno"><a class="line" href="structcutlass_1_1reference_1_1device_1_1Gemm_3_01ElementA_00_01LayoutA_00_01ElementB_00_01Layout4e016ab7cfc644acd7cb4ae770339773.html#aaa583b83e88d991c2e34e46e2858bbf8">  172</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="structcutlass_1_1reference_1_1device_1_1Gemm_3_01ElementA_00_01LayoutA_00_01ElementB_00_01Layout4e016ab7cfc644acd7cb4ae770339773.html#aaa583b83e88d991c2e34e46e2858bbf8">operator()</a>(<a class="code" href="structcutlass_1_1gemm_1_1GemmCoord.html">gemm::GemmCoord</a> problem_size, ScalarType alpha,</div><div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;                  <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;ElementA, LayoutA&gt;</a> tensor_a,</div><div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;                  <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;ElementB, LayoutB&gt;</a> tensor_b, ScalarType beta,</div><div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;                  <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;ElementC, LayoutC&gt;</a> tensor_c,</div><div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;                  AccumulatorType initial_accum = AccumulatorType(0)) {</div><div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;</div><div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;    <a class="code" href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a>(</div><div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;      LayoutA::kRank == 2 &amp;&amp; LayoutB::kRank == 2 &amp;&amp; LayoutC::kRank == 2,</div><div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;      <span class="stringliteral">&quot;Tensors must be of rank 2&quot;</span>);</div><div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;</div><div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;    <a class="code" href="namespacecutlass_1_1reference_1_1device.html#a4b872e5b16985b2cf31530a9090a8423">compute_gemm</a>&lt;ElementA, LayoutA, ElementB, LayoutB, ElementC, LayoutC,</div><div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;                  ScalarType, AccumulatorType, <a class="code" href="structcutlass_1_1multiply__add.html">multiply_add&lt;AccumulatorType&gt;</a>&gt;(</div><div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;        problem_size, alpha, tensor_a, tensor_b, beta, tensor_c, initial_accum);</div><div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;  }</div><div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;</div><div class="line"><a name="l00187"></a><span class="lineno"><a class="line" href="structcutlass_1_1reference_1_1device_1_1Gemm_3_01ElementA_00_01LayoutA_00_01ElementB_00_01Layout4e016ab7cfc644acd7cb4ae770339773.html#a3454b1b46009a4a6057c758e74e531b7">  187</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="structcutlass_1_1reference_1_1device_1_1Gemm_3_01ElementA_00_01LayoutA_00_01ElementB_00_01Layout4e016ab7cfc644acd7cb4ae770339773.html#a3454b1b46009a4a6057c758e74e531b7">operator()</a>(<a class="code" href="structcutlass_1_1gemm_1_1GemmCoord.html">gemm::GemmCoord</a> problem_size, ScalarType alpha,</div><div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;                  <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;ElementA, LayoutA&gt;</a> tensor_a,</div><div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;                  <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;ElementB, LayoutB&gt;</a> tensor_b, ScalarType beta,</div><div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;                  <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;ElementC, LayoutC&gt;</a> tensor_c,</div><div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;                  <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;ElementC, LayoutC&gt;</a> tensor_d,</div><div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;                  AccumulatorType initial_accum = AccumulatorType(0)) {</div><div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;    <a class="code" href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a>(</div><div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;      LayoutA::kRank == 2 &amp;&amp; LayoutB::kRank == 2 &amp;&amp; LayoutC::kRank == 2,</div><div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;      <span class="stringliteral">&quot;Tensors must be of rank 2&quot;</span>);</div><div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;</div><div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;    <a class="code" href="namespacecutlass_1_1reference_1_1device.html#a4b872e5b16985b2cf31530a9090a8423">compute_gemm</a>&lt;ElementA, LayoutA, ElementB, LayoutB, ElementC, LayoutC,</div><div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;                ScalarType, AccumulatorType, <a class="code" href="structcutlass_1_1multiply__add.html">multiply_add&lt;AccumulatorType&gt;</a>&gt;(</div><div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;        problem_size, alpha, tensor_a, tensor_b, beta, tensor_c, tensor_d, initial_accum);</div><div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;  }</div><div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;};</div><div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;</div><div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;</div><div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> ElementA, <span class="keyword">typename</span> LayoutA, <span class="keyword">typename</span> ElementB,</div><div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;          <span class="keyword">typename</span> LayoutB, <span class="keyword">typename</span> ElementC, <span class="keyword">typename</span> LayoutC,</div><div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;          <span class="keyword">typename</span> ScalarType, <span class="keyword">typename</span> AccumulatorType&gt;</div><div class="line"><a name="l00209"></a><span class="lineno"><a class="line" href="structcutlass_1_1reference_1_1device_1_1Gemm_3_01ElementA_00_01LayoutA_00_01ElementB_00_01Layout30b72addd464a2ca4a26785cbfd77a8e.html">  209</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1reference_1_1device_1_1Gemm.html">Gemm</a>&lt;ElementA, LayoutA, ElementB, LayoutB, ElementC, LayoutC, ScalarType,</div><div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;            AccumulatorType, arch::OpMultiplyAddSaturate&gt; {</div><div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;</div><div class="line"><a name="l00212"></a><span class="lineno"><a class="line" href="structcutlass_1_1reference_1_1device_1_1Gemm_3_01ElementA_00_01LayoutA_00_01ElementB_00_01Layout30b72addd464a2ca4a26785cbfd77a8e.html#a505e5a44cacbf991e68638dcd6eba466">  212</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="structcutlass_1_1reference_1_1device_1_1Gemm_3_01ElementA_00_01LayoutA_00_01ElementB_00_01Layout30b72addd464a2ca4a26785cbfd77a8e.html#a505e5a44cacbf991e68638dcd6eba466">operator()</a>(<a class="code" href="structcutlass_1_1gemm_1_1GemmCoord.html">gemm::GemmCoord</a> problem_size, ScalarType alpha,</div><div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;                  <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;ElementA, LayoutA&gt;</a> tensor_a,</div><div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160;                  <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;ElementB, LayoutB&gt;</a> tensor_b, ScalarType beta,</div><div class="line"><a name="l00215"></a><span class="lineno">  215</span>&#160;                  <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;ElementC, LayoutC&gt;</a> tensor_c,</div><div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;                  AccumulatorType initial_accum = AccumulatorType(0)) {</div><div class="line"><a name="l00217"></a><span class="lineno">  217</span>&#160;    <a class="code" href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a>(</div><div class="line"><a name="l00218"></a><span class="lineno">  218</span>&#160;        LayoutA::kRank == 2 &amp;&amp; LayoutB::kRank == 2 &amp;&amp; LayoutC::kRank == 2,</div><div class="line"><a name="l00219"></a><span class="lineno">  219</span>&#160;        <span class="stringliteral">&quot;Tensors must be of rank 2&quot;</span>);</div><div class="line"><a name="l00220"></a><span class="lineno">  220</span>&#160;</div><div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160;    <a class="code" href="namespacecutlass_1_1reference_1_1device.html#a4b872e5b16985b2cf31530a9090a8423">compute_gemm</a>&lt;ElementA, LayoutA, ElementB, LayoutB, ElementC, LayoutC,</div><div class="line"><a name="l00222"></a><span class="lineno">  222</span>&#160;                 ScalarType, AccumulatorType, multiply_add&lt;AccumulatorType&gt;,</div><div class="line"><a name="l00223"></a><span class="lineno">  223</span>&#160;                 <a class="code" href="structcutlass_1_1NumericConverterClamp.html">NumericConverterClamp&lt;ElementC, ScalarType&gt;</a>&gt;(</div><div class="line"><a name="l00224"></a><span class="lineno">  224</span>&#160;        problem_size, alpha, tensor_a, tensor_b, beta, tensor_c, initial_accum);</div><div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;  }</div><div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160;</div><div class="line"><a name="l00227"></a><span class="lineno"><a class="line" href="structcutlass_1_1reference_1_1device_1_1Gemm_3_01ElementA_00_01LayoutA_00_01ElementB_00_01Layout30b72addd464a2ca4a26785cbfd77a8e.html#a0ddb9e7856cec7fba5e2618b664f7dab">  227</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="structcutlass_1_1reference_1_1device_1_1Gemm_3_01ElementA_00_01LayoutA_00_01ElementB_00_01Layout30b72addd464a2ca4a26785cbfd77a8e.html#a0ddb9e7856cec7fba5e2618b664f7dab">operator()</a>(<a class="code" href="structcutlass_1_1gemm_1_1GemmCoord.html">gemm::GemmCoord</a> problem_size, ScalarType alpha,</div><div class="line"><a name="l00228"></a><span class="lineno">  228</span>&#160;                  <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;ElementA, LayoutA&gt;</a> tensor_a,</div><div class="line"><a name="l00229"></a><span class="lineno">  229</span>&#160;                  <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;ElementB, LayoutB&gt;</a> tensor_b, ScalarType beta,</div><div class="line"><a name="l00230"></a><span class="lineno">  230</span>&#160;                  <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;ElementC, LayoutC&gt;</a> tensor_c,</div><div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160;                  <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;ElementC, LayoutC&gt;</a> tensor_d,</div><div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160;                  AccumulatorType initial_accum = AccumulatorType(0)) {</div><div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160;    <a class="code" href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a>(</div><div class="line"><a name="l00234"></a><span class="lineno">  234</span>&#160;        LayoutA::kRank == 2 &amp;&amp; LayoutB::kRank == 2 &amp;&amp; LayoutC::kRank == 2,</div><div class="line"><a name="l00235"></a><span class="lineno">  235</span>&#160;        <span class="stringliteral">&quot;Tensors must be of rank 2&quot;</span>);</div><div class="line"><a name="l00236"></a><span class="lineno">  236</span>&#160;</div><div class="line"><a name="l00237"></a><span class="lineno">  237</span>&#160;    <a class="code" href="namespacecutlass_1_1reference_1_1device.html#a4b872e5b16985b2cf31530a9090a8423">compute_gemm</a>&lt;ElementA, LayoutA, ElementB, LayoutB, ElementC, LayoutC,</div><div class="line"><a name="l00238"></a><span class="lineno">  238</span>&#160;                 ScalarType, AccumulatorType, multiply_add&lt;AccumulatorType&gt;,</div><div class="line"><a name="l00239"></a><span class="lineno">  239</span>&#160;                 <a class="code" href="structcutlass_1_1NumericConverterClamp.html">NumericConverterClamp&lt;ElementC, ScalarType&gt;</a>&gt;(</div><div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160;        problem_size, alpha, tensor_a, tensor_b, beta, tensor_c, tensor_d, initial_accum);</div><div class="line"><a name="l00241"></a><span class="lineno">  241</span>&#160;  }</div><div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160;};</div><div class="line"><a name="l00243"></a><span class="lineno">  243</span>&#160;</div><div class="line"><a name="l00245"></a><span class="lineno">  245</span>&#160;</div><div class="line"><a name="l00247"></a><span class="lineno">  247</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> ElementA, <span class="keyword">typename</span> LayoutA, <span class="keyword">typename</span> ElementB,</div><div class="line"><a name="l00248"></a><span class="lineno">  248</span>&#160;          <span class="keyword">typename</span> LayoutB, <span class="keyword">typename</span> ElementC, <span class="keyword">typename</span> LayoutC,</div><div class="line"><a name="l00249"></a><span class="lineno">  249</span>&#160;          <span class="keyword">typename</span> ScalarType, <span class="keyword">typename</span> AccumulatorType&gt;</div><div class="line"><a name="l00250"></a><span class="lineno"><a class="line" href="structcutlass_1_1reference_1_1device_1_1Gemm_3_01ElementA_00_01LayoutA_00_01ElementB_00_01Layout660562b232f408218828ca5915b7e73a.html">  250</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1reference_1_1device_1_1Gemm.html">Gemm</a>&lt;ElementA, LayoutA, ElementB, LayoutB, ElementC, LayoutC, ScalarType,</div><div class="line"><a name="l00251"></a><span class="lineno">  251</span>&#160;            AccumulatorType, arch::OpXorPopc&gt; {</div><div class="line"><a name="l00252"></a><span class="lineno">  252</span>&#160;</div><div class="line"><a name="l00253"></a><span class="lineno"><a class="line" href="structcutlass_1_1reference_1_1device_1_1Gemm_3_01ElementA_00_01LayoutA_00_01ElementB_00_01Layout660562b232f408218828ca5915b7e73a.html#a00dfb8cae5e94bed01f5a09bbd515410">  253</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="structcutlass_1_1reference_1_1device_1_1Gemm_3_01ElementA_00_01LayoutA_00_01ElementB_00_01Layout660562b232f408218828ca5915b7e73a.html#a00dfb8cae5e94bed01f5a09bbd515410">operator()</a>(<a class="code" href="structcutlass_1_1gemm_1_1GemmCoord.html">gemm::GemmCoord</a> problem_size, ScalarType alpha,</div><div class="line"><a name="l00254"></a><span class="lineno">  254</span>&#160;                  <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;ElementA, LayoutA&gt;</a> tensor_a,</div><div class="line"><a name="l00255"></a><span class="lineno">  255</span>&#160;                  <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;ElementB, LayoutB&gt;</a> tensor_b, ScalarType beta,</div><div class="line"><a name="l00256"></a><span class="lineno">  256</span>&#160;                  <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;ElementC, LayoutC&gt;</a> tensor_c,</div><div class="line"><a name="l00257"></a><span class="lineno">  257</span>&#160;                  AccumulatorType initial_accum = AccumulatorType(0)) {</div><div class="line"><a name="l00258"></a><span class="lineno">  258</span>&#160;    <a class="code" href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a>(</div><div class="line"><a name="l00259"></a><span class="lineno">  259</span>&#160;        LayoutA::kRank == 2 &amp;&amp; LayoutB::kRank == 2 &amp;&amp; LayoutC::kRank == 2,</div><div class="line"><a name="l00260"></a><span class="lineno">  260</span>&#160;        <span class="stringliteral">&quot;Tensors must be of rank 2&quot;</span>);</div><div class="line"><a name="l00261"></a><span class="lineno">  261</span>&#160;</div><div class="line"><a name="l00262"></a><span class="lineno">  262</span>&#160;    <a class="code" href="namespacecutlass_1_1reference_1_1device.html#a4b872e5b16985b2cf31530a9090a8423">compute_gemm</a>&lt;ElementA, LayoutA, ElementB, LayoutB, ElementC, LayoutC,</div><div class="line"><a name="l00263"></a><span class="lineno">  263</span>&#160;                 ScalarType, AccumulatorType, <a class="code" href="structcutlass_1_1xor__add.html">xor_add&lt;AccumulatorType&gt;</a>&gt;(</div><div class="line"><a name="l00264"></a><span class="lineno">  264</span>&#160;        problem_size, alpha, tensor_a, tensor_b, beta, tensor_c, initial_accum);</div><div class="line"><a name="l00265"></a><span class="lineno">  265</span>&#160;  }</div><div class="line"><a name="l00266"></a><span class="lineno">  266</span>&#160;</div><div class="line"><a name="l00267"></a><span class="lineno"><a class="line" href="structcutlass_1_1reference_1_1device_1_1Gemm_3_01ElementA_00_01LayoutA_00_01ElementB_00_01Layout660562b232f408218828ca5915b7e73a.html#a58c28db4ce4a471e23aa8b9093376d72">  267</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="structcutlass_1_1reference_1_1device_1_1Gemm_3_01ElementA_00_01LayoutA_00_01ElementB_00_01Layout660562b232f408218828ca5915b7e73a.html#a58c28db4ce4a471e23aa8b9093376d72">operator()</a>(<a class="code" href="structcutlass_1_1gemm_1_1GemmCoord.html">gemm::GemmCoord</a> problem_size, ScalarType alpha,</div><div class="line"><a name="l00268"></a><span class="lineno">  268</span>&#160;                  <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;ElementA, LayoutA&gt;</a> tensor_a,</div><div class="line"><a name="l00269"></a><span class="lineno">  269</span>&#160;                  <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;ElementB, LayoutB&gt;</a> tensor_b, ScalarType beta,</div><div class="line"><a name="l00270"></a><span class="lineno">  270</span>&#160;                  <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;ElementC, LayoutC&gt;</a> tensor_c,</div><div class="line"><a name="l00271"></a><span class="lineno">  271</span>&#160;                  <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;ElementC, LayoutC&gt;</a> tensor_d,</div><div class="line"><a name="l00272"></a><span class="lineno">  272</span>&#160;                  AccumulatorType initial_accum = AccumulatorType(0)) {</div><div class="line"><a name="l00273"></a><span class="lineno">  273</span>&#160;    <a class="code" href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a>(</div><div class="line"><a name="l00274"></a><span class="lineno">  274</span>&#160;        LayoutA::kRank == 2 &amp;&amp; LayoutB::kRank == 2 &amp;&amp; LayoutC::kRank == 2,</div><div class="line"><a name="l00275"></a><span class="lineno">  275</span>&#160;        <span class="stringliteral">&quot;Tensors must be of rank 2&quot;</span>);</div><div class="line"><a name="l00276"></a><span class="lineno">  276</span>&#160;</div><div class="line"><a name="l00277"></a><span class="lineno">  277</span>&#160;    <a class="code" href="namespacecutlass_1_1reference_1_1device.html#a4b872e5b16985b2cf31530a9090a8423">compute_gemm</a>&lt;ElementA, LayoutA, ElementB, LayoutB, ElementC, LayoutC,</div><div class="line"><a name="l00278"></a><span class="lineno">  278</span>&#160;                 ScalarType, AccumulatorType, <a class="code" href="structcutlass_1_1xor__add.html">xor_add&lt;AccumulatorType&gt;</a>&gt;(</div><div class="line"><a name="l00279"></a><span class="lineno">  279</span>&#160;        problem_size, alpha, tensor_a, tensor_b, beta, tensor_c, tensor_d, initial_accum);</div><div class="line"><a name="l00280"></a><span class="lineno">  280</span>&#160;  }</div><div class="line"><a name="l00281"></a><span class="lineno">  281</span>&#160;};</div><div class="line"><a name="l00282"></a><span class="lineno">  282</span>&#160;</div><div class="line"><a name="l00283"></a><span class="lineno">  283</span>&#160;</div><div class="line"><a name="l00285"></a><span class="lineno">  285</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00286"></a><span class="lineno">  286</span>&#160;<span class="comment">// Batched GEMM</span></div><div class="line"><a name="l00287"></a><span class="lineno">  287</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00289"></a><span class="lineno">  289</span>&#160;<span class="comment"></span></div><div class="line"><a name="l00291"></a><span class="lineno">  291</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00292"></a><span class="lineno">  292</span>&#160;<span class="comment">// TensorRefCollection* is a type satisfying the TensorRefCollection concept.</span></div><div class="line"><a name="l00293"></a><span class="lineno">  293</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00294"></a><span class="lineno">  294</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00295"></a><span class="lineno">  295</span>&#160;  <span class="keyword">typename</span> TensorRefCollectionA,</div><div class="line"><a name="l00296"></a><span class="lineno">  296</span>&#160;  <span class="keyword">typename</span> TensorRefCollectionB,</div><div class="line"><a name="l00297"></a><span class="lineno">  297</span>&#160;  <span class="keyword">typename</span> TensorRefCollectionC,</div><div class="line"><a name="l00298"></a><span class="lineno">  298</span>&#160;  <span class="keyword">typename</span> ScalarType,</div><div class="line"><a name="l00299"></a><span class="lineno">  299</span>&#160;  <span class="keyword">typename</span> AccumulatorType,</div><div class="line"><a name="l00300"></a><span class="lineno">  300</span>&#160;  <span class="keyword">typename</span> InnerProductOp,</div><div class="line"><a name="l00301"></a><span class="lineno">  301</span>&#160;  <span class="keyword">typename</span> ConvertOp</div><div class="line"><a name="l00302"></a><span class="lineno">  302</span>&#160;&gt;</div><div class="line"><a name="l00303"></a><span class="lineno"><a class="line" href="namespacecutlass_1_1reference_1_1device.html#aaa524d4e141cc8934eb9a981e1c89fc5">  303</a></span>&#160;<span class="keywordtype">void</span> <a class="code" href="namespacecutlass_1_1reference_1_1device.html#aaa524d4e141cc8934eb9a981e1c89fc5">BatchedGemm</a>(</div><div class="line"><a name="l00304"></a><span class="lineno">  304</span>&#160;  <a class="code" href="structcutlass_1_1gemm_1_1GemmCoord.html">gemm::GemmCoord</a> problem_size,</div><div class="line"><a name="l00305"></a><span class="lineno">  305</span>&#160;  <span class="keywordtype">int</span> batch_count,</div><div class="line"><a name="l00306"></a><span class="lineno">  306</span>&#160;  ScalarType alpha,</div><div class="line"><a name="l00307"></a><span class="lineno">  307</span>&#160;  TensorRefCollectionA <span class="keyword">const</span>&amp; tensor_a,</div><div class="line"><a name="l00308"></a><span class="lineno">  308</span>&#160;  TensorRefCollectionB <span class="keyword">const</span>&amp; tensor_b,</div><div class="line"><a name="l00309"></a><span class="lineno">  309</span>&#160;  ScalarType beta,</div><div class="line"><a name="l00310"></a><span class="lineno">  310</span>&#160;  TensorRefCollectionC &amp;tensor_c,</div><div class="line"><a name="l00311"></a><span class="lineno">  311</span>&#160;  AccumulatorType initial_accum) {</div><div class="line"><a name="l00312"></a><span class="lineno">  312</span>&#160;</div><div class="line"><a name="l00313"></a><span class="lineno">  313</span>&#160;  <a class="code" href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a>(</div><div class="line"><a name="l00314"></a><span class="lineno">  314</span>&#160;    TensorRefCollectionA::kRank == 2 &amp;&amp;</div><div class="line"><a name="l00315"></a><span class="lineno">  315</span>&#160;    TensorRefCollectionB::kRank == 2 &amp;&amp;</div><div class="line"><a name="l00316"></a><span class="lineno">  316</span>&#160;    TensorRefCollectionC::kRank == 2, <span class="stringliteral">&quot;Tensors must be of rank 2&quot;</span>);</div><div class="line"><a name="l00317"></a><span class="lineno">  317</span>&#160;</div><div class="line"><a name="l00318"></a><span class="lineno">  318</span>&#160;  <span class="comment">// Blocking structure potentially improves performance of reference implementation</span></div><div class="line"><a name="l00319"></a><span class="lineno">  319</span>&#160;  <span class="comment">// with a minor increase in complexity.</span></div><div class="line"><a name="l00320"></a><span class="lineno">  320</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00321"></a><span class="lineno">  321</span>&#160;  <span class="comment">// Note, this reference implementation is NOT expected to approach peak performance.</span></div><div class="line"><a name="l00322"></a><span class="lineno">  322</span>&#160;  <span class="keyword">using</span> OutputTile = <a class="code" href="structcutlass_1_1MatrixShape.html">MatrixShape&lt;4, 4&gt;</a>;</div><div class="line"><a name="l00323"></a><span class="lineno">  323</span>&#160;</div><div class="line"><a name="l00324"></a><span class="lineno">  324</span>&#160;  dim3 block(16, 8);</div><div class="line"><a name="l00325"></a><span class="lineno">  325</span>&#160;  dim3 grid(</div><div class="line"><a name="l00326"></a><span class="lineno">  326</span>&#160;    (problem_size.<a class="code" href="structcutlass_1_1gemm_1_1GemmCoord.html#a93515a41db6c4b7e9101067f60d41b8c">m</a>() + block.x * OutputTile::kRow - 1) / (block.x * OutputTile::kRow),</div><div class="line"><a name="l00327"></a><span class="lineno">  327</span>&#160;    (problem_size.<a class="code" href="structcutlass_1_1gemm_1_1GemmCoord.html#a1b29d2cb15360ad5499216859ad5436a">n</a>() + block.y * OutputTile::kColumn - 1) / (block.y * OutputTile::kColumn),</div><div class="line"><a name="l00328"></a><span class="lineno">  328</span>&#160;    batch_count</div><div class="line"><a name="l00329"></a><span class="lineno">  329</span>&#160;  );</div><div class="line"><a name="l00330"></a><span class="lineno">  330</span>&#160;</div><div class="line"><a name="l00331"></a><span class="lineno">  331</span>&#160;  <span class="comment">// Launch a GEMM kernel</span></div><div class="line"><a name="l00332"></a><span class="lineno">  332</span>&#160;  <a class="code" href="namespacecutlass_1_1reference_1_1device_1_1kernel.html#a013cf9aa1c8f98ec2037f242284def7b">kernel::BatchedGemm</a>&lt;</div><div class="line"><a name="l00333"></a><span class="lineno">  333</span>&#160;    TensorRefCollectionA,</div><div class="line"><a name="l00334"></a><span class="lineno">  334</span>&#160;    TensorRefCollectionB,</div><div class="line"><a name="l00335"></a><span class="lineno">  335</span>&#160;    TensorRefCollectionC,</div><div class="line"><a name="l00336"></a><span class="lineno">  336</span>&#160;    ScalarType,</div><div class="line"><a name="l00337"></a><span class="lineno">  337</span>&#160;    AccumulatorType,</div><div class="line"><a name="l00338"></a><span class="lineno">  338</span>&#160;    OutputTile,</div><div class="line"><a name="l00339"></a><span class="lineno">  339</span>&#160;    InnerProductOp,</div><div class="line"><a name="l00340"></a><span class="lineno">  340</span>&#160;    ConvertOp</div><div class="line"><a name="l00341"></a><span class="lineno">  341</span>&#160;  &gt;&lt;&lt;&lt; grid, block &gt;&gt;&gt;(</div><div class="line"><a name="l00342"></a><span class="lineno">  342</span>&#160;    problem_size,</div><div class="line"><a name="l00343"></a><span class="lineno">  343</span>&#160;    alpha,</div><div class="line"><a name="l00344"></a><span class="lineno">  344</span>&#160;    tensor_a,</div><div class="line"><a name="l00345"></a><span class="lineno">  345</span>&#160;    tensor_b,</div><div class="line"><a name="l00346"></a><span class="lineno">  346</span>&#160;    beta,</div><div class="line"><a name="l00347"></a><span class="lineno">  347</span>&#160;    tensor_c,</div><div class="line"><a name="l00348"></a><span class="lineno">  348</span>&#160;    initial_accum</div><div class="line"><a name="l00349"></a><span class="lineno">  349</span>&#160;  );</div><div class="line"><a name="l00350"></a><span class="lineno">  350</span>&#160;}</div><div class="line"><a name="l00351"></a><span class="lineno">  351</span>&#160;</div><div class="line"><a name="l00354"></a><span class="lineno">  354</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00355"></a><span class="lineno">  355</span>&#160;<span class="comment">// TensorRefCollection* is a type satisfying the TensorRefCollection concept.</span></div><div class="line"><a name="l00356"></a><span class="lineno">  356</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00357"></a><span class="lineno">  357</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00358"></a><span class="lineno">  358</span>&#160;  <span class="keyword">typename</span> TensorRefCollectionA,</div><div class="line"><a name="l00359"></a><span class="lineno">  359</span>&#160;  <span class="keyword">typename</span> TensorRefCollectionB,</div><div class="line"><a name="l00360"></a><span class="lineno">  360</span>&#160;  <span class="keyword">typename</span> TensorRefCollectionC,</div><div class="line"><a name="l00361"></a><span class="lineno">  361</span>&#160;  <span class="keyword">typename</span> ScalarType,</div><div class="line"><a name="l00362"></a><span class="lineno">  362</span>&#160;  <span class="keyword">typename</span> AccumulatorType</div><div class="line"><a name="l00363"></a><span class="lineno">  363</span>&#160;&gt;</div><div class="line"><a name="l00364"></a><span class="lineno"><a class="line" href="namespacecutlass_1_1reference_1_1device.html#abbb24b1a372b793bf35320443c179875">  364</a></span>&#160;<span class="keywordtype">void</span> <a class="code" href="namespacecutlass_1_1reference_1_1device.html#aaa524d4e141cc8934eb9a981e1c89fc5">BatchedGemm</a>(</div><div class="line"><a name="l00365"></a><span class="lineno">  365</span>&#160;  <a class="code" href="structcutlass_1_1gemm_1_1GemmCoord.html">gemm::GemmCoord</a> problem_size,</div><div class="line"><a name="l00366"></a><span class="lineno">  366</span>&#160;  <span class="keywordtype">int</span> batch_count,</div><div class="line"><a name="l00367"></a><span class="lineno">  367</span>&#160;  ScalarType alpha,</div><div class="line"><a name="l00368"></a><span class="lineno">  368</span>&#160;  TensorRefCollectionA <span class="keyword">const</span>&amp; tensor_a,</div><div class="line"><a name="l00369"></a><span class="lineno">  369</span>&#160;  TensorRefCollectionB <span class="keyword">const</span>&amp; tensor_b,</div><div class="line"><a name="l00370"></a><span class="lineno">  370</span>&#160;  ScalarType beta,</div><div class="line"><a name="l00371"></a><span class="lineno">  371</span>&#160;  TensorRefCollectionC &amp;tensor_c) {</div><div class="line"><a name="l00372"></a><span class="lineno">  372</span>&#160;</div><div class="line"><a name="l00373"></a><span class="lineno">  373</span>&#160;  <a class="code" href="namespacecutlass_1_1reference_1_1device.html#aaa524d4e141cc8934eb9a981e1c89fc5">BatchedGemm</a>(problem_size, alpha, tensor_a, tensor_b, beta, tensor_c, ScalarType(0));</div><div class="line"><a name="l00374"></a><span class="lineno">  374</span>&#160;}</div><div class="line"><a name="l00375"></a><span class="lineno">  375</span>&#160;</div><div class="line"><a name="l00377"></a><span class="lineno">  377</span>&#160;</div><div class="line"><a name="l00378"></a><span class="lineno">  378</span>&#160;} <span class="comment">// namespace device</span></div><div class="line"><a name="l00379"></a><span class="lineno">  379</span>&#160;} <span class="comment">// namespace reference</span></div><div class="line"><a name="l00380"></a><span class="lineno">  380</span>&#160;} <span class="comment">// namespace cutlass</span></div><div class="ttc" id="structcutlass_1_1multiply__add_html"><div class="ttname"><a href="structcutlass_1_1multiply__add.html">cutlass::multiply_add</a></div><div class="ttdoc">Fused multiply-add. </div><div class="ttdef"><b>Definition:</b> functional.h:92</div></div>
<div class="ttc" id="structcutlass_1_1reference_1_1device_1_1Gemm_3_01ElementA_00_01LayoutA_00_01ElementB_00_01Layout660562b232f408218828ca5915b7e73a_html_a58c28db4ce4a471e23aa8b9093376d72"><div class="ttname"><a href="structcutlass_1_1reference_1_1device_1_1Gemm_3_01ElementA_00_01LayoutA_00_01ElementB_00_01Layout660562b232f408218828ca5915b7e73a.html#a58c28db4ce4a471e23aa8b9093376d72">cutlass::reference::device::Gemm&lt; ElementA, LayoutA, ElementB, LayoutB, ElementC, LayoutC, ScalarType, AccumulatorType, arch::OpXorPopc &gt;::operator()</a></div><div class="ttdeci">void operator()(gemm::GemmCoord problem_size, ScalarType alpha, TensorRef&lt; ElementA, LayoutA &gt; tensor_a, TensorRef&lt; ElementB, LayoutB &gt; tensor_b, ScalarType beta, TensorRef&lt; ElementC, LayoutC &gt; tensor_c, TensorRef&lt; ElementC, LayoutC &gt; tensor_d, AccumulatorType initial_accum=AccumulatorType(0))</div><div class="ttdef"><b>Definition:</b> tools/util/include/cutlass/util/reference/device/gemm.h:267</div></div>
<div class="ttc" id="structcutlass_1_1MatrixShape_html"><div class="ttname"><a href="structcutlass_1_1MatrixShape.html">cutlass::MatrixShape</a></div><div class="ttdoc">Describes the size of a matrix tile. </div><div class="ttdef"><b>Definition:</b> matrix_shape.h:42</div></div>
<div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="structcutlass_1_1NumericConverterClamp_html"><div class="ttname"><a href="structcutlass_1_1NumericConverterClamp.html">cutlass::NumericConverterClamp</a></div><div class="ttdef"><b>Definition:</b> numeric_conversion.h:254</div></div>
<div class="ttc" id="coord_8h_html"><div class="ttname"><a href="coord_8h.html">coord.h</a></div><div class="ttdoc">A Coord is a coordinate of arbitrary rank into a tensor or matrix. </div></div>
<div class="ttc" id="structcutlass_1_1gemm_1_1GemmCoord_html"><div class="ttname"><a href="structcutlass_1_1gemm_1_1GemmCoord.html">cutlass::gemm::GemmCoord</a></div><div class="ttdef"><b>Definition:</b> include/cutlass/gemm/gemm.h:94</div></div>
<div class="ttc" id="include_2cutlass_2gemm_2gemm_8h_html"><div class="ttname"><a href="include_2cutlass_2gemm_2gemm_8h.html">gemm.h</a></div><div class="ttdoc">Defines common types used for all GEMM-like operators. </div></div>
<div class="ttc" id="structcutlass_1_1gemm_1_1GemmCoord_html_a1b29d2cb15360ad5499216859ad5436a"><div class="ttname"><a href="structcutlass_1_1gemm_1_1GemmCoord.html#a1b29d2cb15360ad5499216859ad5436a">cutlass::gemm::GemmCoord::n</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index const &amp; n() const </div><div class="ttdoc">Returns the GEMM N coordinate. </div><div class="ttdef"><b>Definition:</b> include/cutlass/gemm/gemm.h:137</div></div>
<div class="ttc" id="tensor__view_8h_html"><div class="ttname"><a href="tensor__view_8h.html">tensor_view.h</a></div><div class="ttdoc">Defines a structure containing strides and a pointer to tensor data. </div></div>
<div class="ttc" id="namespacecutlass_1_1reference_1_1device_1_1kernel_html_a013cf9aa1c8f98ec2037f242284def7b"><div class="ttname"><a href="namespacecutlass_1_1reference_1_1device_1_1kernel.html#a013cf9aa1c8f98ec2037f242284def7b">cutlass::reference::device::kernel::BatchedGemm</a></div><div class="ttdeci">__global__ void BatchedGemm(gemm::GemmCoord problem_size, ScalarType alpha, TensorRefCollectionA tensor_collection_a, TensorRefCollectionB tensor_collection_b, ScalarType beta, TensorRefCollectionC tensor_collection_c, AccumulatorType initial_accum)</div><div class="ttdef"><b>Definition:</b> tools/util/include/cutlass/util/reference/device/kernel/gemm.h:108</div></div>
<div class="ttc" id="numeric__conversion_8h_html"><div class="ttname"><a href="numeric__conversion_8h.html">numeric_conversion.h</a></div><div class="ttdoc">Boost-like numeric conversion operator for CUTLASS numeric types. </div></div>
<div class="ttc" id="structcutlass_1_1reference_1_1device_1_1Gemm_html"><div class="ttname"><a href="structcutlass_1_1reference_1_1device_1_1Gemm.html">cutlass::reference::device::Gemm</a></div><div class="ttdef"><b>Definition:</b> tools/util/include/cutlass/util/reference/device/gemm.h:161</div></div>
<div class="ttc" id="namespacecutlass_1_1reference_1_1device_html_aaa524d4e141cc8934eb9a981e1c89fc5"><div class="ttname"><a href="namespacecutlass_1_1reference_1_1device.html#aaa524d4e141cc8934eb9a981e1c89fc5">cutlass::reference::device::BatchedGemm</a></div><div class="ttdeci">void BatchedGemm(gemm::GemmCoord problem_size, int batch_count, ScalarType alpha, TensorRefCollectionA const &amp;tensor_a, TensorRefCollectionB const &amp;tensor_b, ScalarType beta, TensorRefCollectionC &amp;tensor_c, AccumulatorType initial_accum)</div><div class="ttdoc">Computes a batch of GEMMs over a set of matrices of common dimension. </div><div class="ttdef"><b>Definition:</b> tools/util/include/cutlass/util/reference/device/gemm.h:303</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html"><div class="ttname"><a href="classcutlass_1_1TensorRef.html">cutlass::TensorRef&lt; ElementA, LayoutA &gt;</a></div></div>
<div class="ttc" id="structcutlass_1_1reference_1_1device_1_1Gemm_3_01ElementA_00_01LayoutA_00_01ElementB_00_01Layout30b72addd464a2ca4a26785cbfd77a8e_html_a505e5a44cacbf991e68638dcd6eba466"><div class="ttname"><a href="structcutlass_1_1reference_1_1device_1_1Gemm_3_01ElementA_00_01LayoutA_00_01ElementB_00_01Layout30b72addd464a2ca4a26785cbfd77a8e.html#a505e5a44cacbf991e68638dcd6eba466">cutlass::reference::device::Gemm&lt; ElementA, LayoutA, ElementB, LayoutB, ElementC, LayoutC, ScalarType, AccumulatorType, arch::OpMultiplyAddSaturate &gt;::operator()</a></div><div class="ttdeci">void operator()(gemm::GemmCoord problem_size, ScalarType alpha, TensorRef&lt; ElementA, LayoutA &gt; tensor_a, TensorRef&lt; ElementB, LayoutB &gt; tensor_b, ScalarType beta, TensorRef&lt; ElementC, LayoutC &gt; tensor_c, AccumulatorType initial_accum=AccumulatorType(0))</div><div class="ttdef"><b>Definition:</b> tools/util/include/cutlass/util/reference/device/gemm.h:212</div></div>
<div class="ttc" id="numeric__types_8h_html"><div class="ttname"><a href="numeric__types_8h.html">numeric_types.h</a></div><div class="ttdoc">Top-level include for all CUTLASS numeric types. </div></div>
<div class="ttc" id="platform_8h_html_adde4c9ea91b753491851361a4198c009"><div class="ttname"><a href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a></div><div class="ttdeci">#define static_assert(__e, __m)</div><div class="ttdef"><b>Definition:</b> platform.h:153</div></div>
<div class="ttc" id="structcutlass_1_1NumericConverter_html"><div class="ttname"><a href="structcutlass_1_1NumericConverter.html">cutlass::NumericConverter</a></div><div class="ttdef"><b>Definition:</b> numeric_conversion.h:59</div></div>
<div class="ttc" id="structcutlass_1_1reference_1_1device_1_1Gemm_3_01ElementA_00_01LayoutA_00_01ElementB_00_01Layout30b72addd464a2ca4a26785cbfd77a8e_html_a0ddb9e7856cec7fba5e2618b664f7dab"><div class="ttname"><a href="structcutlass_1_1reference_1_1device_1_1Gemm_3_01ElementA_00_01LayoutA_00_01ElementB_00_01Layout30b72addd464a2ca4a26785cbfd77a8e.html#a0ddb9e7856cec7fba5e2618b664f7dab">cutlass::reference::device::Gemm&lt; ElementA, LayoutA, ElementB, LayoutB, ElementC, LayoutC, ScalarType, AccumulatorType, arch::OpMultiplyAddSaturate &gt;::operator()</a></div><div class="ttdeci">void operator()(gemm::GemmCoord problem_size, ScalarType alpha, TensorRef&lt; ElementA, LayoutA &gt; tensor_a, TensorRef&lt; ElementB, LayoutB &gt; tensor_b, ScalarType beta, TensorRef&lt; ElementC, LayoutC &gt; tensor_c, TensorRef&lt; ElementC, LayoutC &gt; tensor_d, AccumulatorType initial_accum=AccumulatorType(0))</div><div class="ttdef"><b>Definition:</b> tools/util/include/cutlass/util/reference/device/gemm.h:227</div></div>
<div class="ttc" id="structcutlass_1_1xor__add_html"><div class="ttname"><a href="structcutlass_1_1xor__add.html">cutlass::xor_add</a></div><div class="ttdoc">Fused multiply-add. </div><div class="ttdef"><b>Definition:</b> functional.h:101</div></div>
<div class="ttc" id="structcutlass_1_1gemm_1_1GemmCoord_html_a93515a41db6c4b7e9101067f60d41b8c"><div class="ttname"><a href="structcutlass_1_1gemm_1_1GemmCoord.html#a93515a41db6c4b7e9101067f60d41b8c">cutlass::gemm::GemmCoord::m</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index const &amp; m() const </div><div class="ttdoc">Returns the GEMM M coordinate. </div><div class="ttdef"><b>Definition:</b> include/cutlass/gemm/gemm.h:129</div></div>
<div class="ttc" id="namespacecutlass_1_1reference_1_1device_html_a4b872e5b16985b2cf31530a9090a8423"><div class="ttname"><a href="namespacecutlass_1_1reference_1_1device.html#a4b872e5b16985b2cf31530a9090a8423">cutlass::reference::device::compute_gemm</a></div><div class="ttdeci">void compute_gemm(gemm::GemmCoord problem_size, ScalarType alpha, TensorRef&lt; ElementA, LayoutA &gt; tensor_a, TensorRef&lt; ElementB, LayoutB &gt; tensor_b, ScalarType beta, TensorRef&lt; ElementC, LayoutC &gt; tensor_c, TensorRef&lt; ElementC, LayoutC &gt; tensor_d, AccumulatorType initial_accum)</div><div class="ttdef"><b>Definition:</b> tools/util/include/cutlass/util/reference/device/gemm.h:68</div></div>
<div class="ttc" id="structcutlass_1_1reference_1_1device_1_1Gemm_3_01ElementA_00_01LayoutA_00_01ElementB_00_01Layout4e016ab7cfc644acd7cb4ae770339773_html_a3454b1b46009a4a6057c758e74e531b7"><div class="ttname"><a href="structcutlass_1_1reference_1_1device_1_1Gemm_3_01ElementA_00_01LayoutA_00_01ElementB_00_01Layout4e016ab7cfc644acd7cb4ae770339773.html#a3454b1b46009a4a6057c758e74e531b7">cutlass::reference::device::Gemm&lt; ElementA, LayoutA, ElementB, LayoutB, ElementC, LayoutC, ScalarType, AccumulatorType, arch::OpMultiplyAdd &gt;::operator()</a></div><div class="ttdeci">void operator()(gemm::GemmCoord problem_size, ScalarType alpha, TensorRef&lt; ElementA, LayoutA &gt; tensor_a, TensorRef&lt; ElementB, LayoutB &gt; tensor_b, ScalarType beta, TensorRef&lt; ElementC, LayoutC &gt; tensor_c, TensorRef&lt; ElementC, LayoutC &gt; tensor_d, AccumulatorType initial_accum=AccumulatorType(0))</div><div class="ttdef"><b>Definition:</b> tools/util/include/cutlass/util/reference/device/gemm.h:187</div></div>
<div class="ttc" id="structcutlass_1_1reference_1_1device_1_1Gemm_3_01ElementA_00_01LayoutA_00_01ElementB_00_01Layout660562b232f408218828ca5915b7e73a_html_a00dfb8cae5e94bed01f5a09bbd515410"><div class="ttname"><a href="structcutlass_1_1reference_1_1device_1_1Gemm_3_01ElementA_00_01LayoutA_00_01ElementB_00_01Layout660562b232f408218828ca5915b7e73a.html#a00dfb8cae5e94bed01f5a09bbd515410">cutlass::reference::device::Gemm&lt; ElementA, LayoutA, ElementB, LayoutB, ElementC, LayoutC, ScalarType, AccumulatorType, arch::OpXorPopc &gt;::operator()</a></div><div class="ttdeci">void operator()(gemm::GemmCoord problem_size, ScalarType alpha, TensorRef&lt; ElementA, LayoutA &gt; tensor_a, TensorRef&lt; ElementB, LayoutB &gt; tensor_b, ScalarType beta, TensorRef&lt; ElementC, LayoutC &gt; tensor_c, AccumulatorType initial_accum=AccumulatorType(0))</div><div class="ttdef"><b>Definition:</b> tools/util/include/cutlass/util/reference/device/gemm.h:253</div></div>
<div class="ttc" id="matrix__traits_8h_html"><div class="ttname"><a href="matrix__traits_8h.html">matrix_traits.h</a></div><div class="ttdoc">Defines properties of matrices used to denote layout and operands to GEMM kernels. </div></div>
<div class="ttc" id="namespacecutlass_1_1reference_1_1device_1_1kernel_html_a0f44a48b38f56a69beade68adb32df6f"><div class="ttname"><a href="namespacecutlass_1_1reference_1_1device_1_1kernel.html#a0f44a48b38f56a69beade68adb32df6f">cutlass::reference::device::kernel::Gemm</a></div><div class="ttdeci">__global__ void Gemm(gemm::GemmCoord problem_size, ScalarType alpha, TensorRefA tensor_a, TensorRefB tensor_b, ScalarType beta, TensorRefC tensor_c, TensorRefC tensor_d, AccumulatorType initial_accum)</div><div class="ttdef"><b>Definition:</b> tools/util/include/cutlass/util/reference/device/kernel/gemm.h:57</div></div>
<div class="ttc" id="functional_8h_html"><div class="ttname"><a href="functional_8h.html">functional.h</a></div><div class="ttdoc">Define basic numeric operators with specializations for Array&lt;T, N&gt;. SIMD-ize where possible...</div></div>
<div class="ttc" id="tools_2util_2include_2cutlass_2util_2reference_2device_2kernel_2gemm_8h_html"><div class="ttname"><a href="tools_2util_2include_2cutlass_2util_2reference_2device_2kernel_2gemm_8h.html">gemm.h</a></div><div class="ttdoc">Reference implementation for GEMM in host-side code. </div></div>
<div class="ttc" id="structcutlass_1_1reference_1_1device_1_1Gemm_3_01ElementA_00_01LayoutA_00_01ElementB_00_01Layout4e016ab7cfc644acd7cb4ae770339773_html_aaa583b83e88d991c2e34e46e2858bbf8"><div class="ttname"><a href="structcutlass_1_1reference_1_1device_1_1Gemm_3_01ElementA_00_01LayoutA_00_01ElementB_00_01Layout4e016ab7cfc644acd7cb4ae770339773.html#aaa583b83e88d991c2e34e46e2858bbf8">cutlass::reference::device::Gemm&lt; ElementA, LayoutA, ElementB, LayoutB, ElementC, LayoutC, ScalarType, AccumulatorType, arch::OpMultiplyAdd &gt;::operator()</a></div><div class="ttdeci">void operator()(gemm::GemmCoord problem_size, ScalarType alpha, TensorRef&lt; ElementA, LayoutA &gt; tensor_a, TensorRef&lt; ElementB, LayoutB &gt; tensor_b, ScalarType beta, TensorRef&lt; ElementC, LayoutC &gt; tensor_c, AccumulatorType initial_accum=AccumulatorType(0))</div><div class="ttdef"><b>Definition:</b> tools/util/include/cutlass/util/reference/device/gemm.h:172</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
