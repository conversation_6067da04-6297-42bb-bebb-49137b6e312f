<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: vector.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_2296cf082f2778f9a3503c8ea1010763.html">layout</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">vector.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="vector_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2017-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> **************************************************************************************************/</span></div><div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;</div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="cutlass_8h.html">cutlass/cutlass.h</a>&quot;</span></div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="coord_8h.html">cutlass/coord.h</a>&quot;</span></div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;</div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;<span class="keyword">namespace </span>layout {</div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;</div><div class="line"><a name="l00037"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1PackedVectorLayout.html">   37</a></span>&#160;<span class="keyword">class </span><a class="code" href="classcutlass_1_1layout_1_1PackedVectorLayout.html">PackedVectorLayout</a> {</div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00040"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1PackedVectorLayout.html#a527d1f3f293a962aa814ca69a5194b24">   40</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="classcutlass_1_1layout_1_1PackedVectorLayout.html#a527d1f3f293a962aa814ca69a5194b24">kRank</a> = 1;</div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;</div><div class="line"><a name="l00043"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1PackedVectorLayout.html#a98a28ffe4b3919f3c011ddf86fec6e69">   43</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="classcutlass_1_1layout_1_1PackedVectorLayout.html#a98a28ffe4b3919f3c011ddf86fec6e69">kStrideRank</a> = 1;</div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;</div><div class="line"><a name="l00046"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1PackedVectorLayout.html#a359d91ca6d2cae5994db320edeea686e">   46</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1layout_1_1PackedVectorLayout.html#a359d91ca6d2cae5994db320edeea686e">Index</a> = int32_t;</div><div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;</div><div class="line"><a name="l00049"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1PackedVectorLayout.html#ac97c429c4de5e90a57fe14a90cb30f6b">   49</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1layout_1_1PackedVectorLayout.html#ac97c429c4de5e90a57fe14a90cb30f6b">LongIndex</a> = int64_t;</div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;</div><div class="line"><a name="l00052"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1PackedVectorLayout.html#a24fa65530043467f05aa2a13cdbc2f3e">   52</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1Coord.html">TensorCoord</a> = <a class="code" href="structcutlass_1_1Coord.html">Coord&lt;kRank, Index&gt;</a>;</div><div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;</div><div class="line"><a name="l00055"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1PackedVectorLayout.html#a19e9cdfad214f29e7e04c5d8560fec7d">   55</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1Coord.html">Stride</a> = <a class="code" href="structcutlass_1_1Coord.html">Coord&lt;kStrideRank, Index&gt;</a>;</div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;</div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;</div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;  <span class="comment">// No actual stride vector stored</span></div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;</div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;</div><div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;  <span class="comment">// Methods</span></div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;</div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00070"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1PackedVectorLayout.html#a8a6ead8c5b4b2a9e22d1e6ae779ff038">   70</a></span>&#160;  <a class="code" href="classcutlass_1_1layout_1_1PackedVectorLayout.html#a8a6ead8c5b4b2a9e22d1e6ae779ff038">PackedVectorLayout</a>() { }</div><div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;</div><div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00074"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1PackedVectorLayout.html#aaa31d06cab1db8ff0c303515396b5b09">   74</a></span>&#160;  <span class="keyword">static</span> <a class="code" href="classcutlass_1_1layout_1_1PackedVectorLayout.html">PackedVectorLayout</a> <a class="code" href="classcutlass_1_1layout_1_1PackedVectorLayout.html#aaa31d06cab1db8ff0c303515396b5b09">packed</a>(<a class="code" href="structcutlass_1_1Coord.html">TensorCoord</a> <span class="keyword">const</span> &amp;size) {</div><div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classcutlass_1_1layout_1_1PackedVectorLayout.html#a8a6ead8c5b4b2a9e22d1e6ae779ff038">PackedVectorLayout</a>();</div><div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;  }</div><div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;</div><div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00080"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1PackedVectorLayout.html#a39cc27d637aa946f1515df04972b1885">   80</a></span>&#160;  <a class="code" href="classcutlass_1_1layout_1_1PackedVectorLayout.html#ac97c429c4de5e90a57fe14a90cb30f6b">LongIndex</a> <a class="code" href="classcutlass_1_1layout_1_1PackedVectorLayout.html#a39cc27d637aa946f1515df04972b1885">operator()</a>(<a class="code" href="structcutlass_1_1Coord.html">TensorCoord</a> <span class="keyword">const</span> &amp;coord)<span class="keyword"> const </span>{</div><div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;    <span class="keywordflow">return</span> coord[0];</div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;  }</div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;</div><div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00086"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1PackedVectorLayout.html#ab6a6e1023e9c04d60714adbb4d713f17">   86</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="classcutlass_1_1layout_1_1PackedVectorLayout.html#ab6a6e1023e9c04d60714adbb4d713f17">stride</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="namespacecutlass.html#a7419519fa453a121dfa5f26bf87318d9">make_Coord</a>(1);</div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;  }</div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;</div><div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00092"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1PackedVectorLayout.html#a20b384f56d697894a75c8f4693155320">   92</a></span>&#160;  <a class="code" href="classcutlass_1_1layout_1_1PackedVectorLayout.html#ac97c429c4de5e90a57fe14a90cb30f6b">LongIndex</a> <a class="code" href="classcutlass_1_1layout_1_1PackedVectorLayout.html#a20b384f56d697894a75c8f4693155320">capacity</a>(<a class="code" href="structcutlass_1_1Coord.html">TensorCoord</a> <span class="keyword">const</span> &amp;size)<span class="keyword"> const </span>{</div><div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;    <span class="keywordflow">return</span> size[0];</div><div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;  }</div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;};</div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;</div><div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;} <span class="comment">// namespace layout</span></div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;} <span class="comment">// namespace cutlass</span></div><div class="ttc" id="classcutlass_1_1layout_1_1PackedVectorLayout_html_ac97c429c4de5e90a57fe14a90cb30f6b"><div class="ttname"><a href="classcutlass_1_1layout_1_1PackedVectorLayout.html#ac97c429c4de5e90a57fe14a90cb30f6b">cutlass::layout::PackedVectorLayout::LongIndex</a></div><div class="ttdeci">int64_t LongIndex</div><div class="ttdoc">Long index type used for offsets. </div><div class="ttdef"><b>Definition:</b> vector.h:49</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1PackedVectorLayout_html_a98a28ffe4b3919f3c011ddf86fec6e69"><div class="ttname"><a href="classcutlass_1_1layout_1_1PackedVectorLayout.html#a98a28ffe4b3919f3c011ddf86fec6e69">cutlass::layout::PackedVectorLayout::kStrideRank</a></div><div class="ttdeci">static int const kStrideRank</div><div class="ttdoc">Rank of stride vector. </div><div class="ttdef"><b>Definition:</b> vector.h:43</div></div>
<div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="coord_8h_html"><div class="ttname"><a href="coord_8h.html">coord.h</a></div><div class="ttdoc">A Coord is a coordinate of arbitrary rank into a tensor or matrix. </div></div>
<div class="ttc" id="namespacecutlass_html_a7419519fa453a121dfa5f26bf87318d9"><div class="ttname"><a href="namespacecutlass.html#a7419519fa453a121dfa5f26bf87318d9">cutlass::make_Coord</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Coord&lt; 1 &gt; make_Coord(int _0)</div><div class="ttdoc">Helper to make a 2-element coordinate. </div><div class="ttdef"><b>Definition:</b> coord.h:387</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1PackedVectorLayout_html_a8a6ead8c5b4b2a9e22d1e6ae779ff038"><div class="ttname"><a href="classcutlass_1_1layout_1_1PackedVectorLayout.html#a8a6ead8c5b4b2a9e22d1e6ae779ff038">cutlass::layout::PackedVectorLayout::PackedVectorLayout</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE PackedVectorLayout()</div><div class="ttdef"><b>Definition:</b> vector.h:70</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1PackedVectorLayout_html_ab6a6e1023e9c04d60714adbb4d713f17"><div class="ttname"><a href="classcutlass_1_1layout_1_1PackedVectorLayout.html#ab6a6e1023e9c04d60714adbb4d713f17">cutlass::layout::PackedVectorLayout::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride stride() const </div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> vector.h:86</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1PackedVectorLayout_html_a527d1f3f293a962aa814ca69a5194b24"><div class="ttname"><a href="classcutlass_1_1layout_1_1PackedVectorLayout.html#a527d1f3f293a962aa814ca69a5194b24">cutlass::layout::PackedVectorLayout::kRank</a></div><div class="ttdeci">static int const kRank</div><div class="ttdoc">Logical rank of tensor. </div><div class="ttdef"><b>Definition:</b> vector.h:40</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1PackedVectorLayout_html_a359d91ca6d2cae5994db320edeea686e"><div class="ttname"><a href="classcutlass_1_1layout_1_1PackedVectorLayout.html#a359d91ca6d2cae5994db320edeea686e">cutlass::layout::PackedVectorLayout::Index</a></div><div class="ttdeci">int32_t Index</div><div class="ttdoc">Index type used for coordinates. </div><div class="ttdef"><b>Definition:</b> vector.h:46</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1PackedVectorLayout_html_aaa31d06cab1db8ff0c303515396b5b09"><div class="ttname"><a href="classcutlass_1_1layout_1_1PackedVectorLayout.html#aaa31d06cab1db8ff0c303515396b5b09">cutlass::layout::PackedVectorLayout::packed</a></div><div class="ttdeci">static CUTLASS_HOST_DEVICE PackedVectorLayout packed(TensorCoord const &amp;size)</div><div class="ttdoc">Helper returns a layout to a tightly packed tensor. </div><div class="ttdef"><b>Definition:</b> vector.h:74</div></div>
<div class="ttc" id="cutlass_8h_html_a28c2443a142676d3d71effdae1a986b1"><div class="ttname"><a href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="ttdeci">#define CUTLASS_HOST_DEVICE</div><div class="ttdef"><b>Definition:</b> cutlass.h:89</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1PackedVectorLayout_html_a39cc27d637aa946f1515df04972b1885"><div class="ttname"><a href="classcutlass_1_1layout_1_1PackedVectorLayout.html#a39cc27d637aa946f1515df04972b1885">cutlass::layout::PackedVectorLayout::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex operator()(TensorCoord const &amp;coord) const </div><div class="ttdoc">Returns the offset of a coordinate in linear memory. </div><div class="ttdef"><b>Definition:</b> vector.h:80</div></div>
<div class="ttc" id="structcutlass_1_1Coord_html"><div class="ttname"><a href="structcutlass_1_1Coord.html">cutlass::Coord</a></div><div class="ttdoc">Statically-sized array specifying Coords within a tensor. </div><div class="ttdef"><b>Definition:</b> coord.h:43</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1PackedVectorLayout_html"><div class="ttname"><a href="classcutlass_1_1layout_1_1PackedVectorLayout.html">cutlass::layout::PackedVectorLayout</a></div><div class="ttdoc">Tensor layout for densely packed vectors. </div><div class="ttdef"><b>Definition:</b> vector.h:37</div></div>
<div class="ttc" id="cutlass_8h_html"><div class="ttname"><a href="cutlass_8h.html">cutlass.h</a></div><div class="ttdoc">Basic include for CUTLASS. </div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1PackedVectorLayout_html_a20b384f56d697894a75c8f4693155320"><div class="ttname"><a href="classcutlass_1_1layout_1_1PackedVectorLayout.html#a20b384f56d697894a75c8f4693155320">cutlass::layout::PackedVectorLayout::capacity</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex capacity(TensorCoord const &amp;size) const </div><div class="ttdoc">Compute the number of contiguous elements needed to store a tensor with the given size...</div><div class="ttdef"><b>Definition:</b> vector.h:92</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
