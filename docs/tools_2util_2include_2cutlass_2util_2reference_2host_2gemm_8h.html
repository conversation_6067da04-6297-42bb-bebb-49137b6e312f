<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: gemm.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_4eeb864c4eec08c7d6b9d3b0352cfdde.html">tools</a></li><li class="navelem"><a class="el" href="dir_88de82f9e8d739a2f42f92d95f0d7933.html">util</a></li><li class="navelem"><a class="el" href="dir_7e9e609009df72bf6226de354e72c328.html">include</a></li><li class="navelem"><a class="el" href="dir_ade2f6ff57439d30f4164e14e54bcf30.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_ff60863f958a43c892071bb1f8a4c81a.html">util</a></li><li class="navelem"><a class="el" href="dir_01de8928c960cafb028e5f164701e1de.html">reference</a></li><li class="navelem"><a class="el" href="dir_b790a865367d69962c5919afdba4a959.html">host</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#nested-classes">Classes</a> &#124;
<a href="#namespaces">Namespaces</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">tools/util/include/cutlass/util/reference/host/gemm.h File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Reference implementation for GEMM in host-side code.  
<a href="#details">More...</a></p>
<div class="textblock"><code>#include &quot;<a class="el" href="coord_8h_source.html">cutlass/coord.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="numeric__types_8h_source.html">cutlass/numeric_types.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="functional_8h_source.html">cutlass/functional.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="numeric__conversion_8h_source.html">cutlass/numeric_conversion.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="matrix__traits_8h_source.html">cutlass/matrix_traits.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="tensor__view_8h_source.html">cutlass/tensor_view.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="include_2cutlass_2gemm_2gemm_8h_source.html">cutlass/gemm/gemm.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="arch_2mma_8h_source.html">cutlass/arch/mma.h</a>&quot;</code><br />
</div><div class="textblock"><div class="dynheader">
Include dependency graph for tools/util/include/cutlass/util/reference/host/gemm.h:</div>
<div class="dyncontent">
<div class="center"><img src="tools_2util_2include_2cutlass_2util_2reference_2host_2gemm_8h__incl.png" border="0" usemap="#gemm_8h" alt=""/></div>
<map name="gemm_8h" id="gemm_8h">
</map>
</div>
</div><div class="textblock"><div class="dynheader">
This graph shows which files directly or indirectly include this file:</div>
<div class="dyncontent">
<div class="center"><img src="tools_2util_2include_2cutlass_2util_2reference_2host_2gemm_8h__dep__incl.png" border="0" usemap="#gemm_8hdep" alt=""/></div>
<map name="gemm_8hdep" id="gemm_8hdep">
</map>
</div>
</div>
<p><a href="tools_2util_2include_2cutlass_2util_2reference_2host_2gemm_8h_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1host_1_1Gemm.html">cutlass::reference::host::Gemm&lt; ElementA, LayoutA, ElementB, LayoutB, ElementC, LayoutC, ScalarType, ComputeType, InnerProductOp &gt;</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1host_1_1Gemm_3_01ElementA_00_01LayoutA_00_01ElementB_00_01LayoutB_193dd3a37f00deff1e5dcd7c310afb1f.html">cutlass::reference::host::Gemm&lt; ElementA, LayoutA, ElementB, LayoutB, ElementC, LayoutC, ScalarType, ComputeType, arch::OpMultiplyAdd &gt;</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Partial specialization for multiply-add.  <a href="structcutlass_1_1reference_1_1host_1_1Gemm_3_01ElementA_00_01LayoutA_00_01ElementB_00_01LayoutB_193dd3a37f00deff1e5dcd7c310afb1f.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1host_1_1Gemm_3_01ElementA_00_01LayoutA_00_01ElementB_00_01LayoutB_55729eac7dbd6bf311ea36f680e83e93.html">cutlass::reference::host::Gemm&lt; ElementA, LayoutA, ElementB, LayoutB, ElementC, LayoutC, ScalarType, ComputeType, arch::OpMultiplyAddSaturate &gt;</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Partial specialization for multiply-add-saturate.  <a href="structcutlass_1_1reference_1_1host_1_1Gemm_3_01ElementA_00_01LayoutA_00_01ElementB_00_01LayoutB_55729eac7dbd6bf311ea36f680e83e93.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1host_1_1Gemm_3_01ElementA_00_01LayoutA_00_01ElementB_00_01LayoutB_4f3f32c4b336238abfd741e87bfced46.html">cutlass::reference::host::Gemm&lt; ElementA, LayoutA, ElementB, LayoutB, ElementC, LayoutC, ScalarType, ComputeType, arch::OpXorPopc &gt;</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Partial specialization for XOR-popc.  <a href="structcutlass_1_1reference_1_1host_1_1Gemm_3_01ElementA_00_01LayoutA_00_01ElementB_00_01LayoutB_4f3f32c4b336238abfd741e87bfced46.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="namespaces"></a>
Namespaces</h2></td></tr>
<tr class="memitem:namespacecutlass"><td class="memItemLeft" align="right" valign="top"> &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespacecutlass.html">cutlass</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:namespacecutlass_1_1reference"><td class="memItemLeft" align="right" valign="top"> &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespacecutlass_1_1reference.html">cutlass::reference</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:namespacecutlass_1_1reference_1_1host"><td class="memItemLeft" align="right" valign="top"> &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespacecutlass_1_1reference_1_1host.html">cutlass::reference::host</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a300d68abd082150020768c0a94044a34"><td class="memTemplParams" colspan="2">template&lt;typename ElementA , typename LayoutA , typename ElementB , typename LayoutB , typename ElementC , typename LayoutC , typename ScalarType , typename ComputeType , typename InnerProductOp  = multiply_add&lt;ComputeType&gt;, typename ConvertOp  = NumericConverter&lt;ElementC, ScalarType&gt;&gt; </td></tr>
<tr class="memitem:a300d68abd082150020768c0a94044a34"><td class="memTemplItemLeft" align="right" valign="top">void&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespacecutlass_1_1reference_1_1host.html#a300d68abd082150020768c0a94044a34">cutlass::reference::host::compute_gemm</a> (gemm::GemmCoord problem_size, ScalarType alpha, TensorRef&lt; ElementA, LayoutA &gt; tensor_a, TensorRef&lt; ElementB, LayoutB &gt; tensor_b, ScalarType beta, TensorRef&lt; ElementC, LayoutC &gt; tensor_c, TensorRef&lt; ElementC, LayoutC &gt; tensor_d, ComputeType initial_accum)</td></tr>
<tr class="separator:a300d68abd082150020768c0a94044a34"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa75c5933390f3960666e97b37c854877"><td class="memTemplParams" colspan="2">template&lt;typename ElementA , typename LayoutA , typename ElementB , typename LayoutB , typename ElementC , typename LayoutC , typename ScalarType , typename ComputeType , typename InnerProductOp  = multiply_add&lt;ComputeType&gt;, typename ConvertOp  = NumericConverter&lt;ElementC, ScalarType&gt;&gt; </td></tr>
<tr class="memitem:aa75c5933390f3960666e97b37c854877"><td class="memTemplItemLeft" align="right" valign="top">void&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespacecutlass_1_1reference_1_1host.html#aa75c5933390f3960666e97b37c854877">cutlass::reference::host::compute_gemm</a> (gemm::GemmCoord problem_size, ScalarType alpha, TensorRef&lt; ElementA, LayoutA &gt; tensor_a, TensorRef&lt; ElementB, LayoutB &gt; tensor_b, ScalarType beta, TensorRef&lt; ElementC, LayoutC &gt; tensor_c, ComputeType initial_accum)</td></tr>
<tr class="separator:aa75c5933390f3960666e97b37c854877"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2c1067fa5de91e2f48589120f62125c2"><td class="memTemplParams" colspan="2">template&lt;typename TensorRefCollectionA , typename TensorRefCollectionB , typename TensorRefCollectionC , typename ScalarType , typename AccumulatorType &gt; </td></tr>
<tr class="memitem:a2c1067fa5de91e2f48589120f62125c2"><td class="memTemplItemLeft" align="right" valign="top">void&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespacecutlass_1_1reference_1_1host.html#a2c1067fa5de91e2f48589120f62125c2">cutlass::reference::host::BatchedGemm</a> (gemm::GemmCoord problem_size, int batch_count, ScalarType alpha, TensorRefCollectionA const &amp;tensor_a, TensorRefCollectionB const &amp;tensor_b, ScalarType beta, TensorRefCollectionC &amp;tensor_c, AccumulatorType initial_accum)</td></tr>
<tr class="memdesc:a2c1067fa5de91e2f48589120f62125c2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Computes a batch of GEMMs over a set of matrices of common dimension.  <a href="namespacecutlass_1_1reference_1_1host.html#a2c1067fa5de91e2f48589120f62125c2">More...</a><br /></td></tr>
<tr class="separator:a2c1067fa5de91e2f48589120f62125c2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1d0a79a48353119706ffa09d570c2182"><td class="memTemplParams" colspan="2">template&lt;typename TensorRefCollectionA , typename TensorRefCollectionB , typename TensorRefCollectionC , typename ScalarType , typename AccumulatorType &gt; </td></tr>
<tr class="memitem:a1d0a79a48353119706ffa09d570c2182"><td class="memTemplItemLeft" align="right" valign="top">void&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespacecutlass_1_1reference_1_1host.html#a1d0a79a48353119706ffa09d570c2182">cutlass::reference::host::BatchedGemm</a> (gemm::GemmCoord problem_size, int batch_count, ScalarType alpha, TensorRefCollectionA const &amp;tensor_a, TensorRefCollectionB const &amp;tensor_b, ScalarType beta, TensorRefCollectionC &amp;tensor_c)</td></tr>
<tr class="separator:a1d0a79a48353119706ffa09d570c2182"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
